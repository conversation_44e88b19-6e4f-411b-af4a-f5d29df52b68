{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/communication-pattern.service\";\nimport * as i2 from \"../../services/title.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../shared/collapsible-block/collapsible-block.component\";\nfunction CommunicationPatternsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.error = null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction CommunicationPatternsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"span\", 17);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1m...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 18);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1m komunika\\u010Dn\\u00ED vzorce...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Testovat detekci\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Testov\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Aktualizovat vzorce\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Aktualizuji...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"h5\", 21);\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵtext(4, \" Konfigurace detekce v\\u00FDpadk\\u016F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_11_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.testDetection());\n    });\n    i0.ɵɵelement(7, \"i\", 24);\n    i0.ɵɵtemplate(8, CommunicationPatternsComponent_div_11_span_8_Template, 2, 0, \"span\", 25);\n    i0.ɵɵtemplate(9, CommunicationPatternsComponent_div_11_span_9_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.refreshPatterns());\n    });\n    i0.ɵɵelement(11, \"i\", 27);\n    i0.ɵɵtemplate(12, CommunicationPatternsComponent_div_11_span_12_Template, 2, 0, \"span\", 25);\n    i0.ɵɵtemplate(13, CommunicationPatternsComponent_div_11_span_13_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 28)(15, \"div\", 1)(16, \"div\", 29)(17, \"strong\", 30);\n    i0.ɵɵtext(18, \"Okno anal\\u00FDzy:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 29)(21, \"strong\", 31);\n    i0.ɵɵtext(22, \"Tolerance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 29)(25, \"strong\", 32);\n    i0.ɵɵtext(26, \"Min. frekvence:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"strong\", 33);\n    i0.ɵɵtext(30, \"Vylou\\u010Dit v\\u00EDkendy: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 34);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 35)(34, \"div\", 29)(35, \"strong\", 36);\n    i0.ɵɵtext(36, \"Min. interval alert\\u016F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 29)(39, \"strong\", 37);\n    i0.ɵɵtext(40, \"Aktualizace vzorc\\u016F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 29)(43, \"strong\", 38);\n    i0.ɵɵtext(44, \"Interval kontrol:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 29)(47, \"strong\", 39);\n    i0.ɵɵtext(48, \"Min. vol\\u00E1n\\u00ED pro anal\\u00FDzu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"spinner-border\", ctx_r2.testing)(\"spinner-border-sm\", ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.refreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"spinner-border\", ctx_r2.refreshing)(\"spinner-border-sm\", ctx_r2.refreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.refreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.refreshing);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.analysisWindowDays, \" dn\\u00ED \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.toleranceMinutes, \" minut \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.minimumFrequencyPerHour, \"/hod \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.config.excludeWeekends ? \"bg-success\" : \"bg-secondary\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.excludeWeekends ? \"Ano\" : \"Ne\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.minimumAlertInterval, \" min \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.patternUpdateIntervalHours, \" hod \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.checkIntervalMinutes, \" min \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.minimumCallsForAnalysis, \" \");\n  }\n}\nfunction CommunicationPatternsComponent_app_collapsible_block_12_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 44)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 48)(11, \"span\", 34);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 48)(14, \"small\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 49)(17, \"strong\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\", 50);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 50);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 51);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\", 44);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 51)(28, \"span\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.customerAbbreviation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r15.getStatusBadgeClass(item_r17.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getStatusText(item_r17.status), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r15.getDetectionLogicTooltip(item_r17.pattern));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r15.getDetectionLogicBadgeClass(item_r17.pattern.detectionLogicType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getDetectionLogicShortText(item_r17.pattern.detectionLogicType), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r15.getActiveHoursTooltip(item_r17.pattern));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r15.formatActiveHours(item_r17.pattern));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.pattern.totalCallsAnalyzed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatNumber(item_r17.pattern.averageIntervalMinutes), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatNumber(item_r17.pattern.standardDeviation), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r15.formatNumber(item_r17.pattern.minIntervalMinutes), \" / \", ctx_r15.formatNumber(item_r17.pattern.maxIntervalMinutes), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatDate(item_r17.lastConnectionDate), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r17.pattern.hasSufficientData ? \"bg-success\" : \"bg-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r17.pattern.hasSufficientData ? \"Ano\" : \"Ne\", \" \");\n  }\n}\nfunction CommunicationPatternsComponent_app_collapsible_block_12_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 52);\n    i0.ɵɵelement(2, \"i\", 53);\n    i0.ɵɵelementStart(3, \"p\", 18);\n    i0.ɵɵtext(4, \"\\u017D\\u00E1dn\\u00E9 komunika\\u010Dn\\u00ED vzorce nebyly nalezeny\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CommunicationPatternsComponent_app_collapsible_block_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-collapsible-block\", 40)(1, \"div\", 41)(2, \"table\", 42)(3, \"thead\", 43)(4, \"tr\")(5, \"th\", 44);\n    i0.ɵɵtext(6, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 44);\n    i0.ɵɵtext(8, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 44);\n    i0.ɵɵtext(10, \"Stav\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 44);\n    i0.ɵɵtext(12, \"Typ detekce\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 44);\n    i0.ɵɵtext(14, \"Aktivn\\u00ED hodiny\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 45);\n    i0.ɵɵtext(16, \"Vol\\u00E1n\\u00ED za 30 dn\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 44);\n    i0.ɵɵtext(18, \"Pr\\u016Fm\\u011Br (min)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 44);\n    i0.ɵɵtext(20, \"Std. odchylka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 44);\n    i0.ɵɵtext(22, \"Min/max (min)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 46);\n    i0.ɵɵtext(24, \"Posl. kontakt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\", 44);\n    i0.ɵɵtext(26, \"Dostatek dat\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"tbody\");\n    i0.ɵɵtemplate(28, CommunicationPatternsComponent_app_collapsible_block_12_tr_28_Template, 30, 20, \"tr\", 47);\n    i0.ɵɵtemplate(29, CommunicationPatternsComponent_app_collapsible_block_12_tr_29_Template, 5, 0, \"tr\", 25);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"showRecordCount\", true)(\"recordCount\", ctx_r3.patterns.length)(\"defaultExpanded\", true);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.patterns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.patterns.length === 0);\n  }\n}\nfunction CommunicationPatternsComponent_div_13_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementStart(2, \"p\", 18);\n    i0.ɵɵtext(3, \"\\u017D\\u00E1dn\\u00E9 v\\u00FDpadky komunikace nebyly detekov\\u00E1ny\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CommunicationPatternsComponent_div_13_div_10_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const outage_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(outage_r21.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(outage_r21.customerAbbreviation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.formatTimeSpan(outage_r21.timeSinceLastContact));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.formatTimeSpan(outage_r21.expectedMaxInterval));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.formatDate(outage_r21.lastContactTime));\n  }\n}\nfunction CommunicationPatternsComponent_div_13_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"table\", 65)(6, \"thead\")(7, \"tr\")(8, \"th\");\n    i0.ɵɵtext(9, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"\\u010Cas od kontaktu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"O\\u010Dek\\u00E1van\\u00FD max\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Posledn\\u00ED kontakt\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, CommunicationPatternsComponent_div_13_div_10_tr_19_Template, 11, 5, \"tr\", 47);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Detekov\\u00E1no \", ctx_r19.testResults.length, \" v\\u00FDpadk\\u016F komunikace \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.testResults);\n  }\n}\nfunction CommunicationPatternsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56)(3, \"div\", 57)(4, \"h5\", 58);\n    i0.ɵɵelement(5, \"i\", 59);\n    i0.ɵɵtext(6, \" V\\u00FDsledky testov\\u00E1n\\u00ED detekce v\\u00FDpadk\\u016F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_13_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.closeTestResults());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 60);\n    i0.ɵɵtemplate(9, CommunicationPatternsComponent_div_13_div_9_Template, 4, 0, \"div\", 7);\n    i0.ɵɵtemplate(10, CommunicationPatternsComponent_div_13_div_10_Template, 20, 2, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 61)(12, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_13_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.closeTestResults());\n    });\n    i0.ɵɵtext(13, \"Zav\\u0159\\u00EDt\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r4.showTestResults ? \"block\" : \"none\");\n    i0.ɵɵclassProp(\"show\", ctx_r4.showTestResults);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.testResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.testResults.length > 0);\n  }\n}\nfunction CommunicationPatternsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 66);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"show\", ctx_r5.showTestResults);\n  }\n}\nexport let CommunicationPatternsComponent = /*#__PURE__*/(() => {\n  class CommunicationPatternsComponent {\n    constructor(communicationPatternService, titleService) {\n      this.communicationPatternService = communicationPatternService;\n      this.titleService = titleService;\n      this.patterns = [];\n      this.config = null;\n      this.testResults = [];\n      this.loading = false;\n      this.refreshing = false;\n      this.testing = false;\n      this.error = null;\n      this.showTestResults = false;\n    }\n    ngOnInit() {\n      this.titleService.setTitle('Komunikační vzorce');\n      this.loadData();\n    }\n    ngAfterViewInit() {\n      // Inicializace popoverů s zpožděním, aby se zajistilo, že DOM je plně načten\n      setTimeout(() => {\n        this.initPopovers();\n      }, 500);\n    }\n    ngOnDestroy() {\n      // Zničení všech popoverů\n      this.destroyPopovers();\n    }\n    loadData() {\n      this.loading = true;\n      this.error = null;\n      // Načtení vzorců\n      this.communicationPatternService.getAllPatterns().subscribe({\n        next: response => {\n          if (response.success) {\n            this.patterns = response.data;\n          } else {\n            this.error = response.message || 'Chyba při načítání vzorců';\n          }\n        },\n        error: error => {\n          this.error = 'Chyba při načítání vzorců: ' + (error.message || error);\n          console.error('Chyba při načítání vzorců:', error);\n        }\n      });\n      // Načtení konfigurace\n      this.communicationPatternService.getConfiguration().subscribe({\n        next: response => {\n          if (response.success) {\n            this.config = response.data;\n          }\n          this.loading = false;\n          // Reinicializace popoverů po načtení dat\n          setTimeout(() => {\n            this.initPopovers();\n          }, 100);\n        },\n        error: error => {\n          console.error('Chyba při načítání konfigurace:', error);\n          this.loading = false;\n        }\n      });\n    }\n    refreshPatterns() {\n      this.refreshing = true;\n      this.error = null;\n      this.communicationPatternService.refreshPatterns().subscribe({\n        next: response => {\n          if (response.success) {\n            // Po úspěšné aktualizaci znovu načti data\n            this.loadData();\n          } else {\n            this.error = response.message || 'Chyba při aktualizaci vzorců';\n          }\n          this.refreshing = false;\n        },\n        error: error => {\n          this.error = 'Chyba při aktualizaci vzorců: ' + (error.message || error);\n          this.refreshing = false;\n          console.error('Chyba při aktualizaci vzorců:', error);\n        }\n      });\n    }\n    testDetection() {\n      this.testing = true;\n      this.error = null;\n      this.testResults = [];\n      this.communicationPatternService.testDetection().subscribe({\n        next: response => {\n          if (response.success) {\n            this.testResults = response.data;\n            this.showTestResults = true;\n          } else {\n            this.error = response.message || 'Chyba při testování detekce';\n          }\n          this.testing = false;\n        },\n        error: error => {\n          this.error = 'Chyba při testování detekce: ' + (error.message || error);\n          this.testing = false;\n          console.error('Chyba při testování detekce:', error);\n        }\n      });\n    }\n    getStatusBadgeClass(status) {\n      switch (status.toLowerCase()) {\n        case 'active':\n          return 'badge bg-success';\n        case 'inactive':\n          return 'badge bg-secondary';\n        case 'blocked':\n          return 'badge bg-danger';\n        default:\n          return 'badge bg-secondary';\n      }\n    }\n    getStatusText(status) {\n      switch (status.toLowerCase()) {\n        case 'active':\n          return 'Aktivní';\n        case 'inactive':\n          return 'Neaktivní';\n        case 'blocked':\n          return 'Blokovaná';\n        default:\n          return status;\n      }\n    }\n    formatTimeSpan(timeSpan) {\n      // Převod TimeSpan stringu na čitelný formát\n      const parts = timeSpan.split(':');\n      if (parts.length >= 2) {\n        const hours = parseInt(parts[0]);\n        const minutes = parseInt(parts[1]);\n        if (hours > 0) {\n          return `${hours}h ${minutes}m`;\n        } else {\n          return `${minutes}m`;\n        }\n      }\n      return timeSpan;\n    }\n    formatDate(date) {\n      if (!date) return 'Nikdy';\n      const d = typeof date === 'string' ? new Date(date) : date;\n      return d.toLocaleString('cs-CZ');\n    }\n    formatNumber(num) {\n      return num.toFixed(1);\n    }\n    formatActiveHours(pattern) {\n      if (!pattern.activeHours) {\n        return 'Neanalyzováno';\n      }\n      const ah = pattern.activeHours;\n      const confidence = (ah.confidence * 100).toFixed(0);\n      return `${ah.startHour}:00-${ah.endHour}:00 (${confidence}% spolehlivost)`;\n    }\n    getActiveHoursTooltip(pattern) {\n      if (!pattern.activeHours) {\n        return 'Aktivní hodiny nebyly analyzovány - nedostatek historických dat';\n      }\n      const ah = pattern.activeHours;\n      const days = ah.activeDays?.map(day => this.getDayName(day)).join(', ') || 'Žádné dny';\n      return `Aktivní dny: ${days}\\nSpolehlivost: ${(ah.confidence * 100).toFixed(1)}%`;\n    }\n    /**\r\n     * Převod čísla dne na český název\r\n     */\n    getDayName(dayNumber) {\n      const dayNames = ['Ne', 'Po', 'Út', 'St', 'Čt', 'Pá', 'So'];\n      return dayNames[dayNumber] || dayNumber.toString();\n    }\n    getDetectionLogicTooltip(pattern) {\n      return pattern.detectionLogicDescription || 'Informace o detekční logice nejsou k dispozici';\n    }\n    getDetectionLogicShortText(detectionType) {\n      switch (detectionType) {\n        case 'Nastavené pracovní hodiny':\n          return 'Nastavené';\n        case 'Historická analýza':\n          return 'Historická';\n        case 'Nepřetržitý monitoring':\n          return 'Nepřetržitý';\n        default:\n          return 'Neznámý';\n      }\n    }\n    getDetectionLogicBadgeClass(detectionType) {\n      switch (detectionType) {\n        case 'Nastavené pracovní hodiny':\n          return 'bg-success';\n        case 'Historická analýza':\n          return 'bg-info';\n        case 'Nepřetržitý monitoring':\n          return 'bg-warning';\n        default:\n          return 'bg-secondary';\n      }\n    }\n    closeTestResults() {\n      this.showTestResults = false;\n      this.testResults = [];\n    }\n    /**\r\n     * Inicializace popoverů pro nápovědu\r\n     */\n    initPopovers() {\n      // Definice obsahu nápověd\n      const helpContent = {\n        'analysis-window': 'Počet dní zpětně, ze kterých se analyzují komunikační vzorce instancí. ' + 'Delší období poskytuje přesnější analýzu, ale vyžaduje více historických dat.',\n        'tolerance': 'Tolerance v minutách pro detekci výpadků komunikace. ' + 'Pokud instance nekomunikuje déle než očekávaný interval + tolerance, je detekován výpadek.',\n        'min-frequency': 'Minimální frekvence komunikace za hodinu potřebná pro analýzu vzorce. ' + 'Instance s nižší frekvencí nebudou analyzovány.',\n        'exclude-weekends': 'Určuje, zda se mají víkendy vyloučit z analýzy komunikačních vzorců. ' + 'Užitečné pro instance, které nekomunikují o víkendech.',\n        'min-alert-interval': 'Minimální interval mezi alerty pro stejnou instanci v minutách. ' + 'Zabraňuje spamování alertů při opakovaných výpadcích.',\n        'pattern-update-interval': 'Interval v hodinách, jak často se aktualizují komunikační vzorce. ' + 'Kratší interval znamená aktuálnější vzorce, ale vyšší zátěž systému.',\n        'check-interval': 'Interval v minutách, jak často se kontrolují výpadky komunikace. ' + 'Kratší interval znamená rychlejší detekci výpadků.',\n        'min-calls-analysis': 'Minimální počet volání potřebný pro analýzu komunikačního vzorce instance. ' + 'Instance s menším počtem volání nebudou analyzovány.'\n      };\n      // Nejprve zrušíme všechny existující popovery\n      this.destroyPopovers();\n      // Inicializace popoverů pomocí Bootstrap API\n      document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n        const helpType = el.getAttribute('data-help-type');\n        if (helpType && helpType in helpContent) {\n          try {\n            new bootstrap.Popover(el, {\n              content: helpContent[helpType],\n              html: true,\n              trigger: 'hover focus',\n              placement: 'top',\n              container: 'body',\n              sanitize: false\n            });\n          } catch (error) {\n            console.error('Chyba při inicializaci popoveru:', error);\n          }\n        } else if (helpType) {\n          console.warn('Nápověda nenalezena pro typ:', helpType);\n        }\n      });\n    }\n    /**\r\n     * Zničení všech popoverů\r\n     */\n    destroyPopovers() {\n      document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n        const popover = bootstrap.Popover.getInstance(el);\n        if (popover) {\n          popover.dispose();\n        }\n      });\n    }\n    static {\n      this.ɵfac = function CommunicationPatternsComponent_Factory(t) {\n        return new (t || CommunicationPatternsComponent)(i0.ɵɵdirectiveInject(i1.CommunicationPatternService), i0.ɵɵdirectiveInject(i2.TitleService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CommunicationPatternsComponent,\n        selectors: [[\"app-communication-patterns\"]],\n        decls: 15,\n        vars: 6,\n        consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [1, \"mb-4\"], [1, \"bi\", \"bi-diagram-3-fill\", \"me-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [\"title\", \"Komunika\\u010Dn\\u00ED vzorce\", \"icon\", \"bi-table\", \"storageKey\", \"collapsible_block_communication_patterns\", \"marginTop\", \"\", \"marginBottom\", \"mb-4\", 3, \"showRecordCount\", \"recordCount\", \"defaultExpanded\", 4, \"ngIf\"], [\"class\", \"modal fade\", \"tabindex\", \"-1\", \"role\", \"dialog\", 3, \"show\", \"display\", 4, \"ngIf\"], [\"class\", \"modal-backdrop fade\", 3, \"show\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-2\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-light\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"bi\", \"bi-gear-fill\", \"me-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-play-circle\"], [4, \"ngIf\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-arrow-clockwise\"], [1, \"card-body\"], [1, \"col-md-3\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"analysis-window\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"tolerance\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"min-frequency\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"exclude-weekends\", 2, \"cursor\", \"help\"], [1, \"badge\"], [1, \"row\", \"mt-2\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"min-alert-interval\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"pattern-update-interval\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"check-interval\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"min-calls-analysis\", 2, \"cursor\", \"help\"], [\"title\", \"Komunika\\u010Dn\\u00ED vzorce\", \"icon\", \"bi-table\", \"storageKey\", \"collapsible_block_communication_patterns\", \"marginTop\", \"\", \"marginBottom\", \"mb-4\", 3, \"showRecordCount\", \"recordCount\", \"defaultExpanded\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [1, \"table-light\"], [1, \"text-nowrap\"], [1, \"text-nowrap\", 2, \"width\", \"120px\"], [1, \"text-nowrap\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [3, \"title\"], [1, \"text-center\", \"text-nowrap\"], [1, \"text-end\"], [1, \"text-center\"], [\"colspan\", \"11\", 1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-inbox\", \"fs-1\"], [\"tabindex\", \"-1\", \"role\", \"dialog\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [1, \"bi\", \"bi-bug-fill\", \"me-2\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"bi\", \"bi-check-circle-fill\", \"text-success\", \"fs-1\"], [1, \"alert\", \"alert-warning\"], [1, \"table\", \"table-sm\"], [1, \"modal-backdrop\", \"fade\"]],\n        template: function CommunicationPatternsComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n            i0.ɵɵelement(5, \"i\", 4);\n            i0.ɵɵtext(6, \" Komunika\\u010Dn\\u00ED vzorce instanc\\u00ED \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"p\", 5);\n            i0.ɵɵtext(8, \" Anal\\u00FDza historick\\u00E9ho chov\\u00E1n\\u00ED komunikace instanc\\u00ED pro inteligentn\\u00ED detekci v\\u00FDpadk\\u016F. Instance bez nastaven\\u00FDch pracovn\\u00EDch hodin pou\\u017E\\u00EDvaj\\u00ED automaticky analyzovan\\u00E9 aktivn\\u00ED hodiny. \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(9, CommunicationPatternsComponent_div_9_Template, 4, 1, \"div\", 6);\n            i0.ɵɵtemplate(10, CommunicationPatternsComponent_div_10_Template, 6, 0, \"div\", 7);\n            i0.ɵɵtemplate(11, CommunicationPatternsComponent_div_11_Template, 50, 24, \"div\", 8);\n            i0.ɵɵtemplate(12, CommunicationPatternsComponent_app_collapsible_block_12_Template, 30, 5, \"app-collapsible-block\", 9);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵtemplate(13, CommunicationPatternsComponent_div_13_Template, 14, 6, \"div\", 10);\n            i0.ɵɵtemplate(14, CommunicationPatternsComponent_div_14_Template, 1, 2, \"div\", 11);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.config && !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showTestResults);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.showTestResults);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i4.CollapsibleBlockComponent],\n        styles: [\".card-header[_ngcontent-%COMP%]{background-color:#f8f9fa!important;border-bottom:1px solid #dee2e6}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:600;font-size:.875rem;letter-spacing:.5px;color:#495057}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{vertical-align:middle}.badge[_ngcontent-%COMP%]{font-size:.75rem}.spinner-border-sm[_ngcontent-%COMP%]{width:1rem;height:1rem}.modal-backdrop[_ngcontent-%COMP%]{background-color:#00000080}.text-end[_ngcontent-%COMP%]{text-align:right}.text-center[_ngcontent-%COMP%]{text-align:center}[data-bs-theme=dark][_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background-color:#343a40!important;border-bottom:1px solid #495057}[data-bs-theme=dark][_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{color:#adb5bd}[data-bs-theme=dark][_ngcontent-%COMP%]   .modal-backdrop[_ngcontent-%COMP%]{background-color:#ffffff1a}\"]\n      });\n    }\n  }\n  return CommunicationPatternsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}