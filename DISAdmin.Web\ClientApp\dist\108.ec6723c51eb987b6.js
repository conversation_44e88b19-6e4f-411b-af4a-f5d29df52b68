"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[108],{2108:(ct,p,u)=>{u.r(p),u.d(p,{AdminModule:()=>st});var l=u(6895),g=u(6123),s=u(433),t=u(1571),m=u(1612),v=u(7185),f=u(7556),d=u(6026),h=u(18);function _(i,o){1&i&&(t.TgZ(0,"div",15),t._UZ(1,"i",16),t.TgZ(2,"strong"),t._uU(3,"P\u0159\xedstup odep\u0159en!"),t.qZA(),t._uU(4," Pro p\u0159\xedstup k t\xe9to funkci mus\xedte b\xfdt administr\xe1tor. "),t.qZA())}function Z(i,o){1&i&&(t.TgZ(0,"div",3)(1,"div",4)(2,"h2"),t._uU(3,"Spr\xe1va serverov\xe9ho certifik\xe1tu"),t.qZA(),t.TgZ(4,"p",17),t._uU(5," Serverov\xfd certifik\xe1t je pou\u017e\xedv\xe1n pro zabezpe\u010den\xed HTTPS komunikace mezi klienty a DISApi serverem. "),t.qZA()()())}function b(i,o){1&i&&(t.TgZ(0,"div",18)(1,"div",19)(2,"span",20),t._uU(3,"Na\u010d\xedt\xe1n\xed..."),t.qZA()()())}function T(i,o){1&i&&(t.TgZ(0,"div",21),t._UZ(1,"i",16),t.TgZ(2,"strong"),t._uU(3,"Serverov\xfd certifik\xe1t nen\xed nakonfigurov\xe1n nebo neexistuje."),t.qZA(),t._uU(4," Vygenerujte nov\xfd certifik\xe1t pomoc\xed formul\xe1\u0159e n\xed\u017ee. "),t.qZA())}function x(i,o){1&i&&(t.TgZ(0,"span",30),t._uU(1,"Platn\xfd"),t.qZA())}function k(i,o){1&i&&(t.TgZ(0,"span",31),t._uU(1,"Neplatn\xfd"),t.qZA())}function A(i,o){if(1&i&&(t.TgZ(0,"div",22)(1,"div",23),t._uU(2,"Cesta k souboru:"),t.qZA(),t.TgZ(3,"div",24),t._uU(4),t.qZA()()),2&i){const e=t.oxw(2);t.xp6(4),t.Oqu(e.certificateInfo.filePath)}}function y(i,o){if(1&i&&(t.TgZ(0,"div")(1,"div",22)(2,"div",23),t._uU(3,"Subject:"),t.qZA(),t.TgZ(4,"div",24),t._uU(5),t.qZA()(),t.TgZ(6,"div",22)(7,"div",23),t._uU(8,"Vydavatel:"),t.qZA(),t.TgZ(9,"div",24),t._uU(10),t.qZA()(),t.TgZ(11,"div",22)(12,"div",23),t._uU(13,"Thumbprint:"),t.qZA(),t.TgZ(14,"div",24),t._uU(15),t.qZA()(),t.TgZ(16,"div",22)(17,"div",23),t._uU(18,"S\xe9riov\xe9 \u010d\xedslo:"),t.qZA(),t.TgZ(19,"div",24),t._uU(20),t.qZA()(),t.TgZ(21,"div",22)(22,"div",23),t._uU(23,"Platnost od:"),t.qZA(),t.TgZ(24,"div",24),t._uU(25),t.ALo(26,"date"),t.qZA()(),t.TgZ(27,"div",22)(28,"div",23),t._uU(29,"Platnost do:"),t.qZA(),t.TgZ(30,"div",25),t._uU(31),t.ALo(32,"localDate"),t.TgZ(33,"span",26),t._uU(34),t.qZA()()(),t.TgZ(35,"div",22)(36,"div",23),t._uU(37,"Obsahuje priv\xe1tn\xed kl\xed\u010d:"),t.qZA(),t.TgZ(38,"div",24),t._uU(39),t.qZA()(),t.TgZ(40,"div",22)(41,"div",23),t._uU(42,"Stav:"),t.qZA(),t.TgZ(43,"div",24),t.YNc(44,x,2,0,"span",27),t.YNc(45,k,2,0,"span",28),t.qZA()(),t.TgZ(46,"div",22)(47,"div",23),t._uU(48,"Typ \xfalo\u017ei\u0161t\u011b:"),t.qZA(),t.TgZ(49,"div",24),t._uU(50),t.qZA()(),t.TgZ(51,"div",22)(52,"div",23),t._uU(53,"Um\xedst\u011bn\xed:"),t.qZA(),t.TgZ(54,"div",24),t._uU(55),t.qZA()(),t.YNc(56,A,5,1,"div",29),t.qZA()),2&i){const e=t.oxw();t.xp6(5),t.Oqu(e.certificateInfo.subject),t.xp6(5),t.Oqu(e.certificateInfo.issuer),t.xp6(5),t.Oqu(e.certificateInfo.thumbprint),t.xp6(5),t.Oqu(e.certificateInfo.serialNumber),t.xp6(5),t.Oqu(t.xi3(26,14,e.certificateInfo.notBefore,"dd.MM.yyyy HH:mm:ss")),t.xp6(5),t.Q6J("ngClass",e.getExpirationClass()),t.xp6(1),t.hij(" ",t.xi3(32,17,e.certificateInfo.notAfter,"dd.MM.yyyy HH:mm:ss")," "),t.xp6(3),t.hij("(",e.getDaysToExpiration()," dn\xed)"),t.xp6(5),t.Oqu(e.certificateInfo.hasPrivateKey?"Ano":"Ne"),t.xp6(5),t.Q6J("ngIf",e.certificateInfo.isValid),t.xp6(1),t.Q6J("ngIf",!e.certificateInfo.isValid),t.xp6(5),t.Oqu(e.certificateInfo.storageType),t.xp6(5),t.Oqu(e.certificateInfo.storageLocation),t.xp6(1),t.Q6J("ngIf","File"===e.certificateInfo.storageType&&e.certificateInfo.filePath)}}function C(i,o){1&i&&(t.TgZ(0,"div",56),t._uU(1," Common Name je povinn\xfd. "),t.qZA())}function U(i,o){1&i&&(t.TgZ(0,"div",56),t._uU(1," Platnost mus\xed b\xfdt mezi 1 a 3650 dny. "),t.qZA())}function E(i,o){1&i&&(t.TgZ(0,"div",57)(1,"div",41),t._UZ(2,"input",58),t.TgZ(3,"label",59),t._uU(4,"CurrentUser (aktu\xe1ln\xed u\u017eivatel)"),t.qZA(),t.TgZ(5,"div",36),t._uU(6,"Certifik\xe1t bude dostupn\xfd pouze pro aktu\xe1ln\xedho u\u017eivatele. Nevy\u017eaduje administr\xe1torsk\xe1 pr\xe1va."),t.qZA()(),t.TgZ(7,"div",44),t._UZ(8,"input",60),t.TgZ(9,"label",61),t._uU(10,"LocalMachine (v\u0161ichni u\u017eivatel\xe9)"),t.qZA(),t.TgZ(11,"div",36),t._uU(12,"Certifik\xe1t bude dostupn\xfd pro v\u0161echny u\u017eivatele. Vy\u017eaduje administr\xe1torsk\xe1 pr\xe1va."),t.qZA()()())}function q(i,o){1&i&&t._UZ(0,"span",62)}function D(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",6)(4,"h5",7),t._uU(5,"Generov\xe1n\xed nov\xe9ho certifik\xe1tu"),t.qZA()(),t.TgZ(6,"div",8)(7,"form",32),t.NdJ("ngSubmit",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.generateCertificate())}),t.TgZ(8,"div",33)(9,"label",34),t._uU(10,"Common Name (CN)"),t.qZA(),t._UZ(11,"input",35),t.TgZ(12,"div",36),t._uU(13,"Dom\xe9nov\xe9 jm\xe9no nebo n\xe1zev serveru, pro kter\xfd bude certifik\xe1t vyd\xe1n. Zadejte URL adresu DISApi, na kterou p\u0159istupuje DIS aplikace (nap\u0159. disapi.disadmin.cz). Tato hodnota mus\xed p\u0159esn\u011b odpov\xeddat dom\xe9n\u011b, kterou DIS aplikace pou\u017e\xedv\xe1 pro p\u0159ipojen\xed k DISApi."),t.qZA(),t.YNc(14,C,2,0,"div",37),t.qZA(),t.TgZ(15,"div",33)(16,"label",38),t._uU(17,"Platnost (dny)"),t.qZA(),t._UZ(18,"input",39),t.TgZ(19,"div",36),t._uU(20,"Po\u010det dn\xed, po kter\xe9 bude certifik\xe1t platn\xfd. Doporu\u010deno: 365 dn\xed (1 rok)."),t.qZA(),t.YNc(21,U,2,0,"div",37),t.qZA(),t.TgZ(22,"div",33)(23,"label",40),t._uU(24,"\xdalo\u017ei\u0161t\u011b certifik\xe1tu"),t.qZA(),t.TgZ(25,"div",41),t._UZ(26,"input",42),t.TgZ(27,"label",43),t._uU(28,"Ulo\u017eit do datab\xe1ze (doporu\u010deno)"),t.qZA(),t.TgZ(29,"div",36),t._uU(30,"Certifik\xe1t bude ulo\u017een p\u0159\xedmo v datab\xe1zi. Toto je nejbezpe\u010dn\u011bj\u0161\xed a nejspolehliv\u011bj\u0161\xed zp\u016fsob."),t.qZA()(),t.TgZ(31,"div",44),t._UZ(32,"input",45),t.TgZ(33,"label",46),t._uU(34,"Ulo\u017eit jako soubor"),t.qZA(),t.TgZ(35,"div",36),t._uU(36,"Certifik\xe1t bude ulo\u017een jako soubor .pfx v adres\xe1\u0159i aplikace."),t.qZA()(),t.TgZ(37,"div",44),t._UZ(38,"input",47),t.TgZ(39,"label",48),t._uU(40,"Ulo\u017eit do \xfalo\u017ei\u0161t\u011b certifik\xe1t\u016f Windows"),t.qZA(),t.TgZ(41,"div",36),t._uU(42,"Certifik\xe1t bude ulo\u017een do \xfalo\u017ei\u0161t\u011b certifik\xe1t\u016f Windows."),t.qZA()(),t.YNc(43,E,13,0,"div",49),t.qZA(),t.TgZ(44,"div",50),t._UZ(45,"input",51),t.TgZ(46,"label",52),t._uU(47,"Aktualizovat konfiguraci"),t.qZA(),t.TgZ(48,"div",36),t._uU(49,"Konfigurace aplikace bude aktualizov\xe1na s nov\xfdm certifik\xe1tem."),t.qZA()(),t.TgZ(50,"div",21),t._UZ(51,"i",16),t.TgZ(52,"strong"),t._uU(53,"Upozorn\u011bn\xed:"),t.qZA(),t._uU(54," Generov\xe1n\xed nov\xe9ho serverov\xe9ho certifik\xe1tu m\u016f\u017ee vy\u017eadovat restart aplikace, aby se zm\u011bny projevily. "),t.qZA(),t.TgZ(55,"div",53),t._UZ(56,"i",16),t.TgZ(57,"strong"),t._uU(58,"D\u016eLE\u017dIT\xc9 VAROV\xc1N\xcd:"),t.qZA(),t._uU(59," Zm\u011bna serverov\xe9ho certifik\xe1tu zp\u016fsob\xed, \u017ee v\u0161echny existuj\xedc\xed klientsk\xe9 certifik\xe1ty p\u0159estanou fungovat! Budete muset p\u0159egenerovat v\u0161echny klientsk\xe9 certifik\xe1ty pro instance DIS. "),t.qZA(),t.TgZ(60,"button",54),t.YNc(61,q,1,0,"span",55),t._uU(62," Generovat certifik\xe1t "),t.qZA()()()()()()}if(2&i){const e=t.oxw();let n,a,r;t.xp6(7),t.Q6J("formGroup",e.generationForm),t.xp6(7),t.Q6J("ngIf",(null==(n=e.generationForm.get("commonName"))?null:n.invalid)&&(null==(n=e.generationForm.get("commonName"))?null:n.touched)),t.xp6(7),t.Q6J("ngIf",(null==(a=e.generationForm.get("validityDays"))?null:a.invalid)&&(null==(a=e.generationForm.get("validityDays"))?null:a.touched)),t.xp6(5),t.Q6J("value",2),t.xp6(6),t.Q6J("value",0),t.xp6(6),t.Q6J("value",1),t.xp6(5),t.Q6J("ngIf",1===(null==(r=e.generationForm.get("storageType"))?null:r.value)),t.xp6(17),t.Q6J("disabled",e.generationForm.invalid||e.generating),t.xp6(1),t.Q6J("ngIf",e.generating)}}function I(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",3)(1,"div",4)(2,"div",5)(3,"div",63)(4,"h5",7),t._uU(5,"Certifik\xe1t byl \xfasp\u011b\u0161n\u011b vygenerov\xe1n"),t.qZA()(),t.TgZ(6,"div",8)(7,"div",64),t._uU(8),t.qZA(),t.TgZ(9,"div",22)(10,"div",23),t._uU(11,"Subject:"),t.qZA(),t.TgZ(12,"div",24),t._uU(13),t.qZA()(),t.TgZ(14,"div",22)(15,"div",23),t._uU(16,"Vydavatel:"),t.qZA(),t.TgZ(17,"div",24),t._uU(18),t.qZA()(),t.TgZ(19,"div",22)(20,"div",23),t._uU(21,"Thumbprint:"),t.qZA(),t.TgZ(22,"div",24),t._uU(23),t.qZA()(),t.TgZ(24,"div",22)(25,"div",23),t._uU(26,"Platnost od:"),t.qZA(),t.TgZ(27,"div",24),t._uU(28),t.ALo(29,"date"),t.qZA()(),t.TgZ(30,"div",22)(31,"div",23),t._uU(32,"Platnost do:"),t.qZA(),t.TgZ(33,"div",24),t._uU(34),t.ALo(35,"date"),t.qZA()(),t.TgZ(36,"div",22)(37,"div",23),t._uU(38,"Cesta k souboru:"),t.qZA(),t.TgZ(39,"div",24),t._uU(40),t.qZA()(),t.TgZ(41,"div",22)(42,"div",23),t._uU(43,"Heslo:"),t.qZA(),t.TgZ(44,"div",24)(45,"div",65),t._UZ(46,"input",66),t.TgZ(47,"button",67),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.togglePasswordVisibility())}),t._UZ(48,"i",68),t.qZA()()()(),t.TgZ(49,"div",69),t._UZ(50,"i",16),t.TgZ(51,"strong"),t._uU(52,"D\u016fle\u017eit\xe9:"),t.qZA(),t._uU(53," Ulo\u017ete si heslo k certifik\xe1tu na bezpe\u010dn\xe9 m\xedsto. Po zav\u0159en\xed tohoto okna ji\u017e nebude mo\u017en\xe9 heslo zobrazit. "),t.qZA()(),t.TgZ(54,"div",12)(55,"button",70),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.resetGeneratedCertificate())}),t._UZ(56,"i",71),t._uU(57," Zp\u011bt "),t.qZA()()()()()}if(2&i){const e=t.oxw();t.xp6(8),t.hij(" ",e.generatedCertificate.message," "),t.xp6(5),t.Oqu(e.generatedCertificate.subject),t.xp6(5),t.Oqu(e.generatedCertificate.issuer),t.xp6(5),t.Oqu(e.generatedCertificate.thumbprint),t.xp6(5),t.Oqu(t.xi3(29,10,e.generatedCertificate.notBefore,"dd.MM.yyyy HH:mm:ss")),t.xp6(6),t.Oqu(t.xi3(35,13,e.generatedCertificate.notAfter,"dd.MM.yyyy HH:mm:ss")),t.xp6(6),t.Oqu(e.generatedCertificate.filePath),t.xp6(6),t.Q6J("type",e.showPassword?"text":"password")("value",e.generatedCertificate.password),t.xp6(2),t.Q6J("ngClass",e.showPassword?"bi-eye-slash":"bi-eye")}}let P=(()=>{const o=class{constructor(n,a,r,c,ut){this.certificateService=n,this.formBuilder=a,this.toastr=r,this.authService=c,this.titleService=ut,this.certificateInfo=null,this.loading=!1,this.generating=!1,this.generatedCertificate=null,this.showPassword=!1,this.isAdmin=!1,this.generationForm=this.formBuilder.group({commonName:["",[s.kI.required]],validityDays:[365,[s.kI.required,s.kI.min(1),s.kI.max(3650)]],saveToStore:[!1],storeLocation:["CurrentUser"],updateConfiguration:[!0,[s.kI.required]],storageType:[2,[s.kI.required]]})}ngOnInit(){this.titleService.setTitle("Serverov\xfd certifik\xe1t"),this.isAdmin=this.authService.isAdmin(),console.log("Je u\u017eivatel admin?",this.isAdmin),this.isAdmin?this.loadCertificateInfo():this.toastr.error("Pro p\u0159\xedstup k t\xe9to funkci mus\xedte b\xfdt administr\xe1tor","P\u0159\xedstup odep\u0159en")}loadCertificateInfo(){this.loading=!0,this.certificateService.getServerCertificateInfo().subscribe({next:n=>{this.certificateInfo=n,this.loading=!1},error:n=>{this.toastr.error("Nepoda\u0159ilo se na\u010d\xedst informace o certifik\xe1tu","Chyba"),console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed informac\xed o certifik\xe1tu",n),this.loading=!1}})}generateCertificate(){this.generationForm.invalid?this.toastr.error("Formul\xe1\u0159 obsahuje chyby","Chyba"):(this.generating=!0,this.certificateService.generateServerCertificate(this.generationForm.value).subscribe({next:a=>{this.generatedCertificate=a,this.toastr.success("Certifik\xe1t byl \xfasp\u011b\u0161n\u011b vygenerov\xe1n","\xdasp\u011bch"),this.generating=!1,this.loadCertificateInfo()},error:a=>{this.toastr.error("Nepoda\u0159ilo se vygenerovat certifik\xe1t","Chyba"),console.error("Chyba p\u0159i generov\xe1n\xed certifik\xe1tu",a),this.generating=!1}}))}togglePasswordVisibility(){this.showPassword=!this.showPassword}getDaysToExpiration(){if(!this.certificateInfo)return 0;const n=new Date,r=new Date(this.certificateInfo.notAfter).getTime()-n.getTime();return Math.ceil(r/864e5)}getExpirationClass(){const n=this.getDaysToExpiration();return n<=0?"text-danger":n<=30?"text-warning":"text-success"}resetGeneratedCertificate(){this.generatedCertificate=null}};let i=o;return o.\u0275fac=function(a){return new(a||o)(t.Y36(m.s),t.Y36(s.qu),t.Y36(v._W),t.Y36(f.e),t.Y36(d.y))},o.\u0275cmp=t.Xpm({type:o,selectors:[["app-server-certificate"]],decls:19,vars:8,consts:[[1,"container"],["class","alert alert-danger mb-4",4,"ngIf"],["class","row mb-4",4,"ngIf"],[1,"row","mb-4"],[1,"col"],[1,"card"],[1,"card-header"],[1,"mb-0"],[1,"card-body"],["class","text-center",4,"ngIf"],["class","alert alert-warning",4,"ngIf"],[4,"ngIf"],[1,"card-footer"],[1,"btn","btn-primary",3,"disabled","click"],[1,"bi","bi-arrow-clockwise","me-1"],[1,"alert","alert-danger","mb-4"],[1,"bi","bi-exclamation-triangle-fill","me-2"],[1,"text-muted"],[1,"text-center"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"alert","alert-warning"],[1,"row","mb-3"],[1,"col-md-3","fw-bold"],[1,"col-md-9"],[1,"col-md-9",3,"ngClass"],[1,"ms-2"],["class","badge bg-success",4,"ngIf"],["class","badge bg-danger",4,"ngIf"],["class","row mb-3",4,"ngIf"],[1,"badge","bg-success"],[1,"badge","bg-danger"],[3,"formGroup","ngSubmit"],[1,"mb-3"],["for","commonName",1,"form-label"],["type","text","id","commonName","formControlName","commonName","placeholder","nap\u0159. disapi.disadmin.cz",1,"form-control"],[1,"form-text"],["class","text-danger",4,"ngIf"],["for","validityDays",1,"form-label"],["type","number","id","validityDays","formControlName","validityDays","min","1","max","3650",1,"form-control"],[1,"form-label"],[1,"form-check"],["type","radio","id","storeTypeDatabase","name","storageType","formControlName","storageType",1,"form-check-input",3,"value"],["for","storeTypeDatabase",1,"form-check-label"],[1,"form-check","mt-2"],["type","radio","id","storeTypeFile","name","storageType","formControlName","storageType",1,"form-check-input",3,"value"],["for","storeTypeFile",1,"form-check-label"],["type","radio","id","storeTypeCertStore","name","storageType","formControlName","storageType",1,"form-check-input",3,"value"],["for","storeTypeCertStore",1,"form-check-label"],["class","mt-2",4,"ngIf"],[1,"mb-3","form-check"],["type","checkbox","id","updateConfiguration","formControlName","updateConfiguration",1,"form-check-input"],["for","updateConfiguration",1,"form-check-label"],[1,"alert","alert-danger"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","spinner-border spinner-border-sm me-1","role","status","aria-hidden","true",4,"ngIf"],[1,"text-danger"],[1,"mt-2"],["type","radio","id","storeLocationCurrentUser","name","storeLocation","value","CurrentUser","formControlName","storeLocation",1,"form-check-input"],["for","storeLocationCurrentUser",1,"form-check-label"],["type","radio","id","storeLocationLocalMachine","name","storeLocation","value","LocalMachine","formControlName","storeLocation",1,"form-check-input"],["for","storeLocationLocalMachine",1,"form-check-label"],["role","status","aria-hidden","true",1,"spinner-border","spinner-border-sm","me-1"],[1,"card-header","bg-success","text-white"],[1,"alert","alert-success"],[1,"input-group"],["readonly","",1,"form-control",3,"type","value"],["type","button",1,"btn","btn-outline-secondary",3,"click"],[1,"bi",3,"ngClass"],[1,"alert","alert-warning","mt-3"],[1,"btn","btn-primary",3,"click"],[1,"bi","bi-arrow-left","me-1"]],template:function(a,r){1&a&&(t.TgZ(0,"div",0),t.YNc(1,_,5,0,"div",1),t.YNc(2,Z,6,0,"div",2),t.TgZ(3,"div",3)(4,"div",4)(5,"div",5)(6,"div",6)(7,"h5",7),t._uU(8,"Aktu\xe1ln\xed serverov\xfd certifik\xe1t"),t.qZA()(),t.TgZ(9,"div",8),t.YNc(10,b,4,0,"div",9),t.YNc(11,T,5,0,"div",10),t.YNc(12,y,57,20,"div",11),t.qZA(),t.TgZ(13,"div",12)(14,"button",13),t.NdJ("click",function(){return r.loadCertificateInfo()}),t._UZ(15,"i",14),t._uU(16," Obnovit "),t.qZA()()()()(),t.YNc(17,D,63,9,"div",2),t.YNc(18,I,58,16,"div",2),t.qZA()),2&a&&(t.xp6(1),t.Q6J("ngIf",!r.isAdmin),t.xp6(1),t.Q6J("ngIf",r.isAdmin),t.xp6(8),t.Q6J("ngIf",r.loading),t.xp6(1),t.Q6J("ngIf",!r.loading&&!r.certificateInfo),t.xp6(1),t.Q6J("ngIf",!r.loading&&r.certificateInfo),t.xp6(2),t.Q6J("disabled",r.loading),t.xp6(3),t.Q6J("ngIf",!r.generatedCertificate),t.xp6(1),t.Q6J("ngIf",r.generatedCertificate))},dependencies:[l.mk,l.O5,s._Y,s.Fj,s.wV,s.Wl,s._,s.JJ,s.JL,s.qQ,s.Fd,s.sg,s.u,l.uU,h.H],styles:[".card[_ngcontent-%COMP%]{border-radius:8px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{border-top-left-radius:8px;border-top-right-radius:8px}.card-footer[_ngcontent-%COMP%]{border-bottom-left-radius:8px;border-bottom-right-radius:8px}.text-success[_ngcontent-%COMP%]{color:#28a745!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.badge[_ngcontent-%COMP%]{font-size:.9em;padding:.4em .6em}.form-text[_ngcontent-%COMP%]{font-size:.85em;color:#6c757d}.alert[_ngcontent-%COMP%]{border-radius:6px}.alert-warning[_ngcontent-%COMP%]{background-color:#fff3cd;border-color:#ffecb5;color:#856404}.alert-success[_ngcontent-%COMP%]{background-color:#d4edda;border-color:#c3e6cb;color:#155724}@media (prefers-color-scheme: dark){.card[_ngcontent-%COMP%]{background-color:#2c3034;border-color:#373b3e}.card-header[_ngcontent-%COMP%], .card-footer[_ngcontent-%COMP%]{background-color:#212529;border-color:#373b3e}.form-control[_ngcontent-%COMP%]{background-color:#2c3034;border-color:#495057;color:#e9ecef}.form-text[_ngcontent-%COMP%]{color:#adb5bd}.alert-warning[_ngcontent-%COMP%]{background-color:#332701;border-color:#664d03;color:#ffda6a}.alert-success[_ngcontent-%COMP%]{background-color:#051b11;border-color:#0f5132;color:#75b798}}"]}),i})();var S=u(4467);function w(i,o){1&i&&(t.TgZ(0,"div",17)(1,"div",18)(2,"span",19),t._uU(3,"Na\u010d\xedt\xe1n\xed..."),t.qZA()()())}function N(i,o){if(1&i&&(t.TgZ(0,"div",20),t._uU(1),t.qZA()),2&i){const e=t.oxw();t.xp6(1),t.hij(" ",e.errorMessage," ")}}function z(i,o){if(1&i&&(t.TgZ(0,"div")(1,"p")(2,"strong"),t._uU(3,"Detaily:"),t.qZA()(),t.TgZ(4,"pre",25),t._uU(5),t.qZA()()),2&i){const e=t.oxw(3);t.xp6(5),t.Oqu(e.status.lastError.additionalInfo)}}function j(i,o){if(1&i&&(t.TgZ(0,"div",24)(1,"h5"),t._uU(2,"Posledn\xed chyba:"),t.qZA(),t.TgZ(3,"div",20)(4,"p")(5,"strong"),t._uU(6,"\u010cas:"),t.qZA(),t._uU(7),t.qZA(),t.TgZ(8,"p")(9,"strong"),t._uU(10,"Popis:"),t.qZA(),t._uU(11),t.qZA(),t.YNc(12,z,6,1,"div",14),t.qZA()()),2&i){const e=t.oxw(2);t.xp6(7),t.hij(" ",e.formatDate(e.status.lastError.timestamp),""),t.xp6(4),t.hij(" ",e.status.lastError.description,""),t.xp6(1),t.Q6J("ngIf",e.status.lastError.additionalInfo)}}function F(i,o){if(1&i&&(t.TgZ(0,"div",24)(1,"h5"),t._uU(2,"Posledn\xed p\u0159\xedstup:"),t.qZA(),t.TgZ(3,"div",6)(4,"div",11)(5,"p")(6,"strong"),t._uU(7,"\u010cas:"),t.qZA(),t._uU(8),t.qZA(),t.TgZ(9,"p")(10,"strong"),t._uU(11,"Endpoint:"),t.qZA(),t._uU(12),t.qZA(),t.TgZ(13,"p")(14,"strong"),t._uU(15,"Status:"),t.qZA(),t._uU(16),t.qZA(),t.TgZ(17,"p")(18,"strong"),t._uU(19,"IP adresa:"),t.qZA(),t._uU(20),t.qZA(),t.TgZ(21,"p")(22,"strong"),t._uU(23,"Doba odezvy:"),t.qZA(),t._uU(24),t.qZA(),t.TgZ(25,"p")(26,"strong"),t._uU(27,"User Agent:"),t.qZA(),t._uU(28),t.qZA()()()()),2&i){const e=t.oxw(2);t.xp6(8),t.hij(" ",e.formatDate(e.status.lastAccess.timestamp),""),t.xp6(4),t.AsE(" ",e.status.lastAccess.method," ",e.status.lastAccess.endpoint,""),t.xp6(4),t.hij(" ",e.status.lastAccess.statusCode,""),t.xp6(4),t.hij(" ",e.status.lastAccess.ipAddress,""),t.xp6(4),t.hij(" ",e.status.lastAccess.responseTimeMs," ms"),t.xp6(4),t.hij(" ",e.status.lastAccess.additionalInfo,"")}}function O(i,o){if(1&i&&(t.TgZ(0,"div")(1,"div",21),t._UZ(2,"div",22),t.TgZ(3,"div")(4,"h4",8),t._uU(5),t.qZA()()(),t.YNc(6,j,13,3,"div",23),t.YNc(7,F,29,7,"div",23),t.qZA()),2&i){const e=t.oxw();t.xp6(2),t.Q6J("ngClass",e.getStatusClass()),t.xp6(3),t.Oqu(e.getStatusText()),t.xp6(1),t.Q6J("ngIf",!e.status.isRunning&&e.status.lastError),t.xp6(1),t.Q6J("ngIf",e.status.lastAccess)}}let M=(()=>{const o=class{constructor(n,a){this.systemStatusService=n,this.titleService=a,this.status=null,this.loading=!1,this.error=!1,this.errorMessage=""}ngOnInit(){this.titleService.setTitle("Stav DIS API"),this.loadStatus()}loadStatus(){this.loading=!0,this.systemStatusService.getDISApiStatus().subscribe({next:n=>{this.status=n,this.loading=!1,this.error=!1},error:n=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu DISApi",n),this.loading=!1,this.error=!0,this.errorMessage="Nepoda\u0159ilo se na\u010d\xedst stav DISApi"}})}getStatusClass(){return this.status?this.status.isRunning?"bg-success":"bg-danger":"bg-secondary"}getStatusText(){return this.status?this.status.isRunning?"B\u011b\u017e\xed":"Neb\u011b\u017e\xed":"Nezn\xe1m\xfd"}formatDate(n){return new Date(n).toLocaleString()}};let i=o;return o.\u0275fac=function(a){return new(a||o)(t.Y36(S.R),t.Y36(d.y))},o.\u0275cmp=t.Xpm({type:o,selectors:[["app-disapi-status-page"]],decls:46,vars:4,consts:[[1,"container"],[1,"row","mb-4"],[1,"col-12"],[1,"text-muted"],[1,"row"],[1,"col-md-8"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"btn","btn-sm","btn-outline-secondary",3,"disabled","click"],[1,"bi","bi-arrow-clockwise"],[1,"card-body"],["class","text-center",4,"ngIf"],["class","alert alert-danger",4,"ngIf"],[4,"ngIf"],[1,"col-md-4"],[1,"card-header"],[1,"text-center"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"alert","alert-danger"],[1,"d-flex","align-items-center","mb-4"],[1,"status-indicator","me-3",3,"ngClass"],["class","mt-4",4,"ngIf"],[1,"mt-4"],[1,"error-details"]],template:function(a,r){1&a&&(t.TgZ(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),t._uU(4,"Stav DIS API"),t.qZA(),t.TgZ(5,"p",3),t._uU(6," Tato str\xe1nka zobrazuje aktu\xe1ln\xed stav DIS API a informace o posledn\xedch chyb\xe1ch a p\u0159\xedstupech. "),t.qZA()()(),t.TgZ(7,"div",4)(8,"div",5)(9,"div",6)(10,"div",7)(11,"h5",8),t._uU(12,"Stav DIS API"),t.qZA(),t.TgZ(13,"button",9),t.NdJ("click",function(){return r.loadStatus()}),t._UZ(14,"i",10),t._uU(15," Obnovit "),t.qZA()(),t.TgZ(16,"div",11),t.YNc(17,w,4,0,"div",12),t.YNc(18,N,2,1,"div",13),t.YNc(19,O,8,4,"div",14),t.qZA()()(),t.TgZ(20,"div",15)(21,"div",6)(22,"div",16)(23,"h5",8),t._uU(24,"Informace"),t.qZA()(),t.TgZ(25,"div",11)(26,"p"),t._uU(27,"DIS API je rozhran\xed pro komunikaci s instancemi DIS. Poskytuje n\xe1sleduj\xedc\xed funkce:"),t.qZA(),t.TgZ(28,"ul")(29,"li"),t._uU(30,"Autentizace pomoc\xed klientsk\xfdch certifik\xe1t\u016f"),t.qZA(),t.TgZ(31,"li"),t._uU(32,"Zabezpe\u010den\xfd p\u0159enos dat"),t.qZA(),t.TgZ(33,"li"),t._uU(34,"Validace po\u017eadavk\u016f"),t.qZA(),t.TgZ(35,"li"),t._uU(36,"Logov\xe1n\xed p\u0159\xedstup\u016f"),t.qZA()(),t.TgZ(37,"p"),t._uU(38,"V p\u0159\xedpad\u011b probl\xe9m\u016f s DIS API zkontrolujte:"),t.qZA(),t.TgZ(39,"ul")(40,"li"),t._uU(41,"Platnost serverov\xe9ho certifik\xe1tu"),t.qZA(),t.TgZ(42,"li"),t._uU(43,"Konfiguraci v souboru appsettings.json"),t.qZA(),t.TgZ(44,"li"),t._uU(45,"Logy aplikace"),t.qZA()()()()()()()),2&a&&(t.xp6(13),t.Q6J("disabled",r.loading),t.xp6(4),t.Q6J("ngIf",r.loading),t.xp6(1),t.Q6J("ngIf",r.error),t.xp6(1),t.Q6J("ngIf",!r.loading&&!r.error&&r.status))},dependencies:[l.mk,l.O5],styles:[".status-indicator[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%}.bg-success[_ngcontent-%COMP%]{background-color:#28a745}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d}.error-details[_ngcontent-%COMP%]{font-size:.9rem;max-height:300px;overflow-y:auto;background-color:#0000000d;padding:15px;border-radius:4px}"]}),i})();var J=u(529);let Q=(()=>{const o=class{constructor(n){this.http=n,this.baseUrl="/api/communicationpatterns"}getAllPatterns(){return this.http.get(this.baseUrl)}getPattern(n){return this.http.get(`${this.baseUrl}/${n}`)}getConfiguration(){return this.http.get(`${this.baseUrl}/configuration`)}refreshPatterns(){return this.http.post(`${this.baseUrl}/refresh`,{})}testDetection(){return this.http.post(`${this.baseUrl}/test-detection`,{})}};let i=o;return o.\u0275fac=function(a){return new(a||o)(t.LFG(J.eN))},o.\u0275prov=t.Yz7({token:o,factory:o.\u0275fac,providedIn:"root"}),i})();var Y=u(5141);function L(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",12),t._UZ(1,"i",13),t._uU(2),t.TgZ(3,"button",14),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.error=null)}),t.qZA()()}if(2&i){const e=t.oxw();t.xp6(2),t.hij(" ",e.error," ")}}function H(i,o){1&i&&(t.TgZ(0,"div",15)(1,"div",16)(2,"span",17),t._uU(3,"Na\u010d\xedt\xe1m..."),t.qZA()(),t.TgZ(4,"p",18),t._uU(5,"Na\u010d\xedt\xe1m komunika\u010dn\xed vzorce..."),t.qZA()())}function B(i,o){1&i&&(t.TgZ(0,"span"),t._uU(1," Testovat detekci"),t.qZA())}function R(i,o){1&i&&(t.TgZ(0,"span"),t._uU(1," Testov\xe1n\xed..."),t.qZA())}function K(i,o){1&i&&(t.TgZ(0,"span"),t._uU(1," Aktualizovat vzorce"),t.qZA())}function V(i,o){1&i&&(t.TgZ(0,"span"),t._uU(1," Aktualizuji..."),t.qZA())}function G(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",19)(1,"div",20)(2,"h5",21),t._UZ(3,"i",22),t._uU(4," Konfigurace detekce v\xfdpadk\u016f "),t.qZA(),t.TgZ(5,"div")(6,"button",23),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.testDetection())}),t._UZ(7,"i",24),t.YNc(8,B,2,0,"span",25),t.YNc(9,R,2,0,"span",25),t.qZA(),t.TgZ(10,"button",26),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.refreshPatterns())}),t._UZ(11,"i",27),t.YNc(12,K,2,0,"span",25),t.YNc(13,V,2,0,"span",25),t.qZA()()(),t.TgZ(14,"div",28)(15,"div",1)(16,"div",29)(17,"strong",30),t._uU(18,"Okno anal\xfdzy:"),t.qZA(),t._uU(19),t.qZA(),t.TgZ(20,"div",29)(21,"strong",31),t._uU(22,"Tolerance:"),t.qZA(),t._uU(23),t.qZA(),t.TgZ(24,"div",29)(25,"strong",32),t._uU(26,"Min. frekvence:"),t.qZA(),t._uU(27),t.qZA(),t.TgZ(28,"div",29)(29,"strong",33),t._uU(30,"Vylou\u010dit v\xedkendy: "),t.qZA(),t.TgZ(31,"span",34),t._uU(32),t.qZA()()(),t.TgZ(33,"div",35)(34,"div",29)(35,"strong",36),t._uU(36,"Min. interval alert\u016f:"),t.qZA(),t._uU(37),t.qZA(),t.TgZ(38,"div",29)(39,"strong",37),t._uU(40,"Aktualizace vzorc\u016f:"),t.qZA(),t._uU(41),t.qZA(),t.TgZ(42,"div",29)(43,"strong",38),t._uU(44,"Interval kontrol:"),t.qZA(),t._uU(45),t.qZA(),t.TgZ(46,"div",29)(47,"strong",39),t._uU(48,"Min. vol\xe1n\xed pro anal\xfdzu:"),t.qZA(),t._uU(49),t.qZA()()()()}if(2&i){const e=t.oxw();t.xp6(6),t.Q6J("disabled",e.testing),t.xp6(1),t.ekj("spinner-border",e.testing)("spinner-border-sm",e.testing),t.xp6(1),t.Q6J("ngIf",!e.testing),t.xp6(1),t.Q6J("ngIf",e.testing),t.xp6(1),t.Q6J("disabled",e.refreshing),t.xp6(1),t.ekj("spinner-border",e.refreshing)("spinner-border-sm",e.refreshing),t.xp6(1),t.Q6J("ngIf",!e.refreshing),t.xp6(1),t.Q6J("ngIf",e.refreshing),t.xp6(6),t.hij(" ",e.config.analysisWindowDays," dn\xed "),t.xp6(4),t.hij(" ",e.config.toleranceMinutes," minut "),t.xp6(4),t.hij(" ",e.config.minimumFrequencyPerHour,"/hod "),t.xp6(4),t.Tol(e.config.excludeWeekends?"bg-success":"bg-secondary"),t.xp6(1),t.hij(" ",e.config.excludeWeekends?"Ano":"Ne"," "),t.xp6(5),t.hij(" ",e.config.minimumAlertInterval," min "),t.xp6(4),t.hij(" ",e.config.patternUpdateIntervalHours," hod "),t.xp6(4),t.hij(" ",e.config.checkIntervalMinutes," min "),t.xp6(4),t.hij(" ",e.config.minimumCallsForAnalysis," ")}}function $(i,o){if(1&i&&(t.TgZ(0,"tr")(1,"td")(2,"strong"),t._uU(3),t.qZA()(),t.TgZ(4,"td",44)(5,"strong"),t._uU(6),t.qZA()(),t.TgZ(7,"td")(8,"span"),t._uU(9),t.qZA()(),t.TgZ(10,"td",48)(11,"span",34),t._uU(12),t.qZA()(),t.TgZ(13,"td",48)(14,"small"),t._uU(15),t.qZA()(),t.TgZ(16,"td",49)(17,"strong"),t._uU(18),t.qZA()(),t.TgZ(19,"td",50),t._uU(20),t.qZA(),t.TgZ(21,"td",50),t._uU(22),t.qZA(),t.TgZ(23,"td",51),t._uU(24),t.qZA(),t.TgZ(25,"td",44),t._uU(26),t.qZA(),t.TgZ(27,"td",51)(28,"span",34),t._uU(29),t.qZA()()()),2&i){const e=o.$implicit,n=t.oxw(2);t.xp6(3),t.Oqu(e.customerAbbreviation),t.xp6(3),t.Oqu(e.instanceName),t.xp6(2),t.Tol(n.getStatusBadgeClass(e.status)),t.xp6(1),t.hij(" ",n.getStatusText(e.status)," "),t.xp6(1),t.Q6J("title",n.getDetectionLogicTooltip(e.pattern)),t.xp6(1),t.Tol(n.getDetectionLogicBadgeClass(e.pattern.detectionLogicType)),t.xp6(1),t.hij(" ",n.getDetectionLogicShortText(e.pattern.detectionLogicType)," "),t.xp6(1),t.Q6J("title",n.getActiveHoursTooltip(e.pattern)),t.xp6(2),t.Oqu(n.formatActiveHours(e.pattern)),t.xp6(3),t.Oqu(e.pattern.totalCallsAnalyzed),t.xp6(2),t.hij(" ",n.formatNumber(e.pattern.averageIntervalMinutes)," "),t.xp6(2),t.hij(" ",n.formatNumber(e.pattern.standardDeviation)," "),t.xp6(2),t.AsE(" ",n.formatNumber(e.pattern.minIntervalMinutes)," / ",n.formatNumber(e.pattern.maxIntervalMinutes)," "),t.xp6(2),t.hij(" ",n.formatDate(e.lastConnectionDate)," "),t.xp6(2),t.Tol(e.pattern.hasSufficientData?"bg-success":"bg-warning"),t.xp6(1),t.hij(" ",e.pattern.hasSufficientData?"Ano":"Ne"," ")}}function W(i,o){1&i&&(t.TgZ(0,"tr")(1,"td",52),t._UZ(2,"i",53),t.TgZ(3,"p",18),t._uU(4,"\u017d\xe1dn\xe9 komunika\u010dn\xed vzorce nebyly nalezeny"),t.qZA()()())}function X(i,o){if(1&i&&(t.TgZ(0,"app-collapsible-block",40)(1,"div",41)(2,"table",42)(3,"thead",43)(4,"tr")(5,"th",44),t._uU(6,"Z\xe1kazn\xedk"),t.qZA(),t.TgZ(7,"th",44),t._uU(8,"Instance"),t.qZA(),t.TgZ(9,"th",44),t._uU(10,"Stav"),t.qZA(),t.TgZ(11,"th",44),t._uU(12,"Typ detekce"),t.qZA(),t.TgZ(13,"th",44),t._uU(14,"Aktivn\xed hodiny"),t.qZA(),t.TgZ(15,"th",45),t._uU(16,"Vol\xe1n\xed za 30 dn\xed"),t.qZA(),t.TgZ(17,"th",44),t._uU(18,"Pr\u016fm\u011br (min)"),t.qZA(),t.TgZ(19,"th",44),t._uU(20,"Std. odchylka"),t.qZA(),t.TgZ(21,"th",44),t._uU(22,"Min/max (min)"),t.qZA(),t.TgZ(23,"th",46),t._uU(24,"Posl. kontakt"),t.qZA(),t.TgZ(25,"th",44),t._uU(26,"Dostatek dat"),t.qZA()()(),t.TgZ(27,"tbody"),t.YNc(28,$,30,20,"tr",47),t.YNc(29,W,5,0,"tr",25),t.qZA()()()()),2&i){const e=t.oxw();t.Q6J("showRecordCount",!0)("recordCount",e.patterns.length)("defaultExpanded",!0),t.xp6(28),t.Q6J("ngForOf",e.patterns),t.xp6(1),t.Q6J("ngIf",0===e.patterns.length)}}function tt(i,o){1&i&&(t.TgZ(0,"div",15),t._UZ(1,"i",63),t.TgZ(2,"p",18),t._uU(3,"\u017d\xe1dn\xe9 v\xfdpadky komunikace nebyly detekov\xe1ny"),t.qZA()())}function et(i,o){if(1&i&&(t.TgZ(0,"tr")(1,"td"),t._uU(2),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA(),t.TgZ(5,"td"),t._uU(6),t.qZA(),t.TgZ(7,"td"),t._uU(8),t.qZA(),t.TgZ(9,"td"),t._uU(10),t.qZA()()),2&i){const e=o.$implicit,n=t.oxw(3);t.xp6(2),t.Oqu(e.instanceName),t.xp6(2),t.Oqu(e.customerAbbreviation),t.xp6(2),t.Oqu(n.formatTimeSpan(e.timeSinceLastContact)),t.xp6(2),t.Oqu(n.formatTimeSpan(e.expectedMaxInterval)),t.xp6(2),t.Oqu(n.formatDate(e.lastContactTime))}}function nt(i,o){if(1&i&&(t.TgZ(0,"div")(1,"div",64),t._UZ(2,"i",13),t._uU(3),t.qZA(),t.TgZ(4,"div",41)(5,"table",65)(6,"thead")(7,"tr")(8,"th"),t._uU(9,"Instance"),t.qZA(),t.TgZ(10,"th"),t._uU(11,"Z\xe1kazn\xedk"),t.qZA(),t.TgZ(12,"th"),t._uU(13,"\u010cas od kontaktu"),t.qZA(),t.TgZ(14,"th"),t._uU(15,"O\u010dek\xe1van\xfd max"),t.qZA(),t.TgZ(16,"th"),t._uU(17,"Posledn\xed kontakt"),t.qZA()()(),t.TgZ(18,"tbody"),t.YNc(19,et,11,5,"tr",47),t.qZA()()()()),2&i){const e=t.oxw(2);t.xp6(3),t.hij(" Detekov\xe1no ",e.testResults.length," v\xfdpadk\u016f komunikace "),t.xp6(16),t.Q6J("ngForOf",e.testResults)}}function it(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",54)(1,"div",55)(2,"div",56)(3,"div",57)(4,"h5",58),t._UZ(5,"i",59),t._uU(6," V\xfdsledky testov\xe1n\xed detekce v\xfdpadk\u016f "),t.qZA(),t.TgZ(7,"button",14),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.closeTestResults())}),t.qZA()(),t.TgZ(8,"div",60),t.YNc(9,tt,4,0,"div",7),t.YNc(10,nt,20,2,"div",25),t.qZA(),t.TgZ(11,"div",61)(12,"button",62),t.NdJ("click",function(){t.CHM(e);const a=t.oxw();return t.KtG(a.closeTestResults())}),t._uU(13,"Zav\u0159\xedt"),t.qZA()()()()()}if(2&i){const e=t.oxw();t.Udp("display",e.showTestResults?"block":"none"),t.ekj("show",e.showTestResults),t.xp6(9),t.Q6J("ngIf",0===e.testResults.length),t.xp6(1),t.Q6J("ngIf",e.testResults.length>0)}}function ot(i,o){if(1&i&&t._UZ(0,"div",66),2&i){const e=t.oxw();t.ekj("show",e.showTestResults)}}let at=(()=>{const o=class{constructor(n,a){this.communicationPatternService=n,this.titleService=a,this.patterns=[],this.config=null,this.testResults=[],this.loading=!1,this.refreshing=!1,this.testing=!1,this.error=null,this.showTestResults=!1}ngOnInit(){this.titleService.setTitle("Komunika\u010dn\xed vzorce"),this.loadData()}ngAfterViewInit(){setTimeout(()=>{this.initPopovers()},500)}ngOnDestroy(){this.destroyPopovers()}loadData(){this.loading=!0,this.error=null,this.communicationPatternService.getAllPatterns().subscribe({next:n=>{n.success?this.patterns=n.data:this.error=n.message||"Chyba p\u0159i na\u010d\xedt\xe1n\xed vzorc\u016f"},error:n=>{this.error="Chyba p\u0159i na\u010d\xedt\xe1n\xed vzorc\u016f: "+(n.message||n),console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed vzorc\u016f:",n)}}),this.communicationPatternService.getConfiguration().subscribe({next:n=>{n.success&&(this.config=n.data),this.loading=!1,setTimeout(()=>{this.initPopovers()},100)},error:n=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed konfigurace:",n),this.loading=!1}})}refreshPatterns(){this.refreshing=!0,this.error=null,this.communicationPatternService.refreshPatterns().subscribe({next:n=>{n.success?this.loadData():this.error=n.message||"Chyba p\u0159i aktualizaci vzorc\u016f",this.refreshing=!1},error:n=>{this.error="Chyba p\u0159i aktualizaci vzorc\u016f: "+(n.message||n),this.refreshing=!1,console.error("Chyba p\u0159i aktualizaci vzorc\u016f:",n)}})}testDetection(){this.testing=!0,this.error=null,this.testResults=[],this.communicationPatternService.testDetection().subscribe({next:n=>{n.success?(this.testResults=n.data,this.showTestResults=!0):this.error=n.message||"Chyba p\u0159i testov\xe1n\xed detekce",this.testing=!1},error:n=>{this.error="Chyba p\u0159i testov\xe1n\xed detekce: "+(n.message||n),this.testing=!1,console.error("Chyba p\u0159i testov\xe1n\xed detekce:",n)}})}getStatusBadgeClass(n){switch(n.toLowerCase()){case"active":return"badge bg-success";case"inactive":default:return"badge bg-secondary";case"blocked":return"badge bg-danger"}}getStatusText(n){switch(n.toLowerCase()){case"active":return"Aktivn\xed";case"inactive":return"Neaktivn\xed";case"blocked":return"Blokovan\xe1";default:return n}}formatTimeSpan(n){const a=n.split(":");if(a.length>=2){const r=parseInt(a[0]),c=parseInt(a[1]);return r>0?`${r}h ${c}m`:`${c}m`}return n}formatDate(n){return n?("string"==typeof n?new Date(n):n).toLocaleString("cs-CZ"):"Nikdy"}formatNumber(n){return n.toFixed(1)}formatActiveHours(n){if(!n.activeHours)return"Neanalyzov\xe1no";const a=n.activeHours,r=(100*a.confidence).toFixed(0);return`${a.startHour}:00-${a.endHour}:00 (${r}% spolehlivost)`}getActiveHoursTooltip(n){if(!n.activeHours)return"Aktivn\xed hodiny nebyly analyzov\xe1ny - nedostatek historick\xfdch dat";const a=n.activeHours;return`Aktivn\xed dny: ${a.activeDays?.map(c=>this.getDayName(c)).join(", ")||"\u017d\xe1dn\xe9 dny"}\nSpolehlivost: ${(100*a.confidence).toFixed(1)}%`}getDayName(n){return["Ne","Po","\xdat","St","\u010ct","P\xe1","So"][n]||n.toString()}getDetectionLogicTooltip(n){return n.detectionLogicDescription||"Informace o detek\u010dn\xed logice nejsou k dispozici"}getDetectionLogicShortText(n){switch(n){case"Nastaven\xe9 pracovn\xed hodiny":return"Nastaven\xe9";case"Historick\xe1 anal\xfdza":return"Historick\xe1";case"Nep\u0159etr\u017eit\xfd monitoring":return"Nep\u0159etr\u017eit\xfd";default:return"Nezn\xe1m\xfd"}}getDetectionLogicBadgeClass(n){switch(n){case"Nastaven\xe9 pracovn\xed hodiny":return"bg-success";case"Historick\xe1 anal\xfdza":return"bg-info";case"Nep\u0159etr\u017eit\xfd monitoring":return"bg-warning";default:return"bg-secondary"}}closeTestResults(){this.showTestResults=!1,this.testResults=[]}initPopovers(){const n={"analysis-window":"Po\u010det dn\xed zp\u011btn\u011b, ze kter\xfdch se analyzuj\xed komunika\u010dn\xed vzorce instanc\xed. Del\u0161\xed obdob\xed poskytuje p\u0159esn\u011bj\u0161\xed anal\xfdzu, ale vy\u017eaduje v\xedce historick\xfdch dat.",tolerance:"Tolerance v minut\xe1ch pro detekci v\xfdpadk\u016f komunikace. Pokud instance nekomunikuje d\xe9le ne\u017e o\u010dek\xe1van\xfd interval + tolerance, je detekov\xe1n v\xfdpadek.","min-frequency":"Minim\xe1ln\xed frekvence komunikace za hodinu pot\u0159ebn\xe1 pro anal\xfdzu vzorce. Instance s ni\u017e\u0161\xed frekvenc\xed nebudou analyzov\xe1ny.","exclude-weekends":"Ur\u010duje, zda se maj\xed v\xedkendy vylou\u010dit z anal\xfdzy komunika\u010dn\xedch vzorc\u016f. U\u017eite\u010dn\xe9 pro instance, kter\xe9 nekomunikuj\xed o v\xedkendech.","min-alert-interval":"Minim\xe1ln\xed interval mezi alerty pro stejnou instanci v minut\xe1ch. Zabra\u0148uje spamov\xe1n\xed alert\u016f p\u0159i opakovan\xfdch v\xfdpadc\xedch.","pattern-update-interval":"Interval v hodin\xe1ch, jak \u010dasto se aktualizuj\xed komunika\u010dn\xed vzorce. Krat\u0161\xed interval znamen\xe1 aktu\xe1ln\u011bj\u0161\xed vzorce, ale vy\u0161\u0161\xed z\xe1t\u011b\u017e syst\xe9mu.","check-interval":"Interval v minut\xe1ch, jak \u010dasto se kontroluj\xed v\xfdpadky komunikace. Krat\u0161\xed interval znamen\xe1 rychlej\u0161\xed detekci v\xfdpadk\u016f.","min-calls-analysis":"Minim\xe1ln\xed po\u010det vol\xe1n\xed pot\u0159ebn\xfd pro anal\xfdzu komunika\u010dn\xedho vzorce instance. Instance s men\u0161\xedm po\u010dtem vol\xe1n\xed nebudou analyzov\xe1ny."};this.destroyPopovers(),document.querySelectorAll('[data-bs-toggle="popover"]').forEach(a=>{const r=a.getAttribute("data-help-type");if(r&&r in n)try{new bootstrap.Popover(a,{content:n[r],html:!0,trigger:"hover focus",placement:"top",container:"body",sanitize:!1})}catch(c){console.error("Chyba p\u0159i inicializaci popoveru:",c)}else r&&console.warn("N\xe1pov\u011bda nenalezena pro typ:",r)})}destroyPopovers(){document.querySelectorAll('[data-bs-toggle="popover"]').forEach(n=>{const a=bootstrap.Popover.getInstance(n);a&&a.dispose()})}};let i=o;return o.\u0275fac=function(a){return new(a||o)(t.Y36(Q),t.Y36(d.y))},o.\u0275cmp=t.Xpm({type:o,selectors:[["app-communication-patterns"]],decls:15,vars:6,consts:[[1,"container-fluid"],[1,"row"],[1,"col-12"],[1,"mb-4"],[1,"bi","bi-diagram-3-fill","me-2"],[1,"text-muted","mb-0"],["class","alert alert-danger alert-dismissible fade show","role","alert",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["class","card mb-4",4,"ngIf"],["title","Komunika\u010dn\xed vzorce","icon","bi-table","storageKey","collapsible_block_communication_patterns","marginTop","","marginBottom","mb-4",3,"showRecordCount","recordCount","defaultExpanded",4,"ngIf"],["class","modal fade","tabindex","-1","role","dialog",3,"show","display",4,"ngIf"],["class","modal-backdrop fade",3,"show",4,"ngIf"],["role","alert",1,"alert","alert-danger","alert-dismissible","fade","show"],[1,"bi","bi-exclamation-triangle-fill","me-2"],["type","button",1,"btn-close",3,"click"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2"],[1,"card","mb-4"],[1,"card-header","bg-light","d-flex","justify-content-between","align-items-center"],[1,"card-title","mb-0"],[1,"bi","bi-gear-fill","me-2"],[1,"btn","btn-outline-primary","btn-sm","me-2",3,"disabled","click"],[1,"bi","bi-play-circle"],[4,"ngIf"],[1,"btn","btn-primary","btn-sm",3,"disabled","click"],[1,"bi","bi-arrow-clockwise"],[1,"card-body"],[1,"col-md-3"],["data-bs-toggle","popover","data-help-type","analysis-window",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","tolerance",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","min-frequency",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","exclude-weekends",2,"cursor","help"],[1,"badge"],[1,"row","mt-2"],["data-bs-toggle","popover","data-help-type","min-alert-interval",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","pattern-update-interval",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","check-interval",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","min-calls-analysis",2,"cursor","help"],["title","Komunika\u010dn\xed vzorce","icon","bi-table","storageKey","collapsible_block_communication_patterns","marginTop","","marginBottom","mb-4",3,"showRecordCount","recordCount","defaultExpanded"],[1,"table-responsive"],[1,"table","table-hover","mb-0"],[1,"table-light"],[1,"text-nowrap"],[1,"text-nowrap",2,"width","120px"],[1,"text-nowrap",2,"width","140px"],[4,"ngFor","ngForOf"],[3,"title"],[1,"text-center","text-nowrap"],[1,"text-end"],[1,"text-center"],["colspan","11",1,"text-center","py-4","text-muted"],[1,"bi","bi-inbox","fs-1"],["tabindex","-1","role","dialog",1,"modal","fade"],["role","document",1,"modal-dialog","modal-lg"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],[1,"bi","bi-bug-fill","me-2"],[1,"modal-body"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],[1,"bi","bi-check-circle-fill","text-success","fs-1"],[1,"alert","alert-warning"],[1,"table","table-sm"],[1,"modal-backdrop","fade"]],template:function(a,r){1&a&&(t.TgZ(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h2"),t._UZ(5,"i",4),t._uU(6," Komunika\u010dn\xed vzorce instanc\xed "),t.qZA(),t.TgZ(7,"p",5),t._uU(8," Anal\xfdza historick\xe9ho chov\xe1n\xed komunikace instanc\xed pro inteligentn\xed detekci v\xfdpadk\u016f. Instance bez nastaven\xfdch pracovn\xedch hodin pou\u017e\xedvaj\xed automaticky analyzovan\xe9 aktivn\xed hodiny. "),t.qZA()(),t.YNc(9,L,4,1,"div",6),t.YNc(10,H,6,0,"div",7),t.YNc(11,G,50,24,"div",8),t.YNc(12,X,30,5,"app-collapsible-block",9),t.qZA()()(),t.YNc(13,it,14,6,"div",10),t.YNc(14,ot,1,2,"div",11)),2&a&&(t.xp6(9),t.Q6J("ngIf",r.error),t.xp6(1),t.Q6J("ngIf",r.loading),t.xp6(1),t.Q6J("ngIf",r.config&&!r.loading),t.xp6(1),t.Q6J("ngIf",!r.loading),t.xp6(1),t.Q6J("ngIf",r.showTestResults),t.xp6(1),t.Q6J("ngIf",r.showTestResults))},dependencies:[l.sg,l.O5,Y.X],styles:[".card-header[_ngcontent-%COMP%]{background-color:#f8f9fa!important;border-bottom:1px solid #dee2e6}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:600;font-size:.875rem;letter-spacing:.5px;color:#495057}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{vertical-align:middle}.badge[_ngcontent-%COMP%]{font-size:.75rem}.spinner-border-sm[_ngcontent-%COMP%]{width:1rem;height:1rem}.modal-backdrop[_ngcontent-%COMP%]{background-color:#00000080}.text-end[_ngcontent-%COMP%]{text-align:right}.text-center[_ngcontent-%COMP%]{text-align:center}[data-bs-theme=dark][_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background-color:#343a40!important;border-bottom:1px solid #495057}[data-bs-theme=dark][_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{color:#adb5bd}[data-bs-theme=dark][_ngcontent-%COMP%]   .modal-backdrop[_ngcontent-%COMP%]{background-color:#ffffff1a}"]}),i})();var rt=u(4466);let st=(()=>{const o=class{};let i=o;return o.\u0275fac=function(a){return new(a||o)},o.\u0275mod=t.oAB({type:o}),o.\u0275inj=t.cJS({imports:[l.ez,s.UX,rt.m,g.Bz.forChild([{path:"server-certificate",component:P},{path:"disapi-status",component:M},{path:"communication-patterns",component:at}])]}),i})()}}]);