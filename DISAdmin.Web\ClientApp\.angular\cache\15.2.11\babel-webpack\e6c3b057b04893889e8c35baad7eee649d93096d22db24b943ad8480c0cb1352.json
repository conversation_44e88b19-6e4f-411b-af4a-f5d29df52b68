{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../pipes/local-date.pipe\";\nfunction ResolveAlertModalComponent_div_8_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"h6\", 12);\n    i0.ɵɵtext(2, \"Pravidlo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"table\", 13)(4, \"tr\")(5, \"td\", 22);\n    i0.ɵɵtext(6, \"N\\u00E1zev:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"tr\")(10, \"td\", 15);\n    i0.ɵɵtext(11, \"Podm\\u00EDnka:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"tr\")(15, \"td\", 15);\n    i0.ɵɵtext(16, \"Threshold:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"td\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate((tmp_0_0 = ctx_r2.getCurrentAlertRule()) == null ? null : tmp_0_0.name);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getConditionText((tmp_1_0 = ctx_r2.getCurrentAlertRule()) == null ? null : tmp_1_0.condition));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r2.getCurrentAlertRule()) == null ? null : tmp_2_0.threshold);\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_44_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtext(1, \" \\u0158e\\u0161en\\u00ED je povinn\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"form\", 23)(2, \"div\", 24)(3, \"label\", 25);\n    i0.ɵɵtext(4, \"\\u0158e\\u0161en\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"textarea\", 26);\n    i0.ɵɵtemplate(6, ResolveAlertModalComponent_div_8_div_44_div_6_Template, 2, 0, \"div\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 28);\n    i0.ɵɵelement(8, \"i\", 29);\n    i0.ɵɵelementStart(9, \"strong\");\n    i0.ɵɵtext(10, \"Mo\\u017Enosti:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ul\", 30)(12, \"li\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Vy\\u0159e\\u0161it:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"li\")(17, \"strong\");\n    i0.ɵɵtext(18, \"Ignorovat:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.resolveForm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r3.resolveForm.get(\"resolution\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r3.resolveForm.get(\"resolution\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" Ozna\\u010D\\u00ED \", ctx_r3.entityType === \"alert\" ? \"upozorn\\u011Bn\\u00ED\" : \"bezpe\\u010Dnostn\\u00ED ud\\u00E1lost\", \" jako vy\\u0159e\\u0161en\\u00E9 s popisem \\u0159e\\u0161en\\u00ED\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Ozna\\u010D\\u00ED \", ctx_r3.entityType === \"alert\" ? \"upozorn\\u011Bn\\u00ED\" : \"bezpe\\u010Dnostn\\u00ED ud\\u00E1lost\", \" jako ignorovan\\u00E9 (zn\\u00E1m\\u00E9, ale nehodl\\u00E1m \\u0159e\\u0161it)\");\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_45_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"small\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Vy\\u0159e\\u0161il:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"br\");\n    i0.ɵɵelementStart(6, \"small\")(7, \"strong\");\n    i0.ɵɵtext(8, \"Datum:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"localDate\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r8.getCurrentResolvedBy(), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(10, 2, ctx_r8.getCurrentResolvedAt(), \"dd.MM.yyyy HH:mm:ss\"), \"\");\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_45_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"strong\");\n    i0.ɵɵtext(2, \"\\u0158e\\u0161en\\u00ED:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r9.getCurrentResolution(), \" \");\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_45_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"i\", 32);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"Vy\\u0159e\\u0161eno\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ResolveAlertModalComponent_div_8_div_45_div_4_div_4_Template, 11, 5, \"div\", 33);\n    i0.ɵɵtemplate(5, ResolveAlertModalComponent_div_8_div_45_div_4_div_5_Template, 4, 1, \"div\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.getCurrentResolvedBy());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.getCurrentResolution());\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_45_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"i\", 35);\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3, \"Ignorov\\u00E1no\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ResolveAlertModalComponent_div_8_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h6\", 12);\n    i0.ɵɵtext(2, \"Stav \\u0159e\\u0161en\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtemplate(4, ResolveAlertModalComponent_div_8_div_45_div_4_Template, 6, 2, \"div\", 7);\n    i0.ɵɵtemplate(5, ResolveAlertModalComponent_div_8_div_45_div_5_Template, 4, 0, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getCurrentStatus() === \"resolved\" ? \"alert-success\" : \"alert-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getCurrentStatus() === \"resolved\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getCurrentStatus() === \"ignored\");\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"severity-high\": a0,\n    \"severity-medium\": a1,\n    \"severity-low\": a2\n  };\n};\nfunction ResolveAlertModalComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10)(2, \"div\", 11)(3, \"h6\", 12);\n    i0.ɵɵtext(4, \"Z\\u00E1kladn\\u00ED informace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"table\", 13)(6, \"tr\")(7, \"td\", 14);\n    i0.ɵɵtext(8, \"Datum a \\u010Das:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"tr\")(13, \"td\", 15);\n    i0.ɵɵtext(14, \"Typ:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵelement(16, \"i\", 16);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"tr\")(19, \"td\", 15);\n    i0.ɵɵtext(20, \"Z\\u00E1va\\u017Enost:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\")(22, \"span\", 17);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"div\", 11)(25, \"h6\", 12);\n    i0.ɵɵtext(26, \"Instance a z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"table\", 13)(28, \"tr\")(29, \"td\", 14);\n    i0.ɵɵtext(30, \"Z\\u00E1kazn\\u00EDk:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"td\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"tr\")(34, \"td\", 15);\n    i0.ɵɵtext(35, \"Instance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"td\");\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(38, ResolveAlertModalComponent_div_8_div_38_Template, 19, 3, \"div\", 18);\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"h6\", 12);\n    i0.ɵɵtext(41, \"Zpr\\u00E1va\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 20);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(44, ResolveAlertModalComponent_div_8_div_44_Template, 20, 4, \"div\", 7);\n    i0.ɵɵtemplate(45, ResolveAlertModalComponent_div_8_div_45_Template, 6, 3, \"div\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 12, ctx_r0.getCurrentTimestamp(), \"dd.MM.yyyy HH:mm:ss\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getAlertTypeIcon(ctx_r0.getCurrentType()) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getDisplayType(), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(15, _c0, ctx_r0.getCurrentSeverity() === 3, ctx_r0.getCurrentSeverity() === 2, ctx_r0.getCurrentSeverity() === 1));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getSeverityText(ctx_r0.getCurrentSeverity()), \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r0.getCurrentCustomerName() || \"-\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.getCurrentInstanceName() || \"-\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.entityType === \"alert\" && ctx_r0.getCurrentAlertRule());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.getAlertTypeClass(ctx_r0.getCurrentType()));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getCurrentDescription(), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getCurrentStatus() === \"active\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getCurrentStatus() !== \"active\");\n  }\n}\nfunction ResolveAlertModalComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function ResolveAlertModalComponent_div_12_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onIgnore());\n    });\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵtext(3, \"Ignorovat \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ResolveAlertModalComponent_div_12_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.onResolve());\n    });\n    i0.ɵɵelement(5, \"i\", 32);\n    i0.ɵɵtext(6, \"Vy\\u0159e\\u0161it \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.resolveForm.invalid);\n  }\n}\nexport let ResolveAlertModalComponent = /*#__PURE__*/(() => {\n  class ResolveAlertModalComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.alert = null;\n      this.securityEvent = null;\n      this.modalId = 'resolveAlertModal';\n      this.entityType = 'alert';\n      this.resolve = new EventEmitter();\n      this.ignore = new EventEmitter();\n      this.close = new EventEmitter();\n      this.resolveForm = this.fb.group({\n        resolution: ['', [Validators.required]]\n      });\n    }\n    ngOnInit() {\n      // Inicializace formuláře\n    }\n    onResolve() {\n      if (this.resolveForm.valid) {\n        const resolution = this.resolveForm.get('resolution')?.value || '';\n        this.resolve.emit(resolution);\n        this.resetForm();\n      }\n    }\n    onIgnore() {\n      this.ignore.emit();\n      this.resetForm();\n    }\n    onClose() {\n      this.close.emit();\n      this.resetForm();\n    }\n    resetForm() {\n      this.resolveForm.reset({\n        resolution: ''\n      });\n    }\n    /**\r\n     * Získání aktuální entity (alert nebo security event)\r\n     */\n    getCurrentEntity() {\n      return this.entityType === 'alert' ? this.alert : this.securityEvent;\n    }\n    /**\r\n     * Získání popisu aktuální entity\r\n     */\n    getCurrentDescription() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return '';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        // Pokud má vlastnost message (AlertItem), použijeme ji jako fallback\n        if ('message' in alert && alert.message) {\n          return alert.description || alert.message;\n        }\n        // Jinak použijeme pouze description (AlertResponse)\n        return alert.description || '';\n      } else {\n        return entity.description || '';\n      }\n    }\n    /**\r\n     * Získání typu aktuální entity pro zobrazení\r\n     */\n    getCurrentType() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return '';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        return alert.alertType || '';\n      } else {\n        return entity.eventType || '';\n      }\n    }\n    /**\r\n     * Získání timestamp aktuální entity\r\n     */\n    getCurrentTimestamp() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return new Date();\n      return entity.timestamp;\n    }\n    /**\r\n     * Získání závažnosti aktuální entity\r\n     */\n    getCurrentSeverity() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return 1;\n      return entity.severity;\n    }\n    /**\r\n     * Získání stavu aktuální entity\r\n     */\n    getCurrentStatus() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return 'active';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        // Pokud má vlastnost status (AlertItem), použijeme ji\n        if ('status' in alert && alert.status) {\n          return alert.status;\n        }\n        // Jinak použijeme boolean vlastnosti (AlertResponse)\n        return alert.isResolved ? 'resolved' : alert.isIgnored ? 'ignored' : 'active';\n      } else {\n        const secEvent = entity;\n        return secEvent.isResolved ? 'resolved' : secEvent.isIgnored ? 'ignored' : 'active';\n      }\n    }\n    /**\r\n     * Získání názvu zákazníka\r\n     */\n    getCurrentCustomerName() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return '';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        return alert.customerName || '';\n      } else {\n        // SecurityEvent nemá customerName\n        return '';\n      }\n    }\n    /**\r\n     * Získání názvu instance\r\n     */\n    getCurrentInstanceName() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return '';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        return alert.instanceName || '';\n      } else {\n        // SecurityEvent nemá instanceName\n        return '';\n      }\n    }\n    /**\r\n     * Získání informací o pravidle (pouze pro alerty)\r\n     */\n    getCurrentAlertRule() {\n      if (this.entityType !== 'alert') return null;\n      const alert = this.alert;\n      if (!alert) return null;\n      // Pokud má alert vazbu na pravidlo, vrátíme informace o pravidle\n      if (alert.alertRuleId && alert.alertRuleName) {\n        return {\n          name: alert.alertRuleName,\n          condition: this.getConditionText(alert.condition || 'gt'),\n          threshold: this.formatThreshold(alert.metricType, alert.threshold)\n        };\n      }\n      // Pro communicationOutage alerty bez explicitního pravidla nebo s pravidlem ale bez názvu\n      if (alert.metricType === 'communicationOutage' || alert.alertType === 'communicationOutage') {\n        return {\n          name: alert.alertRuleName || 'Výpadek komunikace DIS instancí',\n          condition: this.getConditionText(alert.condition || 'gt'),\n          threshold: alert.threshold ? this.formatThreshold(alert.metricType, alert.threshold) : this.getDefaultThreshold('communicationOutage')\n        };\n      }\n      // Pro ostatní alerty s pravidlem\n      if (alert.alertRuleId || alert.metricType) {\n        return {\n          name: alert.alertRuleName || this.getMetricTypeText(alert.metricType) || 'Neznámé pravidlo',\n          condition: this.getConditionText(alert.condition || 'gt'),\n          threshold: alert.threshold ? this.formatThreshold(alert.metricType, alert.threshold) : this.getDefaultThreshold(alert.metricType)\n        };\n      }\n      return null;\n    }\n    /**\r\n     * Formátování threshold hodnoty podle typu metriky\r\n     */\n    formatThreshold(metricType, threshold) {\n      if (threshold === null || threshold === undefined) {\n        return this.getDefaultThreshold(metricType);\n      }\n      switch (metricType) {\n        case 'communicationOutage':\n          return `${threshold} minut`;\n        case 'apiResponseTime':\n          return `${threshold} ms`;\n        case 'errorRate':\n          return `${threshold}%`;\n        case 'apiCallsCount':\n          return `${threshold} volání`;\n        case 'certificateExpiration':\n          return `${threshold} dní`;\n        default:\n          return threshold.toString();\n      }\n    }\n    /**\r\n     * Získání výchozího threshold pro typ metriky\r\n     */\n    getDefaultThreshold(metricType) {\n      switch (metricType) {\n        case 'communicationOutage':\n          return '30 minut';\n        case 'apiResponseTime':\n          return '1000 ms';\n        case 'errorRate':\n          return '5%';\n        default:\n          return 'N/A';\n      }\n    }\n    /**\r\n     * Získání informací o řešení\r\n     */\n    getCurrentResolvedBy() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return '';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        return alert.resolvedBy || '';\n      } else {\n        return entity.resolvedBy || '';\n      }\n    }\n    getCurrentResolvedAt() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return null;\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        return alert.resolvedAt || null;\n      } else {\n        return entity.resolvedAt || null;\n      }\n    }\n    getCurrentResolution() {\n      const entity = this.getCurrentEntity();\n      if (!entity) return '';\n      if (this.entityType === 'alert') {\n        const alert = entity;\n        return alert.resolution || '';\n      } else {\n        return entity.resolution || '';\n      }\n    }\n    /**\r\n     * Získání zobrazovaného typu (metricType pro alerty z pravidel, jinak přeložený alertType)\r\n     */\n    getDisplayType() {\n      if (this.entityType === 'alert') {\n        const alert = this.alert;\n        if (alert?.metricType) {\n          return this.getMetricTypeText(alert.metricType);\n        }\n      }\n      return this.getAlertTypeText(this.getCurrentType());\n    }\n    getSeverityClass(severity) {\n      switch (severity) {\n        case 5:\n          return 'text-danger fw-bold';\n        case 4:\n          return 'text-danger';\n        case 3:\n          return 'text-warning';\n        case 2:\n          return 'text-info';\n        case 1:\n          return 'text-success';\n        default:\n          return 'text-muted';\n      }\n    }\n    getAlertTypeClass(alertType) {\n      switch (alertType) {\n        case 'CertificateExpiring':\n          return 'alert-warning';\n        case 'FailedConnectionAttempts':\n          return 'alert-warning';\n        case 'SuspiciousActivity':\n          return 'alert-danger';\n        case 'ApiKeyMisuse':\n          return 'alert-danger';\n        case 'SystemError':\n          return 'alert-danger';\n        case 'Error':\n          return 'alert-danger';\n        case 'Warning':\n          return 'alert-warning';\n        case 'Information':\n          return 'alert-info';\n        default:\n          return 'alert-info';\n      }\n    }\n    getAlertTypeIcon(alertType) {\n      switch (alertType) {\n        case 'CertificateExpiring':\n          return 'bi-shield-exclamation';\n        case 'FailedConnectionAttempts':\n          return 'bi-x-circle-fill';\n        case 'SuspiciousActivity':\n          return 'bi-exclamation-triangle-fill';\n        case 'ApiKeyMisuse':\n          return 'bi-key-fill';\n        case 'SystemError':\n          return 'bi-exclamation-circle-fill';\n        case 'communicationOutage':\n          return 'bi-wifi-off';\n        case 'Error':\n          return 'bi-exclamation-circle-fill';\n        case 'Warning':\n          return 'bi-exclamation-triangle-fill';\n        case 'Information':\n          return 'bi-info-circle-fill';\n        default:\n          return 'bi-info-circle-fill';\n      }\n    }\n    getAlertTypeText(alertType) {\n      // Alert typy\n      switch (alertType) {\n        case 'CertificateExpiring':\n          return 'Expirující certifikát';\n        case 'FailedConnectionAttempts':\n          return 'Neúspěšné připojení';\n        case 'SuspiciousActivity':\n          return 'Podezřelá aktivita';\n        case 'ApiKeyMisuse':\n          return 'Chyba použití API klíče';\n        case 'SystemError':\n          return 'Systémová chyba';\n        case 'Other':\n          return 'Ostatní';\n        case 'Information':\n          return 'Informace';\n        case 'Warning':\n          return 'Varování';\n        case 'Error':\n          return 'Chyba';\n        // SecurityEvent typy\n        case 'FailedAccessAttempt':\n          return 'Neúspěšný pokus o přístup';\n        case 'IpBlocked':\n          return 'Blokovaná IP adresa';\n        case 'CertificateValidationFailure':\n          return 'Selhání validace certifikátu';\n        case 'UnauthorizedAccess':\n          return 'Neoprávněný přístup';\n        case 'UnauthorizedIpAccess':\n          return 'Přístup z nepovolené IP adresy';\n        default:\n          return alertType;\n      }\n    }\n    /**\r\n     * Získání textu pro typ metriky\r\n     */\n    getMetricTypeText(metricType) {\n      if (!metricType) {\n        return 'Systémový alert';\n      }\n      switch (metricType) {\n        case 'certificateExpiration':\n          return 'Expirace certifikátu';\n        case 'apiResponseTime':\n          return 'Doba odezvy API';\n        case 'apiCallCount':\n          return 'Počet API volání';\n        case 'errorRate':\n          return 'Míra chyb';\n        case 'diskSpace':\n          return 'Místo na disku';\n        case 'memoryUsage':\n          return 'Využití paměti';\n        case 'cpuUsage':\n          return 'Využití CPU';\n        case 'connectionCount':\n          return 'Počet připojení';\n        case 'failedLogins':\n          return 'Neúspěšná přihlášení';\n        case 'suspiciousActivity':\n          return 'Podezřelá aktivita';\n        case 'unauthorizedAccess':\n          return 'Neoprávněný přístup';\n        case 'dataIntegrity':\n          return 'Integrita dat';\n        case 'backupStatus':\n          return 'Stav zálohování';\n        case 'serviceAvailability':\n          return 'Dostupnost služby';\n        case 'networkLatency':\n          return 'Latence sítě';\n        case 'databasePerformance':\n          return 'Výkon databáze';\n        case 'logErrors':\n          return 'Chyby v logu';\n        case 'securityEvents':\n          return 'Bezpečnostní události';\n        case 'systemLoad':\n          return 'Zatížení systému';\n        case 'communicationOutage':\n          return 'Výpadek komunikace';\n        case 'custom':\n          return 'Vlastní metrika';\n        default:\n          return metricType;\n      }\n    }\n    /**\r\n     * Získání textu pro závažnost\r\n     */\n    getSeverityText(severity) {\n      switch (severity) {\n        case 3:\n          return 'Kritický';\n        case 2:\n          return 'Varování';\n        case 1:\n          return 'Informace';\n        default:\n          return 'Neznámá';\n      }\n    }\n    /**\r\n     * Získání textu pro stav\r\n     */\n    getStatusText(status) {\n      switch (status) {\n        case 'active':\n          return 'Aktivní';\n        case 'resolved':\n          return 'Vyřešeno';\n        case 'ignored':\n          return 'Ignorováno';\n        default:\n          return status;\n      }\n    }\n    /**\r\n     * Získání CSS třídy pro stav\r\n     */\n    getStatusClass(status) {\n      switch (status) {\n        case 'active':\n          return 'bg-danger text-white';\n        case 'resolved':\n          return 'bg-success text-white';\n        case 'ignored':\n          return 'bg-warning text-dark';\n        default:\n          return 'bg-secondary text-white';\n      }\n    }\n    /**\r\n     * Získání textu pro podmínku\r\n     */\n    getConditionText(condition) {\n      switch (condition) {\n        case 'gt':\n          return 'Větší než';\n        case 'lt':\n          return 'Menší než';\n        case 'eq':\n          return 'Rovno';\n        case 'gte':\n          return 'Větší nebo rovno';\n        case 'lte':\n          return 'Menší nebo rovno';\n        default:\n          return condition;\n      }\n    }\n    static {\n      this.ɵfac = function ResolveAlertModalComponent_Factory(t) {\n        return new (t || ResolveAlertModalComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: ResolveAlertModalComponent,\n        selectors: [[\"app-resolve-alert-modal\"]],\n        inputs: {\n          alert: \"alert\",\n          securityEvent: \"securityEvent\",\n          modalId: \"modalId\",\n          entityType: \"entityType\"\n        },\n        outputs: {\n          resolve: \"resolve\",\n          ignore: \"ignore\",\n          close: \"close\"\n        },\n        decls: 13,\n        vars: 6,\n        consts: [[\"tabindex\", \"-1\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"static\", \"data-bs-keyboard\", \"true\", 1, \"modal\", \"fade\", 3, \"id\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\", 3, \"id\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"row\", \"mb-4\"], [1, \"col-md-6\"], [1, \"text-muted\", \"mb-2\"], [1, \"table\", \"table-sm\", \"table-borderless\"], [1, \"fw-bold\", 2, \"width\", \"40%\"], [1, \"fw-bold\"], [3, \"ngClass\"], [1, \"badge\", \"severity-badge\", 3, \"ngClass\"], [\"class\", \"mb-4\", 4, \"ngIf\"], [1, \"mb-4\"], [1, \"alert\", 3, \"ngClass\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"fw-bold\", 2, \"width\", \"20%\"], [3, \"formGroup\"], [1, \"mb-3\"], [\"for\", \"resolution\", 1, \"form-label\", \"fw-bold\"], [\"id\", \"resolution\", \"formControlName\", \"resolution\", \"rows\", \"3\", \"placeholder\", \"Popi\\u0161te, jak bylo upozorn\\u011Bn\\u00ED vy\\u0159e\\u0161eno\", 1, \"form-control\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [1, \"alert\", \"alert-info\"], [1, \"bi\", \"bi-info-circle\", \"me-1\"], [1, \"mb-0\", \"mt-2\"], [1, \"text-danger\"], [1, \"bi\", \"bi-check-circle-fill\", \"me-1\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [1, \"mt-2\"], [1, \"bi\", \"bi-eye-slash-fill\", \"me-1\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-warning\", \"me-2\", 3, \"click\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-primary\", 3, \"disabled\", \"click\"]],\n        template: function ResolveAlertModalComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\", 4);\n            i0.ɵɵtext(5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(6, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function ResolveAlertModalComponent_Template_button_click_6_listener() {\n              return ctx.onClose();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"div\", 6);\n            i0.ɵɵtemplate(8, ResolveAlertModalComponent_div_8_Template, 46, 19, \"div\", 7);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"button\", 9);\n            i0.ɵɵlistener(\"click\", function ResolveAlertModalComponent_Template_button_click_10_listener() {\n              return ctx.onClose();\n            });\n            i0.ɵɵtext(11, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(12, ResolveAlertModalComponent_div_12_Template, 7, 1, \"div\", 7);\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"id\", ctx.modalId);\n            i0.ɵɵattribute(\"aria-labelledby\", ctx.modalId + \"Label\");\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"id\", ctx.modalId + \"Label\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵtextInterpolate1(\" Detail \", ctx.entityType === \"alert\" ? \"upozorn\\u011Bn\\u00ED\" : \"bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\", \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.getCurrentEntity());\n            i0.ɵɵadvance(4);\n            i0.ɵɵproperty(\"ngIf\", ctx.getCurrentStatus() === \"active\");\n          }\n        },\n        dependencies: [i2.NgClass, i2.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.LocalDatePipe],\n        styles: [\".severity-badge[_ngcontent-%COMP%]{min-width:80px;padding:.5em .75em;border-radius:.375rem;font-weight:500;font-size:.75em;line-height:1;text-align:center;white-space:nowrap;vertical-align:baseline}.severity-high[_ngcontent-%COMP%]{background-color:#dc3545!important;color:#fff!important}.severity-medium[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529!important}.severity-low[_ngcontent-%COMP%], .severity-1[_ngcontent-%COMP%]{background-color:#0dcaf0!important;color:#212529!important}.severity-2[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529!important}.severity-3[_ngcontent-%COMP%]{background-color:#dc3545!important;color:#fff!important}body.dark-theme[_nghost-%COMP%]   .severity-medium[_ngcontent-%COMP%], body.dark-theme   [_nghost-%COMP%]   .severity-medium[_ngcontent-%COMP%], body.dark-theme[_nghost-%COMP%]   .severity-low[_ngcontent-%COMP%], body.dark-theme   [_nghost-%COMP%]   .severity-low[_ngcontent-%COMP%]{color:#212529!important}\"]\n      });\n    }\n  }\n  return ResolveAlertModalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}