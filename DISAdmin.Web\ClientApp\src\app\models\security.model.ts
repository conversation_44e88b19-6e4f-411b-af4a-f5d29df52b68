export enum MatchType {
  ExactMatch = 0,
  Contains = 1,
  StartsWith = 2,
  EndsWith = 3
}

export interface SecurityDashboardResponse {
  recentSecurityEvents: SecurityEventResponse[];
  activeAlerts: AlertResponse[];
  failedConnectionStats: FailedConnectionStatsResponse[];
}

export interface SecurityEventResponse {
  id: number;
  timestamp: Date;
  eventType: string;
  ipAddress: string;
  username?: string;
  description: string;
  severity: number;
  isResolved: boolean;
  isIgnored: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolution?: string;
}

export interface AlertResponse {
  id: number;
  timestamp: Date;
  alertType: string;
  instanceId?: number;
  instanceName?: string;
  customerName?: string;
  customerAbbreviation?: string;
  description: string;
  severity: number;
  isResolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
  resolution?: string;
  isIgnored?: boolean;
}

export interface FailedConnectionStatsResponse {
  instanceId: number;
  instanceName: string;
  failedCertificateValidationCount: number;
  lastFailedCertificateValidation?: Date;
  failedApiKeyValidationCount: number;
  lastFailedApiKeyValidation?: Date;
  lastKnownIpAddress?: string;
}

export interface ResolveAlertRequest {
  resolution: string;
}

export interface SecurityEventFilterResponse {
  id: number;
  eventType: number;
  eventTypeName: string;
  description?: string;
  descriptionMatchType: number;
  descriptionMatchTypeName: string;
  ipAddress?: string;
  ipAddressMatchType: number;
  ipAddressMatchTypeName: string;
  createdAt: Date;
  createdBy: string;
}

export interface CreateSecurityEventFilterRequest {
  eventType: number;
  description?: string;
  descriptionMatchType: number;
  ipAddress?: string;
  ipAddressMatchType: number;
  deleteExistingEvents: boolean;
}

export interface SecurityEventFilterApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

export function getMatchTypeName(matchType: MatchType): string {
  switch (matchType) {
    case MatchType.ExactMatch:
      return 'Přesná shoda';
    case MatchType.Contains:
      return 'Obsahuje';
    case MatchType.StartsWith:
      return 'Začíná na';
    case MatchType.EndsWith:
      return 'Končí na';
    default:
      return 'Neznámý';
  }
}
