{"ast": null, "code": "import { MatchType } from '../models/security.model';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SharedModule } from '../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/security.service\";\nimport * as i2 from \"../services/modal.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../shared/components/resolve-alert-modal/resolve-alert-modal.component\";\nfunction SecurityComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction SecurityComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 ne\\u00FAsp\\u011B\\u0161n\\u00E9 pokusy o p\\u0159ipojen\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_8_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r12.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", stat_r12.failedCertificateValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r12.failedCertificateValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 8, stat_r12.lastFailedCertificateValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", stat_r12.failedApiKeyValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r12.failedApiKeyValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 11, stat_r12.lastFailedApiKeyValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r12.lastKnownIpAddress || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\")(4, \"th\", 41);\n    i0.ɵɵtext(5, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 41);\n    i0.ɵɵtext(7, \"Ne\\u00FAsp. validace cert.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 41);\n    i0.ɵɵtext(9, \"Posledn\\u00ED ne\\u00FAsp. validace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 41);\n    i0.ɵɵtext(11, \"Ne\\u00FAsp. validace API kl\\u00ED\\u010De\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 41);\n    i0.ɵɵtext(13, \"Posledn\\u00ED ne\\u00FAsp. validace API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 41);\n    i0.ɵɵtext(15, \"Posledn\\u00ED IP\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_9_div_8_tr_17_Template, 17, 14, \"tr\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.failedConnectionStats);\n  }\n}\nfunction SecurityComponent_div_9_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_19_tr_19_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_19_tr_19_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const event_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.openResolveSecurityEventModal(event_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_19_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 41)(2, \"span\", 43);\n    i0.ɵɵelement(3, \"i\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 41)(6, \"span\", 43);\n    i0.ɵɵelement(7, \"i\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 41);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 41);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 44);\n    i0.ɵɵtemplate(20, SecurityComponent_div_9_div_19_tr_19_button_20_Template, 2, 0, \"button\", 45);\n    i0.ɵɵelementStart(21, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_19_tr_19_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const event_r14 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.openBlockEventModal(event_r14));\n    });\n    i0.ɵɵelement(22, \"i\", 47);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const event_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getSeverityClass(event_r14.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getSeverityIcon(event_r14.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getSeverityText(event_r14.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getEventTypeClass(event_r14.eventType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getEventTypeIcon(event_r14.eventType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.getEventTypeText(event_r14.eventType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 11, event_r14.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r14.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r14.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r14.username || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !event_r14.isResolved && !event_r14.isIgnored);\n  }\n}\nfunction SecurityComponent_div_9_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 41);\n    i0.ɵɵtext(13, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_19_tr_19_Template, 23, 14, \"tr\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.securityEvents);\n  }\n}\nfunction SecurityComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 30)(2, \"div\", 31)(3, \"h5\", 32);\n    i0.ɵɵelement(4, \"i\", 33);\n    i0.ɵɵtext(5, \"Statistiky ne\\u00FAsp\\u011B\\u0161n\\u00FDch p\\u0159ipojen\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtemplate(7, SecurityComponent_div_9_div_7_Template, 2, 0, \"div\", 24);\n    i0.ɵɵtemplate(8, SecurityComponent_div_9_div_8_Template, 18, 1, \"div\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 30)(10, \"div\", 31)(11, \"h5\", 32);\n    i0.ɵɵelement(12, \"i\", 35);\n    i0.ɵɵtext(13, \"Ned\\u00E1vn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.openManageFiltersModal());\n    });\n    i0.ɵɵelement(15, \"i\", 37);\n    i0.ɵɵtext(16, \"Spravovat filtry \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 34);\n    i0.ɵɵtemplate(18, SecurityComponent_div_9_div_18_Template, 2, 0, \"div\", 24);\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_19_Template, 20, 1, \"div\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length > 0);\n  }\n}\nfunction SecurityComponent_div_19_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"label\", 69)(2, \"strong\");\n    i0.ɵɵtext(3, \"IP adresa:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 55)(5, \"input\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_div_37_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.filterIpAddress = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_div_37_Template_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.ipAddressMatchType = $event);\n    });\n    i0.ɵɵelementStart(7, \"option\", 58);\n    i0.ɵɵtext(8, \"P\\u0159esn\\u00E1 shoda\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"option\", 58);\n    i0.ɵɵtext(10, \"Obsahuje\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 58);\n    i0.ɵɵtext(12, \"Za\\u010D\\u00EDn\\u00E1 na\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 58);\n    i0.ɵɵtext(14, \"Kon\\u010D\\u00ED na\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"small\", 59);\n    i0.ɵɵtext(16, \"Vyberte, jak m\\u00E1 b\\u00FDt IP adresa porovn\\u00E1v\\u00E1na\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.filterIpAddress);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.ipAddressMatchType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 3);\n  }\n}\nfunction SecurityComponent_div_19_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"small\");\n    i0.ɵɵtext(2, \"Filtr bude aplikov\\u00E1n na v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Pozor!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Vytvo\\u0159\\u00EDte filtr, kter\\u00FD zablokuje podobn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti v budoucnu. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\");\n    i0.ɵɵtext(7, \"N\\u00E1hled filtru:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 52)(9, \"div\", 34)(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Typ ud\\u00E1losti:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 53)(15, \"label\", 54)(16, \"strong\");\n    i0.ɵɵtext(17, \"Popis:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 55)(19, \"input\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.selectedEvent.description = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"select\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_select_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.descriptionMatchType = $event);\n    });\n    i0.ɵɵelementStart(21, \"option\", 58);\n    i0.ɵɵtext(22, \"P\\u0159esn\\u00E1 shoda\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 58);\n    i0.ɵɵtext(24, \"Obsahuje\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 58);\n    i0.ɵɵtext(26, \"Za\\u010D\\u00EDn\\u00E1 na\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"option\", 58);\n    i0.ɵɵtext(28, \"Kon\\u010D\\u00ED na\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"small\", 59);\n    i0.ɵɵtext(30, \"Vyberte, jak m\\u00E1 b\\u00FDt popis porovn\\u00E1v\\u00E1n\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 53)(32, \"div\", 60)(33, \"input\", 61);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_input_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.applyToAllIps = $event);\n    })(\"change\", function SecurityComponent_div_19_Template_input_change_33_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onApplyToAllIpsChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"label\", 62)(35, \"strong\");\n    i0.ɵɵtext(36, \"Pou\\u017E\\u00EDt pro v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, SecurityComponent_div_19_div_37_Template, 17, 6, \"div\", 63);\n    i0.ɵɵtemplate(38, SecurityComponent_div_19_div_38_Template, 3, 0, \"div\", 64);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 65)(40, \"input\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_input_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.deleteExistingEvents = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"label\", 67);\n    i0.ɵɵtext(42, \" Smazat tak\\u00E9 existuj\\u00EDc\\u00ED ud\\u00E1losti odpov\\u00EDdaj\\u00EDc\\u00ED tomuto vzoru \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getEventTypeText(ctx_r3.selectedEvent.eventType), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.selectedEvent.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.descriptionMatchType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.applyToAllIps);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.applyToAllIps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.applyToAllIps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.deleteExistingEvents);\n  }\n}\nfunction SecurityComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 filtry nejsou nastaveny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_36_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"small\", 74);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"small\", 74);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_36_tr_21_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const filter_r35 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.confirmDeleteFilter(filter_r35));\n    });\n    i0.ɵɵelement(20, \"i\", 76);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const filter_r35 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r35.eventTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r35.description || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r35.descriptionMatchTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r35.ipAddress || \"V\\u0161echny IP\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r35.ipAddressMatchTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 7, filter_r35.createdAt, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r35.createdBy);\n  }\n}\nfunction SecurityComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\")(4, \"th\", 41);\n    i0.ɵɵtext(5, \"Typ ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 41);\n    i0.ɵɵtext(7, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 41);\n    i0.ɵɵtext(9, \"Typ porovn\\u00E1v\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 41);\n    i0.ɵɵtext(11, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 41);\n    i0.ɵɵtext(13, \"Typ porovn\\u00E1v\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 41);\n    i0.ɵɵtext(15, \"Vytvo\\u0159eno\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 41);\n    i0.ɵɵtext(17, \"Vytvo\\u0159il\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 41);\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, SecurityComponent_div_36_tr_21_Template, 21, 10, \"tr\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.securityEventFilters);\n  }\n}\nexport let SecurityComponent = /*#__PURE__*/(() => {\n  class SecurityComponent {\n    constructor(securityService, modalService, toastr) {\n      this.securityService = securityService;\n      this.modalService = modalService;\n      this.toastr = toastr;\n      this.loading = false;\n      this.error = null;\n      this.securityEvents = [];\n      this.failedConnectionStats = [];\n      this.selectedSecurityEvent = null;\n      // Security Event Filters\n      this.securityEventFilters = [];\n      this.selectedEvent = null;\n      this.deleteExistingEvents = false;\n      this.loadingFilters = false;\n      this.applyToAllIps = true; // Výchozí stav - použít pro všechny IP\n      this.filterIpAddress = '';\n      this.descriptionMatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro popis\n      this.ipAddressMatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro IP adresu\n    }\n\n    ngOnInit() {\n      this.loadSecurityDashboard();\n    }\n    loadSecurityDashboard() {\n      this.loading = true;\n      this.error = null;\n      this.securityService.getSecurityDashboard().subscribe({\n        next: response => {\n          // Převod časů na lokální čas\n          this.securityEvents = response.recentSecurityEvents.map(event => ({\n            ...event,\n            timestamp: new Date(event.timestamp)\n          }));\n          this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n            ...stat,\n            lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n            lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n          }));\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading security dashboard', err);\n          this.error = 'Chyba při načítání bezpečnostního dashboardu';\n          this.loading = false;\n        }\n      });\n    }\n    openResolveSecurityEventModal(securityEvent) {\n      this.selectedSecurityEvent = securityEvent;\n      this.modalService.open('resolveSecurityEventModal');\n    }\n    onResolveSecurityEvent(resolution) {\n      if (!this.selectedSecurityEvent) {\n        return;\n      }\n      this.securityService.resolveSecurityEvent(this.selectedSecurityEvent.id, resolution).subscribe({\n        next: () => {\n          this.toastr.success('Bezpečnostní událost byla úspěšně vyřešena', 'Úspěch');\n          this.loadSecurityDashboard();\n        },\n        error: err => {\n          console.error('Error resolving security event', err);\n          this.error = 'Chyba při řešení bezpečnostní události';\n          this.toastr.error('Chyba při řešení bezpečnostní události', 'Chyba');\n        }\n      });\n    }\n    onIgnoreSecurityEvent() {\n      if (!this.selectedSecurityEvent) {\n        return;\n      }\n      this.securityService.ignoreSecurityEvent(this.selectedSecurityEvent.id).subscribe({\n        next: () => {\n          this.toastr.success('Bezpečnostní událost byla ignorována', 'Úspěch');\n          this.loadSecurityDashboard();\n        },\n        error: err => {\n          console.error('Error ignoring security event', err);\n          this.error = 'Chyba při ignorování bezpečnostní události';\n          this.toastr.error('Chyba při ignorování bezpečnostní události', 'Chyba');\n        }\n      });\n    }\n    getSeverityClass(severity) {\n      switch (severity) {\n        case 5:\n          return 'text-danger fw-bold';\n        case 4:\n          return 'text-danger';\n        case 3:\n          return 'text-warning';\n        case 2:\n          return 'text-info';\n        case 1:\n          return 'text-success';\n        default:\n          return 'text-muted';\n      }\n    }\n    getSeverityIcon(severity) {\n      switch (severity) {\n        case 5:\n        case 4:\n          return 'bi-exclamation-triangle-fill';\n        case 3:\n          return 'bi-exclamation-circle-fill';\n        case 2:\n          return 'bi-info-circle-fill';\n        default:\n          return 'bi-check-circle-fill';\n      }\n    }\n    getSeverityText(severity) {\n      switch (severity) {\n        case 5:\n          return 'Kritická';\n        case 4:\n          return 'Vysoká';\n        case 3:\n          return 'Střední';\n        case 2:\n          return 'Nízká';\n        case 1:\n          return 'Informační';\n        default:\n          return 'Neznámá';\n      }\n    }\n    getEventTypeClass(eventType) {\n      switch (eventType) {\n        case 'SuspiciousActivity':\n          return 'text-danger';\n        case 'CertificateValidationFailure':\n          return 'text-warning';\n        case 'IpBlocked':\n          return 'text-danger';\n        case 'FailedAccessAttempt':\n          return 'text-warning';\n        default:\n          return 'text-info';\n      }\n    }\n    getEventTypeIcon(eventType) {\n      switch (eventType) {\n        case 'SuspiciousActivity':\n          return 'bi-exclamation-triangle-fill';\n        case 'CertificateValidationFailure':\n          return 'bi-shield-exclamation';\n        case 'IpBlocked':\n          return 'bi-slash-circle-fill';\n        case 'FailedAccessAttempt':\n          return 'bi-x-circle-fill';\n        case 'ApiKeyMisuse':\n          return 'bi-key-fill';\n        case 'UnauthorizedAccess':\n          return 'bi-shield-x';\n        case 'Other':\n          return 'bi-question-circle-fill';\n        default:\n          return 'bi-info-circle-fill';\n      }\n    }\n    getEventTypeText(eventType) {\n      switch (eventType) {\n        case 'FailedAccessAttempt':\n          return 'Neúspěšný pokus o přístup';\n        case 'SuspiciousActivity':\n          return 'Podezřelá aktivita';\n        case 'IpBlocked':\n          return 'Blokovaná IP adresa';\n        case 'CertificateValidationFailure':\n          return 'Selhání validace certifikátu';\n        case 'ApiKeyMisuse':\n          return 'Nesprávné použití API klíče';\n        case 'UnauthorizedAccess':\n          return 'Neautorizovaný přístup';\n        case 'Other':\n          return 'Ostatní';\n        default:\n          return eventType;\n      }\n    }\n    // Security Event Filter methods\n    openBlockEventModal(event) {\n      this.selectedEvent = event;\n      this.deleteExistingEvents = false;\n      this.applyToAllIps = true; // Výchozí stav\n      this.filterIpAddress = event.ipAddress || '';\n      const modal = new bootstrap.Modal(document.getElementById('blockEventModal'));\n      modal.show();\n    }\n    onApplyToAllIpsChange() {\n      if (!this.applyToAllIps && this.selectedEvent) {\n        // Pokud se vypne \"pro všechny IP\", nastav IP adresu z události\n        this.filterIpAddress = this.selectedEvent.ipAddress || '';\n      }\n    }\n    confirmBlockEvent() {\n      if (!this.selectedEvent) return;\n      const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);\n      const request = {\n        eventType: eventTypeNumber,\n        description: this.selectedEvent.description,\n        descriptionMatchType: this.descriptionMatchType,\n        ipAddress: this.applyToAllIps ? undefined : this.filterIpAddress,\n        ipAddressMatchType: this.ipAddressMatchType,\n        deleteExistingEvents: this.deleteExistingEvents\n      };\n      this.securityService.createSecurityEventFilter(request).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');\n            this.loadSecurityDashboard(); // Refresh data\n            const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal'));\n            modal?.hide();\n          } else {\n            this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');\n          }\n        },\n        error: error => {\n          console.error('Error creating filter:', error);\n          this.toastr.error('Chyba při vytváření filtru', 'Chyba');\n        }\n      });\n    }\n    openManageFiltersModal() {\n      this.loadSecurityEventFilters();\n      const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal'));\n      modal.show();\n    }\n    loadSecurityEventFilters() {\n      this.loadingFilters = true;\n      this.securityService.getSecurityEventFilters().subscribe({\n        next: response => {\n          if (response.success && response.data) {\n            this.securityEventFilters = response.data.map(filter => ({\n              ...filter,\n              createdAt: new Date(filter.createdAt)\n            }));\n          } else {\n            this.securityEventFilters = [];\n          }\n          this.loadingFilters = false;\n        },\n        error: error => {\n          console.error('Error loading filters:', error);\n          this.toastr.error('Chyba při načítání filtrů', 'Chyba');\n          this.loadingFilters = false;\n        }\n      });\n    }\n    confirmDeleteFilter(filter) {\n      if (confirm(`Opravdu chcete smazat filtr pro \"${filter.eventTypeName}\"?`)) {\n        this.securityService.deleteSecurityEventFilter(filter.id).subscribe({\n          next: response => {\n            if (response.success) {\n              this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');\n              this.loadSecurityEventFilters(); // Refresh filters\n            } else {\n              this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');\n            }\n          },\n          error: error => {\n            console.error('Error deleting filter:', error);\n            this.toastr.error('Chyba při mazání filtru', 'Chyba');\n          }\n        });\n      }\n    }\n    getEventTypeNumber(eventType) {\n      switch (eventType) {\n        case 'FailedAccessAttempt':\n          return 0;\n        case 'SuspiciousActivity':\n          return 1;\n        case 'IpBlocked':\n          return 2;\n        case 'CertificateValidationFailure':\n          return 3;\n        case 'ApiKeyMisuse':\n          return 4;\n        case 'UnauthorizedAccess':\n          return 5;\n        case 'Other':\n          return 6;\n        default:\n          return 6;\n        // Other\n      }\n    }\n\n    static {\n      this.ɵfac = function SecurityComponent_Factory(t) {\n        return new (t || SecurityComponent)(i0.ɵɵdirectiveInject(i1.SecurityService), i0.ɵɵdirectiveInject(i2.ModalService), i0.ɵɵdirectiveInject(i3.ToastrService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SecurityComponent,\n        selectors: [[\"app-security\"]],\n        standalone: true,\n        features: [i0.ɵɵStandaloneFeature],\n        decls: 40,\n        vars: 8,\n        consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"modalId\", \"resolveSecurityEventModal\", \"entityType\", \"securityEvent\", 3, \"securityEvent\", \"resolve\", \"ignore\", \"close\"], [\"id\", \"blockEventModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"blockEventModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-warning\", \"text-dark\"], [\"id\", \"blockEventModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\", \"me-1\"], [\"id\", \"manageFiltersModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"manageFiltersModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [1, \"modal-header\"], [\"id\", \"manageFiltersModalLabel\", 1, \"modal-title\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"card-body\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-funnel\", \"me-1\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"text-nowrap\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", \"class\", \"btn btn-outline-primary btn-sm\", \"title\", \"Vy\\u0159e\\u0161it\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Blokovat ud\\u00E1lost\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\"], [\"type\", \"button\", \"title\", \"Vy\\u0159e\\u0161it\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-check-circle\"], [1, \"alert\", \"alert-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"card\"], [1, \"mb-3\"], [1, \"form-label\"], [1, \"input-group\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"form-select\", 2, \"max-width\", \"150px\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\"], [1, \"form-text\", \"text-muted\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"applyToAllIps\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"applyToAllIps\", 1, \"form-check-label\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"mt-2 text-muted\", 4, \"ngIf\"], [1, \"form-check\", \"mt-3\"], [\"type\", \"checkbox\", \"id\", \"deleteExistingEvents\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"deleteExistingEvents\", 1, \"form-check-label\"], [1, \"mt-2\"], [\"for\", \"filterIpAddress\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"filterIpAddress\", \"placeholder\", \"Zadejte IP adresu\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mt-2\", \"text-muted\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"text-muted\"], [\"type\", \"button\", \"title\", \"Smazat filtr\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-trash\"]],\n        template: function SecurityComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n            i0.ɵɵtext(3, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(4, \"button\", 2);\n            i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_4_listener() {\n              return ctx.loadSecurityDashboard();\n            });\n            i0.ɵɵelement(5, \"i\", 3);\n            i0.ɵɵtext(6, \"Aktualizovat \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(7, SecurityComponent_div_7_Template, 4, 0, \"div\", 4);\n            i0.ɵɵtemplate(8, SecurityComponent_div_8_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(9, SecurityComponent_div_9_Template, 20, 4, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(10, \"app-resolve-alert-modal\", 7);\n            i0.ɵɵlistener(\"resolve\", function SecurityComponent_Template_app_resolve_alert_modal_resolve_10_listener($event) {\n              return ctx.onResolveSecurityEvent($event);\n            })(\"ignore\", function SecurityComponent_Template_app_resolve_alert_modal_ignore_10_listener() {\n              return ctx.onIgnoreSecurityEvent();\n            })(\"close\", function SecurityComponent_Template_app_resolve_alert_modal_close_10_listener() {\n              return ctx.selectedSecurityEvent = null;\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"div\", 11)(15, \"h5\", 12);\n            i0.ɵɵtext(16, \"Blokovat bezpe\\u010Dnostn\\u00ED ud\\u00E1lost\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(17, \"button\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(18, \"div\", 14);\n            i0.ɵɵtemplate(19, SecurityComponent_div_19_Template, 43, 11, \"div\", 6);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(20, \"div\", 15)(21, \"button\", 16);\n            i0.ɵɵtext(22, \"Zru\\u0161it\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_23_listener() {\n              return ctx.confirmBlockEvent();\n            });\n            i0.ɵɵelement(24, \"i\", 18);\n            i0.ɵɵtext(25, \"Blokovat ud\\u00E1lost \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20)(28, \"div\", 10)(29, \"div\", 21)(30, \"h5\", 22);\n            i0.ɵɵtext(31, \"Spravovat filtry bezpe\\u010Dnostn\\u00EDch ud\\u00E1lost\\u00ED\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(32, \"button\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(33, \"div\", 14);\n            i0.ɵɵtemplate(34, SecurityComponent_div_34_Template, 4, 0, \"div\", 23);\n            i0.ɵɵtemplate(35, SecurityComponent_div_35_Template, 2, 0, \"div\", 24);\n            i0.ɵɵtemplate(36, SecurityComponent_div_36_Template, 22, 1, \"div\", 25);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\", 15)(38, \"button\", 16);\n            i0.ɵɵtext(39, \"Zav\\u0159\\u00EDt\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"securityEvent\", ctx.selectedSecurityEvent);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.selectedEvent);\n            i0.ɵɵadvance(15);\n            i0.ɵɵproperty(\"ngIf\", ctx.loadingFilters);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length === 0);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length > 0);\n          }\n        },\n        dependencies: [LocalDatePipe, CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, FormsModule, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, ReactiveFormsModule, SharedModule, i6.ResolveAlertModalComponent],\n        styles: [\".card[_ngcontent-%COMP%]{box-shadow:0 .125rem .25rem #00000013;border-radius:.5rem;border:1px solid rgba(0,0,0,.125)}.card-header[_ngcontent-%COMP%]{background-color:#00000008;border-bottom:1px solid rgba(0,0,0,.125)}.table[_ngcontent-%COMP%]{margin-bottom:0}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top:none;font-weight:600}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{padding:.75rem 1rem;vertical-align:middle}.btn-sm[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem}.alert[_ngcontent-%COMP%]{margin-bottom:1rem;border-radius:.25rem}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-info[_ngcontent-%COMP%]{color:#0dcaf0!important}.text-success[_ngcontent-%COMP%]{color:#198754!important}@media (prefers-color-scheme: dark){.card[_ngcontent-%COMP%]{background-color:#2b3035;border-color:#373b3e}.table[_ngcontent-%COMP%]{color:#e9ecef}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-bottom-color:#373b3e}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{border-top-color:#373b3e}.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#ffffff13}.alert-info[_ngcontent-%COMP%]{background-color:#0d3b66;border-color:#0d3b66;color:#e9ecef}}\"]\n      });\n    }\n  }\n  return SecurityComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}