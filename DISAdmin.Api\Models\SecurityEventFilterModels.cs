using DISAdmin.Core.Data.Entities;

namespace DISAdmin.Api.Models;

public class SecurityEventFilterResponse
{
    public int Id { get; set; }
    public int EventType { get; set; }
    public string EventTypeName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public int DescriptionMatchType { get; set; }
    public string DescriptionMatchTypeName { get; set; } = string.Empty;
    public string? IpAddress { get; set; }
    public int IpAddressMatchType { get; set; }
    public string IpAddressMatchTypeName { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public string CreatedBy { get; set; } = string.Empty;
}

public class CreateSecurityEventFilterRequest
{
    public int EventType { get; set; }
    public string? Description { get; set; }
    public int DescriptionMatchType { get; set; } = 0; // ExactMatch jako výchozí
    public string? IpAddress { get; set; }
    public int IpAddressMatchType { get; set; } = 0; // ExactMatch jako výchozí
    public bool DeleteExistingEvents { get; set; } = false;
}

public class DeleteEventsByPatternRequest
{
    public int EventType { get; set; }
    public string? Description { get; set; }
    public int DescriptionMatchType { get; set; } = 0; // ExactMatch jako výchozí
    public string? IpAddress { get; set; }
    public int IpAddressMatchType { get; set; } = 0; // ExactMatch jako výchozí
}

public class SecurityEventFilterApiResponse<T>
{
    public bool Success { get; set; }
    public T? Data { get; set; }
    public string? Message { get; set; }
}
