namespace DISAdmin.Core.Data.Entities;

/// <summary>
/// Typ porovnávání pro filtrování bezpečnostních událostí
/// </summary>
public enum MatchType
{
    /// <summary>
    /// Přesná shoda - text se musí přesně shodovat
    /// </summary>
    ExactMatch = 0,

    /// <summary>
    /// Obsahuje - text musí obsahovat zadaný řetězec
    /// </summary>
    Contains = 1,

    /// <summary>
    /// Začíná na - text musí začínat zadaným řetězcem
    /// </summary>
    StartsWith = 2,

    /// <summary>
    /// Končí na - text musí končit zadaným řetězcem
    /// </summary>
    EndsWith = 3
}
