"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[154],{4154:(Te,Z,p)=>{p.r(Z),p.d(Z,{PerformanceModule:()=>Ne});var _=p(6895),P=p(6123),w=p(433),D=p(3192),x=p(8374),e=p(1571),C=p(529),N=p(2843);let I=(()=>{const d=class{constructor(t){this.http=t,this.baseUrl="/api/performance"}getInstancesComparison(t,o,n,a,s){let l=new C.LE;return t.forEach(c=>{l=l.append("instanceIds",c.toString())}),o&&(l=l.append("methodName",o)),n&&(l=l.append("className",n)),a&&(l=l.append("fromDate",a.toISOString())),s&&(l=l.append("toDate",s.toISOString())),this.http.get(`${this.baseUrl}/instances-comparison`,{params:l})}getVersionsComparison(t,o,n,a,s){let l=(new C.LE).set("instanceId",t.toString());return o&&(l=l.append("methodName",o)),n&&(l=l.append("className",n)),a&&(l=l.append("fromDate",a.toISOString())),s&&(l=l.append("toDate",s.toISOString())),this.http.get(`${this.baseUrl}/versions-comparison`,{params:l})}getAggregatedPerformance(t,o,n){let a=new C.LE;return t&&(a=a.append("instanceId",t.toString())),o&&(a=a.append("fromDate",o.toISOString())),n&&(a=a.append("toDate",n.toISOString())),this.http.get(`${this.baseUrl}/aggregated-performance`,{params:a})}getSlowestMethods(t,o,n,a=10){let s=(new C.LE).set("limit",a.toString());return t&&(s=s.append("instanceId",t.toString())),o&&(s=s.append("fromDate",o.toISOString())),n&&(s=s.append("toDate",n.toISOString())),this.http.get(`${this.baseUrl}/slowest-methods`,{params:s})}getMostCalledMethods(t,o,n,a=10){let s=(new C.LE).set("limit",a.toString());return t&&(s=s.append("instanceId",t.toString())),o&&(s=s.append("fromDate",o.toISOString())),n&&(s=s.append("toDate",n.toISOString())),this.http.get(`${this.baseUrl}/most-called-methods`,{params:s})}getMethodDetail(t,o,n,a,s){if(console.log("getMethodDetail - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,className:o,methodName:n,fromDate:a,toDate:s}),!o||!n)return console.error("getMethodDetail - Nejsou zad\xe1ny povinn\xe9 parametry (t\u0159\xedda nebo metoda)"),(0,N._)(()=>new Error("Nejsou zad\xe1ny povinn\xe9 parametry (t\u0159\xedda nebo metoda)"));let l=(new C.LE).set("className",o).set("methodName",n);return null!==t&&0!==t&&"0"!==t?.toString()?(l=l.set("instanceId",t.toString()),console.log("getMethodDetail - P\u0159id\xe1v\xe1m instanceId do parametr\u016f:",t)):console.log('getMethodDetail - Nepos\xedl\xe1m instanceId, proto\u017ee je vybr\xe1na mo\u017enost "V\u0161echny instance"'),a&&(l=l.append("fromDate",a.toISOString())),s&&(l=l.append("toDate",s.toISOString())),this.http.get(`${this.baseUrl}/method-detail`,{params:l})}getAllMethods(t,o,n){let a=new C.LE;return t&&(a=a.append("instanceId",t.toString())),o&&(a=a.append("fromDate",o.toISOString())),n&&(a=a.append("toDate",n.toISOString())),this.http.get(`${this.baseUrl}/all-methods`,{params:a})}};let r=d;return d.\u0275fac=function(o){return new(o||d)(e.LFG(C.eN))},d.\u0275prov=e.Yz7({token:d,factory:d.\u0275fac,providedIn:"root"}),r})();var T=p(9523),j=p(9991),S=p(9060),q=p(3119);const E=["aggregatedPerformanceChart"],V=["instancesComparisonChart"],O=["versionsComparisonChart"],U=["methodDetailChart"];function J(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div",5),e._uU(1),e.TgZ(2,"button",6),e.NdJ("click",function(){e.CHM(i);const o=e.oxw();return e.KtG(o.error=null)}),e.qZA()()}if(2&r){const i=e.oxw();e.xp6(1),e.hij(" ",i.error," ")}}function L(r,d){1&r&&(e.TgZ(0,"div",9)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Na\u010d\xedt\xe1n\xed dat..."),e.qZA()())}function R(r,d){1&r&&(e.TgZ(0,"div",31)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Zpracov\xe1n\xed dat grafu..."),e.qZA()())}function $(r,d){1&r&&(e.TgZ(0,"div",32),e._UZ(1,"i",33),e.TgZ(2,"p"),e._uU(3,"Nejsou k dispozici \u017e\xe1dn\xe1 data pro zobrazen\xed grafu"),e.qZA()())}function Q(r,d){1&r&&e._UZ(0,"canvas",34,35)}function G(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"tr",36)(1,"td"),e._uU(2),e.qZA(),e.TgZ(3,"td"),e._uU(4),e.qZA(),e.TgZ(5,"td"),e._uU(6),e.qZA(),e.TgZ(7,"td"),e._uU(8),e.qZA(),e.TgZ(9,"td"),e._uU(10),e.ALo(11,"number"),e.qZA(),e.TgZ(12,"td"),e._uU(13),e.ALo(14,"number"),e.qZA(),e.TgZ(15,"td"),e._uU(16),e.ALo(17,"number"),e.qZA(),e.TgZ(18,"td"),e._uU(19),e.ALo(20,"number"),e.qZA(),e.TgZ(21,"td"),e._uU(22),e.ALo(23,"number"),e.qZA(),e.TgZ(24,"td")(25,"button",37),e.NdJ("click",function(){const n=e.CHM(i).$implicit,a=e.oxw(3);return e.KtG(a.showMethodDetail(n))}),e._UZ(26,"i",38),e.qZA()()()}if(2&r){const i=d.$implicit;e.xp6(2),e.Oqu(i.instanceName),e.xp6(2),e.Oqu(i.className),e.xp6(2),e.Oqu(i.methodName),e.xp6(2),e.Oqu(i.totalCount),e.xp6(2),e.Oqu(e.xi3(11,9,i.min,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(14,12,i.max,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(17,15,i.avg,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(20,18,i.median,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(23,21,i.percentil95,"1.0-2"))}}function Y(r,d){1&r&&(e.TgZ(0,"tr")(1,"td",39),e._uU(2,"\u017d\xe1dn\xe9 metody k zobrazen\xed"),e.qZA()())}function K(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"div",13)(2,"div",14)(3,"h5",15),e._uU(4,"Agregovan\xfd v\xfdkon v \u010dase"),e.qZA(),e.TgZ(5,"button",16),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.openFullscreenChart(o.aggregatedPerformanceChart,"Agregovan\xfd v\xfdkon v \u010dase"))}),e._UZ(6,"i",17),e.qZA()(),e.TgZ(7,"div",18)(8,"div",19),e.YNc(9,R,6,0,"div",20),e.YNc(10,$,4,0,"div",21),e.YNc(11,Q,2,0,"canvas",22),e.qZA()()(),e.TgZ(12,"div",13)(13,"div",14)(14,"h5",23),e._uU(15,"Nejpomalej\u0161\xed metody"),e.qZA()(),e.TgZ(16,"div",18)(17,"div",24)(18,"table",25)(19,"thead",26)(20,"tr",27)(21,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("instanceName"))}),e._uU(22," Instance "),e._UZ(23,"i",29),e.qZA(),e.TgZ(24,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("className"))}),e._uU(25," T\u0159\xedda "),e._UZ(26,"i",29),e.qZA(),e.TgZ(27,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("methodName"))}),e._uU(28," Metoda "),e._UZ(29,"i",29),e.qZA(),e.TgZ(30,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("totalCount"))}),e._uU(31," Po\u010det vol\xe1n\xed "),e._UZ(32,"i",29),e.qZA(),e.TgZ(33,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("min"))}),e._uU(34," Min (ms) "),e._UZ(35,"i",29),e.qZA(),e.TgZ(36,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("max"))}),e._uU(37," Max (ms) "),e._UZ(38,"i",29),e.qZA(),e.TgZ(39,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("avg"))}),e._uU(40," Pr\u016fm\u011br (ms) "),e._UZ(41,"i",29),e.qZA(),e.TgZ(42,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("median"))}),e._uU(43," Medi\xe1n (ms) "),e._UZ(44,"i",29),e.qZA(),e.TgZ(45,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("percentil95"))}),e._uU(46," 95. percentil (ms) "),e._UZ(47,"i",29),e.qZA(),e.TgZ(48,"th"),e._uU(49,"Akce"),e.qZA()()(),e.TgZ(50,"tbody"),e.YNc(51,G,27,24,"tr",30),e.YNc(52,Y,3,0,"tr",4),e.qZA()()()()()()}if(2&r){const i=e.oxw(2);e.xp6(9),e.Q6J("ngIf",i.renderingAggregatedChart),e.xp6(1),e.Q6J("ngIf",!i.hasAggregatedChartData),e.xp6(1),e.Q6J("ngIf",i.hasAggregatedChartData),e.xp6(12),e.ekj("bi-sort-up","instanceName"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","instanceName"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","instanceName"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","className"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","className"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","className"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","methodName"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","methodName"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","methodName"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","totalCount"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","totalCount"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","totalCount"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","min"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","min"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","min"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","max"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","max"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","max"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","avg"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","avg"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","avg"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","median"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","median"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","median"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","percentil95"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","percentil95"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","percentil95"!==i.sortColumn),e.xp6(4),e.Q6J("ngForOf",i.slowestMethods),e.xp6(1),e.Q6J("ngIf",!i.slowestMethods||0===i.slowestMethods.length)}}function H(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"app-advanced-filter",7),e.NdJ("filterChange",function(o){e.CHM(i);const n=e.oxw();return e.KtG(n.onAggregatedFilterChange(o))}),e.qZA(),e.YNc(2,L,6,0,"div",8),e.YNc(3,K,53,59,"div",4),e.qZA()}if(2&r){const i=e.oxw();e.xp6(1),e.Q6J("entityType","performance_aggregated")("fields",i.aggregatedFilterFields),e.xp6(1),e.Q6J("ngIf",i.loading),e.xp6(1),e.Q6J("ngIf",!i.loading)}}function W(r,d){1&r&&(e.TgZ(0,"div",9)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Na\u010d\xedt\xe1n\xed dat..."),e.qZA()())}function B(r,d){1&r&&(e.TgZ(0,"div",31)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Zpracov\xe1n\xed dat grafu..."),e.qZA()())}function X(r,d){1&r&&(e.TgZ(0,"div",32),e._UZ(1,"i",33),e.TgZ(2,"p"),e._uU(3,"Nejsou k dispozici \u017e\xe1dn\xe1 data pro zobrazen\xed grafu"),e.qZA()())}function ee(r,d){1&r&&e._UZ(0,"canvas",34,41)}function te(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"div",13)(2,"div",14)(3,"h5",40),e._uU(4,"Srovn\xe1n\xed v\xfdkonu metod mezi instancemi"),e.qZA(),e.TgZ(5,"button",16),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.openFullscreenChart(o.instancesComparisonChart,"Srovn\xe1n\xed v\xfdkonu metod mezi instancemi"))}),e._UZ(6,"i",17),e.qZA()(),e.TgZ(7,"div",18)(8,"div",19),e.YNc(9,B,6,0,"div",20),e.YNc(10,X,4,0,"div",21),e.YNc(11,ee,2,0,"canvas",22),e.qZA()()()()}if(2&r){const i=e.oxw(2);e.xp6(9),e.Q6J("ngIf",i.renderingInstancesComparisonChart),e.xp6(1),e.Q6J("ngIf",!i.hasInstancesComparisonChartData),e.xp6(1),e.Q6J("ngIf",i.hasInstancesComparisonChartData)}}function oe(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"app-advanced-filter",7),e.NdJ("filterChange",function(o){e.CHM(i);const n=e.oxw();return e.KtG(n.onInstancesComparisonFilterChange(o))}),e.qZA(),e.YNc(2,W,6,0,"div",8),e.YNc(3,te,12,3,"div",4),e.qZA()}if(2&r){const i=e.oxw();e.xp6(1),e.Q6J("entityType","performance_instances_comparison")("fields",i.instancesComparisonFilterFields),e.xp6(1),e.Q6J("ngIf",i.loading),e.xp6(1),e.Q6J("ngIf",!i.loading)}}function ae(r,d){1&r&&(e.TgZ(0,"div",9)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Na\u010d\xedt\xe1n\xed dat..."),e.qZA()())}function ne(r,d){1&r&&(e.TgZ(0,"div",31)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Zpracov\xe1n\xed dat grafu..."),e.qZA()())}function ie(r,d){1&r&&(e.TgZ(0,"div",32),e._UZ(1,"i",33),e.TgZ(2,"p"),e._uU(3,"Nejsou k dispozici \u017e\xe1dn\xe1 data pro zobrazen\xed grafu"),e.qZA()())}function se(r,d){1&r&&e._UZ(0,"canvas",34,43)}function re(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"div",13)(2,"div",14)(3,"h5",42),e._uU(4,"Srovn\xe1n\xed v\xfdkonu metod mezi verzemi"),e.qZA(),e.TgZ(5,"button",16),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.openFullscreenChart(o.versionsComparisonChart,"Srovn\xe1n\xed v\xfdkonu metod mezi verzemi"))}),e._UZ(6,"i",17),e.qZA()(),e.TgZ(7,"div",18)(8,"div",19),e.YNc(9,ne,6,0,"div",20),e.YNc(10,ie,4,0,"div",21),e.YNc(11,se,2,0,"canvas",22),e.qZA()()()()}if(2&r){const i=e.oxw(2);e.xp6(9),e.Q6J("ngIf",i.renderingVersionsComparisonChart),e.xp6(1),e.Q6J("ngIf",!i.hasVersionsComparisonChartData),e.xp6(1),e.Q6J("ngIf",i.hasVersionsComparisonChartData)}}function le(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"app-advanced-filter",7),e.NdJ("filterChange",function(o){e.CHM(i);const n=e.oxw();return e.KtG(n.onVersionsComparisonFilterChange(o))}),e.qZA(),e.YNc(2,ae,6,0,"div",8),e.YNc(3,re,12,3,"div",4),e.qZA()}if(2&r){const i=e.oxw();e.xp6(1),e.Q6J("entityType","performance_versions_comparison")("fields",i.versionsComparisonFilterFields),e.xp6(1),e.Q6J("ngIf",i.loading),e.xp6(1),e.Q6J("ngIf",!i.loading)}}function de(r,d){1&r&&(e.TgZ(0,"div",9)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Na\u010d\xedt\xe1n\xed dat..."),e.qZA()())}function ce(r,d){1&r&&(e.TgZ(0,"span",48),e._uU(1,"Nejpomalej\u0161\xed metody"),e.qZA())}function me(r,d){1&r&&(e.TgZ(0,"span",49),e._uU(1,"Nej\u010dast\u011bji volan\xe9 metody"),e.qZA())}function he(r,d){1&r&&(e.TgZ(0,"span",50),e._uU(1,"V\u0161echny metody"),e.qZA())}function ge(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"tr",36)(1,"td"),e._uU(2),e.qZA(),e.TgZ(3,"td"),e._uU(4),e.qZA(),e.TgZ(5,"td"),e._uU(6),e.qZA(),e.TgZ(7,"td"),e._uU(8),e.qZA(),e.TgZ(9,"td"),e._uU(10),e.ALo(11,"number"),e.qZA(),e.TgZ(12,"td"),e._uU(13),e.ALo(14,"number"),e.qZA(),e.TgZ(15,"td"),e._uU(16),e.ALo(17,"number"),e.qZA(),e.TgZ(18,"td"),e._uU(19),e.ALo(20,"number"),e.qZA(),e.TgZ(21,"td"),e._uU(22),e.ALo(23,"number"),e.qZA(),e.TgZ(24,"td")(25,"button",37),e.NdJ("click",function(){const n=e.CHM(i).$implicit,a=e.oxw(4);return e.KtG(a.showMethodDetail(n))}),e._UZ(26,"i",38),e.qZA()()()}if(2&r){const i=d.$implicit;e.xp6(2),e.Oqu(i.instanceName),e.xp6(2),e.Oqu(i.className),e.xp6(2),e.Oqu(i.methodName),e.xp6(2),e.Oqu(i.totalCount),e.xp6(2),e.Oqu(e.xi3(11,9,i.min,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(14,12,i.max,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(17,15,i.avg,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(20,18,i.median,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(23,21,i.percentil95,"1.0-2"))}}function pe(r,d){1&r&&(e.TgZ(0,"tr")(1,"td",39),e._uU(2,"\u017d\xe1dn\xe9 metody k zobrazen\xed"),e.qZA()())}function ue(r,d){if(1&r&&(e.ynx(0),e.YNc(1,ge,27,24,"tr",30),e.YNc(2,pe,3,0,"tr",4),e.BQk()),2&r){const i=e.oxw(3);e.xp6(1),e.Q6J("ngForOf",i.slowestMethods),e.xp6(1),e.Q6J("ngIf",!i.slowestMethods||0===i.slowestMethods.length)}}function fe(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"tr",36)(1,"td"),e._uU(2),e.qZA(),e.TgZ(3,"td"),e._uU(4),e.qZA(),e.TgZ(5,"td"),e._uU(6),e.qZA(),e.TgZ(7,"td"),e._uU(8),e.qZA(),e.TgZ(9,"td"),e._uU(10),e.ALo(11,"number"),e.qZA(),e.TgZ(12,"td"),e._uU(13),e.ALo(14,"number"),e.qZA(),e.TgZ(15,"td"),e._uU(16),e.ALo(17,"number"),e.qZA(),e.TgZ(18,"td"),e._uU(19),e.ALo(20,"number"),e.qZA(),e.TgZ(21,"td"),e._uU(22),e.ALo(23,"number"),e.qZA(),e.TgZ(24,"td")(25,"button",37),e.NdJ("click",function(){const n=e.CHM(i).$implicit,a=e.oxw(4);return e.KtG(a.showMethodDetail(n))}),e._UZ(26,"i",38),e.qZA()()()}if(2&r){const i=d.$implicit;e.xp6(2),e.Oqu(i.instanceName),e.xp6(2),e.Oqu(i.className),e.xp6(2),e.Oqu(i.methodName),e.xp6(2),e.Oqu(i.totalCount),e.xp6(2),e.Oqu(e.xi3(11,9,i.min,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(14,12,i.max,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(17,15,i.avg,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(20,18,i.median,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(23,21,i.percentil95,"1.0-2"))}}function ve(r,d){1&r&&(e.TgZ(0,"tr")(1,"td",39),e._uU(2,"\u017d\xe1dn\xe9 metody k zobrazen\xed"),e.qZA()())}function Ce(r,d){if(1&r&&(e.ynx(0),e.YNc(1,fe,27,24,"tr",30),e.YNc(2,ve,3,0,"tr",4),e.BQk()),2&r){const i=e.oxw(3);e.xp6(1),e.Q6J("ngForOf",i.mostCalledMethods),e.xp6(1),e.Q6J("ngIf",!i.mostCalledMethods||0===i.mostCalledMethods.length)}}function De(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"tr",36)(1,"td"),e._uU(2),e.qZA(),e.TgZ(3,"td"),e._uU(4),e.qZA(),e.TgZ(5,"td"),e._uU(6),e.qZA(),e.TgZ(7,"td"),e._uU(8),e.qZA(),e.TgZ(9,"td"),e._uU(10),e.ALo(11,"number"),e.qZA(),e.TgZ(12,"td"),e._uU(13),e.ALo(14,"number"),e.qZA(),e.TgZ(15,"td"),e._uU(16),e.ALo(17,"number"),e.qZA(),e.TgZ(18,"td"),e._uU(19),e.ALo(20,"number"),e.qZA(),e.TgZ(21,"td"),e._uU(22),e.ALo(23,"number"),e.qZA(),e.TgZ(24,"td")(25,"button",37),e.NdJ("click",function(){const n=e.CHM(i).$implicit,a=e.oxw(4);return e.KtG(a.showMethodDetail(n))}),e._UZ(26,"i",38),e.qZA()()()}if(2&r){const i=d.$implicit;e.xp6(2),e.Oqu(i.instanceName),e.xp6(2),e.Oqu(i.className),e.xp6(2),e.Oqu(i.methodName),e.xp6(2),e.Oqu(i.totalCount),e.xp6(2),e.Oqu(e.xi3(11,9,i.min,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(14,12,i.max,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(17,15,i.avg,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(20,18,i.median,"1.0-2")),e.xp6(3),e.Oqu(e.xi3(23,21,i.percentil95,"1.0-2"))}}function _e(r,d){1&r&&(e.TgZ(0,"tr")(1,"td",39),e._uU(2,"\u017d\xe1dn\xe9 metody k zobrazen\xed"),e.qZA()())}function be(r,d){if(1&r&&(e.ynx(0),e.YNc(1,De,27,24,"tr",30),e.YNc(2,_e,3,0,"tr",4),e.BQk()),2&r){const i=e.oxw(3);e.xp6(1),e.Q6J("ngForOf",i.allMethods),e.xp6(1),e.Q6J("ngIf",!i.allMethods||0===i.allMethods.length)}}function ye(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"div",13)(2,"div",14)(3,"h5",44),e.YNc(4,ce,2,0,"span",45),e.YNc(5,me,2,0,"span",46),e.YNc(6,he,2,0,"span",47),e.qZA()(),e.TgZ(7,"div",18)(8,"div",24)(9,"table",25)(10,"thead",26)(11,"tr",27)(12,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("instanceName"))}),e._uU(13," Instance "),e._UZ(14,"i",29),e.qZA(),e.TgZ(15,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("className"))}),e._uU(16," T\u0159\xedda "),e._UZ(17,"i",29),e.qZA(),e.TgZ(18,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("methodName"))}),e._uU(19," Metoda "),e._UZ(20,"i",29),e.qZA(),e.TgZ(21,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("totalCount"))}),e._uU(22," Po\u010det vol\xe1n\xed "),e._UZ(23,"i",29),e.qZA(),e.TgZ(24,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("min"))}),e._uU(25," Min (ms) "),e._UZ(26,"i",29),e.qZA(),e.TgZ(27,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("max"))}),e._uU(28," Max (ms) "),e._UZ(29,"i",29),e.qZA(),e.TgZ(30,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("avg"))}),e._uU(31," Pr\u016fm\u011br (ms) "),e._UZ(32,"i",29),e.qZA(),e.TgZ(33,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("median"))}),e._uU(34," Medi\xe1n (ms) "),e._UZ(35,"i",29),e.qZA(),e.TgZ(36,"th",28),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.onSort("percentil95"))}),e._uU(37," 95. percentil (ms) "),e._UZ(38,"i",29),e.qZA(),e.TgZ(39,"th"),e._uU(40,"Akce"),e.qZA()()(),e.TgZ(41,"tbody"),e.YNc(42,ue,3,2,"ng-container",4),e.YNc(43,Ce,3,2,"ng-container",4),e.YNc(44,be,3,2,"ng-container",4),e.qZA()()()()()()}if(2&r){const i=e.oxw(2);e.xp6(4),e.Q6J("ngIf","slowest"===i.methodsDisplayType),e.xp6(1),e.Q6J("ngIf","most-called"===i.methodsDisplayType),e.xp6(1),e.Q6J("ngIf","all"===i.methodsDisplayType),e.xp6(8),e.ekj("bi-sort-up","instanceName"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","instanceName"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","instanceName"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","className"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","className"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","className"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","methodName"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","methodName"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","methodName"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","totalCount"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","totalCount"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","totalCount"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","min"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","min"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","min"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","max"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","max"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","max"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","avg"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","avg"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","avg"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","median"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","median"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","median"!==i.sortColumn),e.xp6(3),e.ekj("bi-sort-up","percentil95"===i.sortColumn&&"asc"===i.sortDirection)("bi-sort-down","percentil95"===i.sortColumn&&"desc"===i.sortDirection)("bi-sort","percentil95"!==i.sortColumn),e.xp6(4),e.Q6J("ngIf","slowest"===i.methodsDisplayType),e.xp6(1),e.Q6J("ngIf","most-called"===i.methodsDisplayType),e.xp6(1),e.Q6J("ngIf","all"===i.methodsDisplayType)}}function ke(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"app-advanced-filter",7),e.NdJ("filterChange",function(o){e.CHM(i);const n=e.oxw();return e.KtG(n.onMethodsFilterChange(o))}),e.qZA(),e.YNc(2,de,6,0,"div",8),e.YNc(3,ye,45,60,"div",4),e.qZA()}if(2&r){const i=e.oxw();e.xp6(1),e.Q6J("entityType","performance_methods")("fields",i.methodsFilterFields),e.xp6(1),e.Q6J("ngIf",i.loading),e.xp6(1),e.Q6J("ngIf",!i.loading)}}function Ae(r,d){1&r&&(e.TgZ(0,"div",9)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Na\u010d\xedt\xe1n\xed dat..."),e.qZA()())}function xe(r,d){1&r&&(e.TgZ(0,"div",31)(1,"div",10)(2,"span",11),e._uU(3,"Na\u010d\xedt\xe1n\xed..."),e.qZA()(),e.TgZ(4,"p",12),e._uU(5,"Zpracov\xe1n\xed dat grafu..."),e.qZA()())}function Fe(r,d){1&r&&(e.TgZ(0,"div",32),e._UZ(1,"i",33),e.TgZ(2,"p"),e._uU(3,"Nejsou k dispozici \u017e\xe1dn\xe1 data pro zobrazen\xed grafu"),e.qZA()())}function Me(r,d){1&r&&e._UZ(0,"canvas",34,62)}function Ze(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"div",51)(2,"div",52)(3,"div",53)(4,"div",54)(5,"h5",55),e._uU(6,"Pr\u016fm\u011brn\xe1 doba odezvy"),e.qZA(),e.TgZ(7,"p",56),e._uU(8),e.ALo(9,"number"),e.qZA(),e.TgZ(10,"p",57),e._uU(11,"ms"),e.qZA()()()(),e.TgZ(12,"div",52)(13,"div",53)(14,"div",54)(15,"h5",58),e._uU(16,"Medi\xe1n doby odezvy"),e.qZA(),e.TgZ(17,"p",56),e._uU(18),e.ALo(19,"number"),e.qZA(),e.TgZ(20,"p",57),e._uU(21,"ms"),e.qZA()()()(),e.TgZ(22,"div",52)(23,"div",53)(24,"div",54)(25,"h5",59),e._uU(26,"95. percentil"),e.qZA(),e.TgZ(27,"p",56),e._uU(28),e.ALo(29,"number"),e.qZA(),e.TgZ(30,"p",57),e._uU(31,"ms"),e.qZA()()()(),e.TgZ(32,"div",52)(33,"div",53)(34,"div",54)(35,"h5",60),e._uU(36,"Celkov\xfd po\u010det vol\xe1n\xed"),e.qZA(),e.TgZ(37,"p",56),e._uU(38),e.qZA(),e.TgZ(39,"p",57),e._uU(40,"za zvolen\xe9 obdob\xed"),e.qZA()()()()(),e.TgZ(41,"div",13)(42,"div",14)(43,"h5",61),e._uU(44,"V\xfdkon metody v \u010dase"),e.qZA(),e.TgZ(45,"button",16),e.NdJ("click",function(){e.CHM(i);const o=e.oxw(2);return e.KtG(o.openFullscreenChart(o.methodDetailChart,"V\xfdkon metody v \u010dase"))}),e._UZ(46,"i",17),e.qZA()(),e.TgZ(47,"div",18)(48,"div",19),e.YNc(49,xe,6,0,"div",20),e.YNc(50,Fe,4,0,"div",21),e.YNc(51,Me,2,0,"canvas",22),e.qZA()()()()}if(2&r){const i=e.oxw(2);e.xp6(8),e.Oqu(e.xi3(9,7,null==i.methodDetailData.overallStats?null:i.methodDetailData.overallStats.avg,"1.0-2")),e.xp6(10),e.Oqu(e.xi3(19,10,null==i.methodDetailData.overallStats?null:i.methodDetailData.overallStats.median,"1.0-2")),e.xp6(10),e.Oqu(e.xi3(29,13,null==i.methodDetailData.overallStats?null:i.methodDetailData.overallStats.percentil95,"1.0-2")),e.xp6(10),e.Oqu(null==i.methodDetailData.overallStats?null:i.methodDetailData.overallStats.totalCount),e.xp6(11),e.Q6J("ngIf",i.renderingMethodDetailChart),e.xp6(1),e.Q6J("ngIf",!i.hasMethodDetailChartData),e.xp6(1),e.Q6J("ngIf",i.hasMethodDetailChartData)}}function we(r,d){if(1&r){const i=e.EpF();e.TgZ(0,"div")(1,"app-advanced-filter",7),e.NdJ("filterChange",function(o){e.CHM(i);const n=e.oxw();return e.KtG(n.onMethodDetailFilterChange(o))}),e.qZA(),e.YNc(2,Ae,6,0,"div",8),e.YNc(3,Ze,52,16,"div",4),e.qZA()}if(2&r){const i=e.oxw();e.xp6(1),e.Q6J("entityType","performance_method_detail")("fields",i.methodDetailFilterFields),e.xp6(1),e.Q6J("ngIf",i.loading),e.xp6(1),e.Q6J("ngIf",!i.loading&&i.methodDetailData)}}D.kL.register(...D.zX);let ze=(()=>{const d=class{constructor(t,o,n,a,s,l){this.performanceService=t,this.instanceService=o,this.datePipe=n,this.chartModalService=a,this.changeDetectorRef=s,this.tableSortingService=l,this.aggregatedPerformanceChart=null,this.instancesComparisonChart=null,this.versionsComparisonChart=null,this.methodDetailChart=null,this.renderingAggregatedChart=!1,this.renderingInstancesComparisonChart=!1,this.renderingVersionsComparisonChart=!1,this.renderingMethodDetailChart=!1,this.hasAggregatedChartData=!0,this.hasInstancesComparisonChartData=!0,this.hasVersionsComparisonChartData=!0,this.hasMethodDetailChartData=!0,this.slowestMethods=[],this.mostCalledMethods=[],this.allMethods=[],this.selectedMethod=null,this.methodDetailData=null,this.instances=[],this.aggregatedPerformanceData=null,this.instancesComparisonData=null,this.versionsComparisonData=null,this.methodDetailChartData=null,this.methodsDisplayType="slowest",this.sortColumn="percentil95",this.sortDirection="desc",this.aggregatedFilterFields=[],this.instancesComparisonFilterFields=[],this.versionsComparisonFilterFields=[],this.methodsFilterFields=[],this.methodDetailFilterFields=[],this.loading=!0,this.error=null,this.activeTab="aggregated",this.subscriptions=[],this.tabs=[{id:"aggregated",label:"Agregovan\xfd v\xfdkon"},{id:"instances-comparison",label:"Srovn\xe1n\xed instanc\xed"},{id:"versions-comparison",label:"Srovn\xe1n\xed verz\xed"},{id:"methods",label:"Metody"},{id:"method-detail",label:"Detail metody"}],this.aggregatedFilterData={period:7},this.instancesComparisonFilterData={instanceIds:[],period:7,className:"",methodName:""},this.versionsComparisonFilterData={period:90,className:"",methodName:""},this.methodDetailFilterData={period:30,className:"",methodName:""}}ngOnInit(){console.log("PerformanceComponent - ngOnInit"),this.initEmptyFilterFields(),this.loadSortingState();const t=this.loadActiveTab();t&&(console.log(`ngOnInit - Na\u010dtena posledn\xed aktivn\xed z\xe1lo\u017eka: ${t}`),this.activeTab=t),this.aggregatedFilterData={instanceId:void 0,period:"7",fromDate:void 0,toDate:void 0,methodsDisplayType:"slowest"},this.instanceService.getAll().subscribe({next:o=>{console.log("ngOnInit - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",o),this.instances=o,this.initFilterFields(),this.loadLastFilter("performance_aggregated"),console.log("ngOnInit - Vol\xe1m API pro na\u010dten\xed dat po na\u010dten\xed instanc\xed"),setTimeout(()=>{console.log("ngOnInit - Vol\xe1m API p\u0159\xedmo s pou\u017eit\xedm ulo\u017een\xe9ho filtru:",this.aggregatedFilterData),this.loading=!0;let n=this.aggregatedFilterData.instanceId;(null==n||"null"===String(n))&&(n=void 0);let a,s=new Date;this.aggregatedFilterData.period?"custom"!==this.aggregatedFilterData.period?(a=new Date,a.setDate(s.getDate()-Number(this.aggregatedFilterData.period))):(this.aggregatedFilterData.fromDate?a=new Date(this.aggregatedFilterData.fromDate):(a=new Date,a.setDate(s.getDate()-7)),this.aggregatedFilterData.toDate&&(s=new Date(this.aggregatedFilterData.toDate))):(a=new Date,a.setDate(s.getDate()-7)),console.log("ngOnInit - Parametry pro vol\xe1n\xed API:",{instanceId:n,fromDate:a,toDate:s});const l=this.performanceService.getAggregatedPerformance(n,a,s).subscribe({next:c=>{console.log("ngOnInit - Data \xfasp\u011b\u0161n\u011b na\u010dtena p\u0159\xedmo:",c),this.aggregatedPerformanceData=c,this.aggregatedPerformanceChartRef&&this.renderAggregatedPerformanceChart(c),this.loading=!1,setTimeout(()=>{this.loadSlowestMethods()},100)},error:c=>{console.error("ngOnInit - Chyba p\u0159i p\u0159\xedm\xe9m na\u010d\xedt\xe1n\xed dat:",c),this.loading=!1}});this.subscriptions.push(l)},500)},error:o=>{console.error("ngOnInit - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",o)}})}ngAfterViewInit(){console.log("PerformanceComponent - ngAfterViewInit"),this.initPopovers(),this.initDefaultMetricsVisibility(),setTimeout(()=>{this.changeTab(this.activeTab)},500),"aggregated"===this.activeTab&&this.aggregatedPerformanceData&&this.aggregatedPerformanceChartRef?(console.log("PerformanceComponent - M\xe1m data, zkus\xedm vykreslit graf"),setTimeout(()=>{this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData)},500)):(console.log("PerformanceComponent - Nem\xe1m data nebo canvas element, zkus\xedm pozd\u011bji"),setTimeout(()=>{"aggregated"===this.activeTab&&this.aggregatedPerformanceData&&this.aggregatedPerformanceChartRef&&(console.log("PerformanceComponent - M\xe1m data a canvas element, zkus\xedm vykreslit graf po timeoutu"),this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData))},2e3))}initDefaultMetricsVisibility(){localStorage.getItem("metrics_visibility_aggregated")||localStorage.setItem("metrics_visibility_aggregated",JSON.stringify({"Pr\u016fm\u011br (ms)":!0,"Medi\xe1n (ms)":!0,"95. percentil (ms)":!0,"99. percentil (ms)":!1,"Minimum (ms)":!1,"Maximum (ms)":!1,"Po\u010det vol\xe1n\xed":!1})),localStorage.getItem("metrics_visibility_method-detail")||localStorage.setItem("metrics_visibility_method-detail",JSON.stringify({"Pr\u016fm\u011br (ms)":!0,"Medi\xe1n (ms)":!0,"95. percentil (ms)":!0,"Po\u010det vol\xe1n\xed":!1}))}initEmptyFilterFields(){const t={name:"period",label:"\u010casov\xe9 obdob\xed",type:"select",options:[{value:1,label:"1 den"},{value:7,label:"7 dn\xed"},{value:30,label:"30 dn\xed"},{value:90,label:"90 dn\xed"},{value:"custom",label:"Vlastn\xed obdob\xed"}]},o={name:"fromDate",label:"Od",type:"date",visible:a=>"custom"===a.period,required:a=>"custom"===a.period,errorMessage:"Zadejte po\u010d\xe1te\u010dn\xed datum"},n={name:"toDate",label:"Do",type:"date",visible:a=>"custom"===a.period,required:a=>"custom"===a.period,errorMessage:"Zadejte koncov\xe9 datum"};this.aggregatedFilterFields=[{name:"instanceId",label:"Instance",type:"select",options:[{value:null,label:"V\u0161echny instance"}]},t,o,n],this.instancesComparisonFilterFields=[{name:"instanceIds",label:"Instance",type:"multiselect",options:[],required:!0,validators:[(0,x._)(2)],errorMessage:"Vyberte alespo\u0148 dv\u011b instance pro srovn\xe1n\xed"},{name:"className",label:"T\u0159\xedda",type:"text"},{name:"methodName",label:"Metoda",type:"text"},t,o,n],this.versionsComparisonFilterFields=[{name:"instanceId",label:"Instance",type:"select",options:[{value:null,label:"Vyberte instanci"}],required:!0,errorMessage:"Vyberte instanci pro srovn\xe1n\xed verz\xed"},{name:"className",label:"T\u0159\xedda",type:"text"},{name:"methodName",label:"Metoda",type:"text"},t,o,n],this.methodsFilterFields=[{name:"instanceId",label:"Instance",type:"select",options:[{value:null,label:"V\u0161echny instance"}]},{name:"methodsDisplayType",label:"Metrika",type:"select",options:[{value:"slowest",label:"Nejpomalej\u0161\xed metody"},{value:"most-called",label:"Nej\u010dast\u011bji volan\xe9 metody"},{value:"all",label:"V\u0161echny metody"}]},t,o,n],this.methodDetailFilterFields=[{name:"instanceId",label:"Instance",type:"select",options:[{value:0,label:"V\u0161echny instance"}],required:!1},{name:"className",label:"T\u0159\xedda",type:"text",required:!0,errorMessage:"Zadejte n\xe1zev t\u0159\xeddy"},{name:"methodName",label:"Metoda",type:"text",required:!0,errorMessage:"Zadejte n\xe1zev metody"},t,o,n]}initFilterFields(){const t={name:"period",label:"\u010casov\xe9 obdob\xed",type:"select",options:[{value:1,label:"1 den"},{value:7,label:"7 dn\xed"},{value:30,label:"30 dn\xed"},{value:90,label:"90 dn\xed"},{value:"custom",label:"Vlastn\xed obdob\xed"}]},o=[{value:null,label:"V\u0161echny instance"},...this.instances.sort((n,a)=>{const s=n.customerAbbreviation.localeCompare(a.customerAbbreviation);return 0!==s?s:n.name.localeCompare(a.name)}).map(n=>({value:n.id,label:`${n.customerAbbreviation} - ${n.name}`}))];this.updateFilterField(this.aggregatedFilterFields,0,{name:"instanceId",label:"Instance",type:"select",options:o}),this.updateFilterField(this.instancesComparisonFilterFields,0,{name:"instanceIds",label:"Instance",type:"multiselect",options:this.instances.sort((n,a)=>{const s=n.customerAbbreviation.localeCompare(a.customerAbbreviation);return 0!==s?s:n.name.localeCompare(a.name)}).map(n=>({value:n.id,label:`${n.customerAbbreviation} - ${n.name}`})),required:!0,validators:[(0,x._)(2)],errorMessage:"Vyberte alespo\u0148 dv\u011b instance pro srovn\xe1n\xed"}),this.updateFilterField(this.versionsComparisonFilterFields,0,{name:"instanceId",label:"Instance",type:"select",options:[{value:null,label:"Vyberte instanci"},...this.instances.sort((n,a)=>{const s=n.customerAbbreviation.localeCompare(a.customerAbbreviation);return 0!==s?s:n.name.localeCompare(a.name)}).map(n=>({value:n.id,label:`${n.customerAbbreviation} - ${n.name}`}))],required:!0,errorMessage:"Vyberte instanci pro srovn\xe1n\xed verz\xed"}),this.updateFilterField(this.methodsFilterFields,0,{name:"instanceId",label:"Instance",type:"select",options:o}),this.updateFilterField(this.methodDetailFilterFields,0,{name:"instanceId",label:"Instance",type:"select",options:[{value:0,label:"V\u0161echny instance"},...this.instances.sort((n,a)=>{const s=n.customerAbbreviation.localeCompare(a.customerAbbreviation);return 0!==s?s:n.name.localeCompare(a.name)}).map(n=>({value:n.id,label:`${n.customerAbbreviation} - ${n.name}`}))]}),this.updateFilterField(this.aggregatedFilterFields,1,t),this.updateFilterField(this.instancesComparisonFilterFields,3,t),this.updateFilterField(this.versionsComparisonFilterFields,3,t),this.updateFilterField(this.methodsFilterFields,2,t),this.updateFilterField(this.methodDetailFilterFields,3,t)}updateFilterField(t,o,n){t&&t.length>o&&(t[o]={...t[o],...n},console.log(`Aktualizace pole ${n.name}:`,t[o]))}loadInstances(){console.log("loadInstances - Na\u010d\xedt\xe1m seznam instanc\xed"),this.loading?console.warn("loadInstances - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji"):(this.loading=!0,this.subscriptions.push(this.instanceService.getAll().subscribe({next:t=>{console.log("loadInstances - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",t),this.instances=t,this.initFilterFields(),this.loadLastFilter("performance_aggregated"),console.log("loadInstances - Data instanc\xed na\u010dtena, filtry inicializov\xe1ny"),this.loading=!1},error:t=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",t),this.loading=!1}})))}onPeriodChange(){}ngOnDestroy(){this.subscriptions.forEach(t=>t.unsubscribe())}openFullscreenChart(t,o){if(t instanceof HTMLCanvasElement){const n=D.kL.getChart(t);this.chartModalService.openChartModal(n||null,o)}else this.chartModalService.openChartModal(t,o)}changeTab(t){this.error="",this.changeDetectorRef.detectChanges();const o="string"==typeof t?t:String(t);switch(this.activeTab=o,this.saveActiveTab(o),"method-detail"===o&&this.selectedMethod&&(this.tabs.some(a=>"method-detail"===a.id)||this.tabs.push({id:"method-detail",label:"Detail metody"})),o){case"aggregated":this.loadLastFilter("performance_aggregated"),requestAnimationFrame(()=>{setTimeout(()=>{console.log("onTabChange - Na\u010d\xedt\xe1m data pro z\xe1lo\u017eku aggregated"),this.aggregatedPerformanceData&&this.aggregatedPerformanceChartRef?(console.log("onTabChange - M\xe1m data, zkus\xedm vykreslit graf"),this.renderAggregatedPerformanceChart(this.aggregatedPerformanceData)):this.loadAggregatedPerformance()},500)});break;case"instances-comparison":this.loadLastFilter("performance_instances_comparison"),requestAnimationFrame(()=>{setTimeout(()=>{console.log("onTabChange - Na\u010d\xedt\xe1m data pro z\xe1lo\u017eku instances-comparison"),this.instancesComparisonData&&this.instancesComparisonChartRef?(console.log("onTabChange - M\xe1m data, zkus\xedm vykreslit graf"),this.renderInstancesComparisonChart(this.instancesComparisonData)):this.loadInstancesComparison()},500)});break;case"versions-comparison":this.loadLastFilter("performance_versions_comparison"),requestAnimationFrame(()=>{setTimeout(()=>{console.log("onTabChange - Na\u010d\xedt\xe1m data pro z\xe1lo\u017eku versions-comparison"),this.versionsComparisonData&&this.versionsComparisonChartRef?(console.log("onTabChange - M\xe1m data, zkus\xedm vykreslit graf"),this.renderVersionsComparisonChart(this.versionsComparisonData)):this.loadVersionsComparison()},500)});break;case"methods":localStorage.getItem("last_filter_performance_methods")||(console.log("onTabChange - Nen\xed ulo\u017een \u017e\xe1dn\xfd filtr pro z\xe1lo\u017eku methods, resetuji instanceId na undefined"),this.aggregatedFilterData.instanceId=void 0),this.loadLastFilter("performance_methods"),requestAnimationFrame(()=>{setTimeout(()=>{console.log("onTabChange - Na\u010d\xedt\xe1m data pro z\xe1lo\u017eku methods, instanceId:",this.aggregatedFilterData.instanceId),this.loadMethodsByType()},100)});break;case"method-detail":if(this.loadLastFilter("performance_method_detail"),this.hasMethodDetailChartData=!0,this.selectedMethod){console.log("onTabChange - Pou\u017eit\xed hodnot z vybran\xe9 metody pro filtr:",this.selectedMethod);let s=this.selectedMethod.instanceId;(null==s||"null"===String(s))&&(s=0),this.methodDetailFilterData={...this.methodDetailFilterData,instanceId:s,className:this.selectedMethod.className,methodName:this.selectedMethod.methodName},this.saveFilter("performance_method_detail",this.methodDetailFilterData)}requestAnimationFrame(()=>{setTimeout(()=>{console.log("onTabChange - Na\u010d\xedt\xe1m data pro z\xe1lo\u017eku method-detail"),this.loadMethodDetail()},500)})}setTimeout(()=>{this.initPopovers()},500)}loadLastFilter(t){console.log(`loadLastFilter - Na\u010d\xedt\xe1m posledn\xed filtr pro ${t}`);try{const o=`last_filter_${t}`,n=localStorage.getItem(o);if(n){const a=JSON.parse(n);switch(console.log(`Na\u010dten posledn\xed filtr pro ${t} z localStorage:`,a),t){case"performance_aggregated":JSON.stringify(this.aggregatedFilterData)!==JSON.stringify({...this.aggregatedFilterData,...a})?(this.aggregatedFilterData={...this.aggregatedFilterData,...a},console.log(`Aktualizov\xe1n filtr pro ${t}:`,this.aggregatedFilterData)):console.log(`Filtr pro ${t} se nezm\u011bnil, p\u0159eskakuji aktualizaci`);break;case"performance_instances_comparison":JSON.stringify(this.instancesComparisonFilterData)!==JSON.stringify({...this.instancesComparisonFilterData,...a})?(this.instancesComparisonFilterData={...this.instancesComparisonFilterData,...a},console.log(`Aktualizov\xe1n filtr pro ${t}:`,this.instancesComparisonFilterData)):console.log(`Filtr pro ${t} se nezm\u011bnil, p\u0159eskakuji aktualizaci`);break;case"performance_versions_comparison":JSON.stringify(this.versionsComparisonFilterData)!==JSON.stringify({...this.versionsComparisonFilterData,...a})?(this.versionsComparisonFilterData={...this.versionsComparisonFilterData,...a},console.log(`Aktualizov\xe1n filtr pro ${t}:`,this.versionsComparisonFilterData)):console.log(`Filtr pro ${t} se nezm\u011bnil, p\u0159eskakuji aktualizaci`);break;case"performance_methods":JSON.stringify(this.aggregatedFilterData)!==JSON.stringify({...this.aggregatedFilterData,...a})?(this.aggregatedFilterData={...this.aggregatedFilterData,...a},a.methodsDisplayType&&(this.methodsDisplayType=a.methodsDisplayType),console.log(`Aktualizov\xe1n filtr pro ${t}:`,this.aggregatedFilterData)):console.log(`Filtr pro ${t} se nezm\u011bnil, p\u0159eskakuji aktualizaci`);break;case"performance_method_detail":JSON.stringify(this.methodDetailFilterData)!==JSON.stringify({...this.methodDetailFilterData,...a})?(this.methodDetailFilterData={...this.methodDetailFilterData,...a},console.log(`Aktualizov\xe1n filtr pro ${t}:`,this.methodDetailFilterData)):console.log(`Filtr pro ${t} se nezm\u011bnil, p\u0159eskakuji aktualizaci`)}}}catch(o){console.error(`Chyba p\u0159i na\u010d\xedt\xe1n\xed posledn\xedho filtru pro ${t} z localStorage`,o)}}loadAggregatedPerformance(){if(console.log("loadAggregatedPerformance - Za\u010d\xedn\xe1m na\u010d\xedtat data"),this.loading)return void console.warn("loadAggregatedPerformance - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji");if(this.loading=!0,this.error="",this.aggregatedPerformanceChartRef?console.log("loadAggregatedPerformance - Reference na canvas element je k dispozici"):console.warn("loadAggregatedPerformance - Reference na canvas element nen\xed k dispozici, ale pokra\u010duji v na\u010d\xedt\xe1n\xed dat"),!this.instances||0===this.instances.length)return console.warn("loadAggregatedPerformance - Instance nejsou na\u010dteny, na\u010d\xedt\xe1m je"),void this.instanceService.getAll().subscribe({next:a=>{console.log("loadAggregatedPerformance - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",a),this.instances=a,this.initFilterFields(),this.loadAggregatedPerformance()},error:a=>{console.error("loadAggregatedPerformance - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",a),this.error="Nepoda\u0159ilo se na\u010d\xedst seznam instanc\xed. Zkuste to pros\xedm znovu.",this.loading=!1}});this.aggregatedFilterData||(console.warn("loadAggregatedPerformance - Filtr nen\xed inicializov\xe1n, inicializuji v\xfdchoz\xed hodnoty"),this.aggregatedFilterData={instanceId:void 0,period:"7",fromDate:void 0,toDate:void 0});let o,n,t=this.aggregatedFilterData.instanceId;if((null==t||"null"===String(t))&&(t=void 0),console.log("loadAggregatedPerformance - instanceId:",t),this.aggregatedFilterData.period)if("custom"!==this.aggregatedFilterData.period){const a=new Date;o=new Date,o.setDate(a.getDate()-Number(this.aggregatedFilterData.period)),n=a}else this.aggregatedFilterData.fromDate?o=new Date(this.aggregatedFilterData.fromDate):(o=new Date,o.setDate(o.getDate()-7)),n=this.aggregatedFilterData.toDate?new Date(this.aggregatedFilterData.toDate):new Date;else{const a=new Date;o=new Date,o.setDate(a.getDate()-7),n=a}console.log("loadAggregatedPerformance - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,fromDate:o,toDate:n});try{const a=this.performanceService.getAggregatedPerformance(t,o,n).subscribe({next:s=>{if(console.log("loadAggregatedPerformance - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",s),!s)return console.error("loadAggregatedPerformance - API vr\xe1tilo pr\xe1zdn\xe1 data"),this.error="API vr\xe1tilo pr\xe1zdn\xe1 data. Zkuste to pros\xedm znovu.",void(this.loading=!1);!s.labels&&!s.dailyData&&console.warn("loadAggregatedPerformance - Data neobsahuj\xed labels ani dailyData, pokus\xedm se je zpracovat"),this.aggregatedPerformanceData=s,this.renderAggregatedPerformanceChart(s),this.loading=!1},error:s=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed agregovan\xfdch v\xfdkonnostn\xedch metrik:",s),this.error="Nepoda\u0159ilo se na\u010d\xedst agregovan\xe9 v\xfdkonnostn\xed metriky. "+(s.message?s.message:"Zkuste to pros\xedm znovu."),this.loading=!1,0===s.status&&(console.log("loadAggregatedPerformance - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadAggregatedPerformance - Opakuji vol\xe1n\xed API"),this.loadAggregatedPerformance()},5e3))},complete:()=>{console.log("loadAggregatedPerformance - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(a)}catch(a){console.error("loadAggregatedPerformance - Chyba p\u0159i vol\xe1n\xed API:",a),this.error="Nepoda\u0159ilo se na\u010d\xedst agregovan\xe9 v\xfdkonnostn\xed metriky. Zkuste to pros\xedm znovu.",this.loading=!1}"aggregated"===this.activeTab&&setTimeout(()=>{this.loadSlowestMethods()},100)}onAggregatedFilterChange(t){console.log("onAggregatedFilterChange - P\u0159ijat\xe1 data filtru:",t),JSON.stringify(this.aggregatedFilterData)===JSON.stringify(t)&&console.log("onAggregatedFilterChange - Filtr se nezm\u011bnil, ale pokra\u010duji v na\u010d\xedt\xe1n\xed dat"),this.loading?console.warn("onAggregatedFilterChange - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji"):("custom"===t.period?(t.fromDate&&"string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),t.toDate&&"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))):t.fromDate&&t.toDate&&("string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))),this.aggregatedFilterData=t,console.log("onAggregatedFilterChange - Zpracovan\xe1 data filtru:",this.aggregatedFilterData),setTimeout(()=>{this.loadAggregatedPerformance()},0))}loadInstancesComparison(){if(console.log("loadInstancesComparison - Za\u010d\xedn\xe1m na\u010d\xedtat data"),this.loading)return void console.warn("loadInstancesComparison - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji");if(this.loading=!0,this.error="",!this.instances||0===this.instances.length)return console.warn("loadInstancesComparison - Instance nejsou na\u010dteny, na\u010d\xedt\xe1m je"),void this.instanceService.getAll().subscribe({next:l=>{console.log("loadInstancesComparison - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",l),this.instances=l,this.initFilterFields(),this.loadInstancesComparison()},error:l=>{console.error("loadInstancesComparison - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",l),this.error="Nepoda\u0159ilo se na\u010d\xedst seznam instanc\xed. Zkuste to pros\xedm znovu.",this.loading=!1}});if(!this.instancesComparisonFilterData.instanceIds||this.instancesComparisonFilterData.instanceIds.length<2)return void(this.loading=!1);const t=this.instancesComparisonFilterData.instanceIds,o=this.instancesComparisonFilterData.methodName,n=this.instancesComparisonFilterData.className;let a,s;if(this.instancesComparisonFilterData.period)if("custom"!==this.instancesComparisonFilterData.period){const l=new Date;a=new Date,a.setDate(l.getDate()-Number(this.instancesComparisonFilterData.period)),s=l}else this.instancesComparisonFilterData.fromDate?a=new Date(this.instancesComparisonFilterData.fromDate):(a=new Date,a.setDate(a.getDate()-7)),s=this.instancesComparisonFilterData.toDate?new Date(this.instancesComparisonFilterData.toDate):new Date;else{const l=new Date;a=new Date,a.setDate(l.getDate()-7),s=l}try{console.log("loadInstancesComparison - Vol\xe1m API pro na\u010dten\xed dat",{instanceIds:t,methodName:o,className:n,fromDate:a,toDate:s});const l=this.performanceService.getInstancesComparison(t,o,n,a,s).subscribe({next:c=>{if(console.log("loadInstancesComparison - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",c),!c)return console.error("loadInstancesComparison - API vr\xe1tilo pr\xe1zdn\xe1 data"),this.error="API vr\xe1tilo pr\xe1zdn\xe1 data. Zkuste to pros\xedm znovu.",void(this.loading=!1);this.instancesComparisonData=c,this.renderInstancesComparisonChart(c),this.loading=!1},error:c=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed srovn\xe1n\xed instanc\xed:",c),this.error="Nepoda\u0159ilo se na\u010d\xedst srovn\xe1n\xed v\xfdkonu metod mezi instancemi. "+(c.message?c.message:"Zkuste to pros\xedm znovu."),this.loading=!1,0===c.status&&(console.log("loadInstancesComparison - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadInstancesComparison - Opakuji vol\xe1n\xed API"),this.loadInstancesComparison()},5e3))},complete:()=>{console.log("loadInstancesComparison - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(l)}catch(l){console.error("loadInstancesComparison - Chyba p\u0159i vol\xe1n\xed API:",l),this.error="Nepoda\u0159ilo se na\u010d\xedst srovn\xe1n\xed v\xfdkonu metod mezi instancemi. Zkuste to pros\xedm znovu.",this.loading=!1}}onInstancesComparisonFilterChange(t){console.log("onInstancesComparisonFilterChange - P\u0159ijat\xe1 data filtru:",t),JSON.stringify(this.instancesComparisonFilterData)===JSON.stringify(t)&&console.log("onInstancesComparisonFilterChange - Filtr se nezm\u011bnil, ale pokra\u010duji v na\u010d\xedt\xe1n\xed dat"),this.loading?console.warn("onInstancesComparisonFilterChange - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji"):("custom"===t.period?(t.fromDate&&"string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),t.toDate&&"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))):t.fromDate&&t.toDate&&("string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))),this.instancesComparisonFilterData=t,console.log("onInstancesComparisonFilterChange - Zpracovan\xe1 data filtru:",this.instancesComparisonFilterData),this.loadInstancesComparison())}loadVersionsComparison(){if(console.log("loadVersionsComparison - Za\u010d\xedn\xe1m na\u010d\xedtat data"),this.loading)return void console.warn("loadVersionsComparison - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji");if(this.loading=!0,this.error="",!this.instances||0===this.instances.length)return console.warn("loadVersionsComparison - Instance nejsou na\u010dteny, na\u010d\xedt\xe1m je"),void this.instanceService.getAll().subscribe({next:l=>{console.log("loadVersionsComparison - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",l),this.instances=l,this.initFilterFields(),this.loadVersionsComparison()},error:l=>{console.error("loadVersionsComparison - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",l),this.error="Nepoda\u0159ilo se na\u010d\xedst seznam instanc\xed. Zkuste to pros\xedm znovu.",this.loading=!1}});if(!this.versionsComparisonFilterData.instanceId||null===this.versionsComparisonFilterData.instanceId||"null"===String(this.versionsComparisonFilterData.instanceId))return console.warn("loadVersionsComparison - Nen\xed vybr\xe1na instance, p\u0159eskakuji na\u010d\xedt\xe1n\xed dat"),void(this.loading=!1);const t=this.versionsComparisonFilterData.instanceId,o=this.versionsComparisonFilterData.methodName,n=this.versionsComparisonFilterData.className;let a,s;if(this.versionsComparisonFilterData.period)if("custom"!==this.versionsComparisonFilterData.period){const l=new Date;a=new Date,a.setDate(l.getDate()-Number(this.versionsComparisonFilterData.period)),s=l}else this.versionsComparisonFilterData.fromDate?a=new Date(this.versionsComparisonFilterData.fromDate):(a=new Date,a.setDate(a.getDate()-7)),s=this.versionsComparisonFilterData.toDate?new Date(this.versionsComparisonFilterData.toDate):new Date;else{const l=new Date;a=new Date,a.setDate(l.getDate()-7),s=l}try{console.log("loadVersionsComparison - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,methodName:o,className:n,fromDate:a,toDate:s});const l=this.performanceService.getVersionsComparison(t,o,n,a,s).subscribe({next:c=>{if(console.log("loadVersionsComparison - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",c),!c)return console.error("loadVersionsComparison - API vr\xe1tilo pr\xe1zdn\xe1 data"),this.error="API vr\xe1tilo pr\xe1zdn\xe1 data. Zkuste to pros\xedm znovu.",void(this.loading=!1);this.versionsComparisonData=c,this.renderVersionsComparisonChart(c),this.loading=!1},error:c=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed srovn\xe1n\xed verz\xed:",c),this.error="Nepoda\u0159ilo se na\u010d\xedst srovn\xe1n\xed v\xfdkonu metod mezi verzemi. "+(c.message?c.message:"Zkuste to pros\xedm znovu."),this.loading=!1,0===c.status&&(console.log("loadVersionsComparison - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadVersionsComparison - Opakuji vol\xe1n\xed API"),this.loadVersionsComparison()},5e3))},complete:()=>{console.log("loadVersionsComparison - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(l)}catch(l){console.error("loadVersionsComparison - Chyba p\u0159i vol\xe1n\xed API:",l),this.error="Nepoda\u0159ilo se na\u010d\xedst srovn\xe1n\xed v\xfdkonu metod mezi verzemi. Zkuste to pros\xedm znovu.",this.loading=!1}}onVersionsComparisonFilterChange(t){if(console.log("onVersionsComparisonFilterChange - P\u0159ijat\xe1 data filtru:",t),JSON.stringify(this.versionsComparisonFilterData)===JSON.stringify(t)&&console.log("onVersionsComparisonFilterChange - Filtr se nezm\u011bnil, ale pokra\u010duji v na\u010d\xedt\xe1n\xed dat"),!this.loading)return null==t.instanceId||"null"===String(t.instanceId)?(console.warn("onVersionsComparisonFilterChange - Nen\xed vybr\xe1na instance, p\u0159eskakuji na\u010d\xedt\xe1n\xed dat"),void(this.versionsComparisonFilterData=t)):("custom"===t.period?(t.fromDate&&"string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),t.toDate&&"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))):t.fromDate&&t.toDate&&("string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))),this.versionsComparisonFilterData=t,console.log("onVersionsComparisonFilterChange - Zpracovan\xe1 data filtru:",this.versionsComparisonFilterData),void this.loadVersionsComparison());console.warn("onVersionsComparisonFilterChange - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji")}loadMostCalledMethods(){if(console.log("loadMostCalledMethods - Za\u010d\xedn\xe1m na\u010d\xedtat data"),this.loading)return void console.warn("loadMostCalledMethods - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji");if(this.loading=!0,!this.instances||0===this.instances.length)return console.warn("loadMostCalledMethods - Instance nejsou na\u010dteny, na\u010d\xedt\xe1m je"),void this.instanceService.getAll().subscribe({next:a=>{console.log("loadMostCalledMethods - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",a),this.instances=a,this.initFilterFields(),this.loadMostCalledMethods()},error:a=>{console.error("loadMostCalledMethods - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",a),this.mostCalledMethods=[],this.loading=!1}});let o,n,t=this.aggregatedFilterData.instanceId;if((null==t||"null"===String(t)||0===t)&&(t=void 0,console.log("loadMostCalledMethods - Pou\u017eit\xed undefined pro instanceId (v\u0161echny instance)")),this.aggregatedFilterData.period)if("custom"!==this.aggregatedFilterData.period){const a=new Date;o=new Date,o.setDate(a.getDate()-Number(this.aggregatedFilterData.period)),n=a}else this.aggregatedFilterData.fromDate?o=new Date(this.aggregatedFilterData.fromDate):(o=new Date,o.setDate(o.getDate()-7)),n=this.aggregatedFilterData.toDate?new Date(this.aggregatedFilterData.toDate):new Date;else{const a=new Date;o=new Date,o.setDate(a.getDate()-7),n=a}try{console.log("loadMostCalledMethods - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,fromDate:o,toDate:n});const a=this.performanceService.getMostCalledMethods(t,o,n,10).subscribe({next:s=>{if(console.log("loadMostCalledMethods - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",s),!s)return console.error("loadMostCalledMethods - API vr\xe1tilo pr\xe1zdn\xe1 data"),this.mostCalledMethods=[],void(this.loading=!1);this.mostCalledMethods=s,this.applySortToMethods(),this.loading=!1},error:s=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed nej\u010dast\u011bji volan\xfdch metod:",s),this.mostCalledMethods=[],this.loading=!1,0===s.status&&(console.log("loadMostCalledMethods - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadMostCalledMethods - Opakuji vol\xe1n\xed API"),this.loadMostCalledMethods()},5e3))},complete:()=>{console.log("loadMostCalledMethods - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(a)}catch(a){console.error("loadMostCalledMethods - Chyba p\u0159i vol\xe1n\xed API:",a),this.mostCalledMethods=[],this.loading=!1}}loadSlowestMethods(){if(console.log("loadSlowestMethods - Za\u010d\xedn\xe1m na\u010d\xedtat data"),this.loading)return void console.warn("loadSlowestMethods - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji");if(this.loading=!0,!this.instances||0===this.instances.length)return console.warn("loadSlowestMethods - Instance nejsou na\u010dteny, na\u010d\xedt\xe1m je"),void this.instanceService.getAll().subscribe({next:a=>{console.log("loadSlowestMethods - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",a),this.instances=a,this.initFilterFields(),this.loadSlowestMethods()},error:a=>{console.error("loadSlowestMethods - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",a),this.slowestMethods=[]}});let o,n,t=this.aggregatedFilterData.instanceId;if((null==t||"null"===String(t)||0===t)&&(t=void 0,console.log("loadSlowestMethods - Pou\u017eit\xed undefined pro instanceId (v\u0161echny instance)")),this.aggregatedFilterData.period)if("custom"!==this.aggregatedFilterData.period){const a=new Date;o=new Date,o.setDate(a.getDate()-Number(this.aggregatedFilterData.period)),n=a}else this.aggregatedFilterData.fromDate?o=new Date(this.aggregatedFilterData.fromDate):(o=new Date,o.setDate(o.getDate()-7)),n=this.aggregatedFilterData.toDate?new Date(this.aggregatedFilterData.toDate):new Date;else{const a=new Date;o=new Date,o.setDate(a.getDate()-7),n=a}try{console.log("loadSlowestMethods - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,fromDate:o,toDate:n});const a=this.performanceService.getSlowestMethods(t,o,n,10).subscribe({next:s=>{if(console.log("loadSlowestMethods - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",s),!s)return console.error("loadSlowestMethods - API vr\xe1tilo pr\xe1zdn\xe1 data"),this.slowestMethods=[],void(this.loading=!1);this.slowestMethods=s,this.applySortToMethods(),this.loading=!1},error:s=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed nejpomalej\u0161\xedch metod:",s),this.slowestMethods=[],this.loading=!1,0===s.status&&(console.log("loadSlowestMethods - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadSlowestMethods - Opakuji vol\xe1n\xed API"),this.loadSlowestMethods()},5e3))},complete:()=>{console.log("loadSlowestMethods - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(a)}catch(a){console.error("loadSlowestMethods - Chyba p\u0159i vol\xe1n\xed API:",a),this.slowestMethods=[],this.loading=!1}}loadAllMethods(){let o,n,t=this.aggregatedFilterData.instanceId;if((null==t||"null"===String(t)||0===t)&&(t=void 0,console.log("loadAllMethods - Pou\u017eit\xed undefined pro instanceId (v\u0161echny instance)")),this.aggregatedFilterData.period)if("custom"!==this.aggregatedFilterData.period){const a=new Date;o=new Date,o.setDate(a.getDate()-Number(this.aggregatedFilterData.period)),n=a}else this.aggregatedFilterData.fromDate?o=new Date(this.aggregatedFilterData.fromDate):(o=new Date,o.setDate(o.getDate()-7)),n=this.aggregatedFilterData.toDate?new Date(this.aggregatedFilterData.toDate):new Date;else{const a=new Date;o=new Date,o.setDate(a.getDate()-7),n=a}try{console.log("loadAllMethods - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,fromDate:o,toDate:n});const a=this.performanceService.getAllMethods(t,o,n).subscribe({next:s=>{if(console.log("loadAllMethods - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",s),!s)return console.error("loadAllMethods - API vr\xe1tilo pr\xe1zdn\xe1 data"),void(this.allMethods=[]);this.allMethods=s,this.applySortToMethods()},error:s=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed v\u0161ech metod:",s),this.allMethods=[],0===s.status&&(console.log("loadAllMethods - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadAllMethods - Opakuji vol\xe1n\xed API"),this.loadAllMethods()},5e3))},complete:()=>{console.log("loadAllMethods - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(a)}catch(a){console.error("loadAllMethods - Chyba p\u0159i vol\xe1n\xed API:",a),this.allMethods=[]}}loadMethodDetail(){if(console.log("loadMethodDetail - Za\u010d\xedn\xe1m na\u010d\xedtat data"),this.loading)return void console.warn("loadMethodDetail - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji");if(this.loading=!0,this.error="",!(this.selectedMethod||this.methodDetailFilterData.className&&this.methodDetailFilterData.methodName))return console.warn("loadMethodDetail - Nen\xed vybr\xe1na metoda a nejsou zad\xe1ny povinn\xe9 parametry"),void(this.loading=!1);if(this.selectedMethod&&(!this.methodDetailFilterData.className||!this.methodDetailFilterData.methodName)){console.log("loadMethodDetail - Pou\u017eit\xed parametr\u016f z vybran\xe9 metody"),this.methodDetailFilterData.className=this.selectedMethod.className,this.methodDetailFilterData.methodName=this.selectedMethod.methodName;let l=this.selectedMethod.instanceId;(null==l||"null"===String(l))&&(l=0),this.methodDetailFilterData.instanceId=l,this.saveFilter("performance_method_detail",this.methodDetailFilterData)}if(!this.instances||0===this.instances.length)return console.warn("loadMethodDetail - Instance nejsou na\u010dteny, na\u010d\xedt\xe1m je"),void this.instanceService.getAll().subscribe({next:l=>{console.log("loadMethodDetail - Instance \xfasp\u011b\u0161n\u011b na\u010dteny:",l),this.instances=l,this.initFilterFields(),this.loadMethodDetail()},error:l=>{console.error("loadMethodDetail - Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed:",l),this.error="Nepoda\u0159ilo se na\u010d\xedst seznam instanc\xed. Zkuste to pros\xedm znovu.",this.loading=!1}});if(!this.methodDetailFilterData.instanceId||!this.methodDetailFilterData.className||!this.methodDetailFilterData.methodName)return void(this.loading=!1);let t=this.methodDetailFilterData.instanceId??null;(null==t||"null"===String(t))&&(t=0,console.log("loadMethodDetail - Pou\u017eit\xed hodnoty 0 pro instanceId (v\u0161echny instance)"),this.methodDetailFilterData.instanceId=0),console.log("loadMethodDetail - Fin\xe1ln\xed hodnota instanceId:",t);const o=this.methodDetailFilterData.className,n=this.methodDetailFilterData.methodName;let a,s;if(this.methodDetailFilterData.period)if("custom"!==this.methodDetailFilterData.period){const l=new Date;a=new Date,a.setDate(l.getDate()-Number(this.methodDetailFilterData.period)),s=l}else this.methodDetailFilterData.fromDate?a=new Date(this.methodDetailFilterData.fromDate):(a=new Date,a.setDate(a.getDate()-7)),s=this.methodDetailFilterData.toDate?new Date(this.methodDetailFilterData.toDate):new Date;else{const l=new Date;a=new Date,a.setDate(l.getDate()-7),s=l}try{if(console.log("loadMethodDetail - Vol\xe1m API pro na\u010dten\xed dat",{instanceId:t,className:o,methodName:n,fromDate:a,toDate:s}),!o||!n)return console.error("loadMethodDetail - Nejsou zad\xe1ny povinn\xe9 parametry (t\u0159\xedda nebo metoda)"),this.error="Pro zobrazen\xed detailu metody je nutn\xe9 zadat n\xe1zev t\u0159\xeddy a metody.",void(this.loading=!1);const c=this.performanceService.getMethodDetail(0===t?null:t,o,n,a,s).subscribe({next:m=>{if(console.log("loadMethodDetail - Data \xfasp\u011b\u0161n\u011b na\u010dtena:",m),!m)return console.error("loadMethodDetail - API vr\xe1tilo pr\xe1zdn\xe1 data"),this.error="API vr\xe1tilo pr\xe1zdn\xe1 data. Zkuste to pros\xedm znovu.",void(this.loading=!1);m.className&&m.methodName&&(console.log("loadMethodDetail - Aktualizuji hodnoty filtru podle na\u010dten\xfdch dat:",{className:m.className,methodName:m.methodName}),this.methodDetailFilterData.className=m.className,this.methodDetailFilterData.methodName=m.methodName,this.saveFilter("performance_method_detail",this.methodDetailFilterData)),this.methodDetailData=m,this.methodDetailChartData=m;const u=m&&m.labels&&Array.isArray(m.labels)&&m.labels.length>0;this.hasMethodDetailChartData=u,this.renderMethodDetailChart(m),this.loading=!1},error:m=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed detailu metody:",m),this.error="Nepoda\u0159ilo se na\u010d\xedst detail metody. "+(m.message?m.message:"Zkuste to pros\xedm znovu."),this.loading=!1,0===m.status&&(console.log("loadMethodDetail - Server neodpov\xedd\xe1, zkus\xedm to znovu za 5 sekund"),setTimeout(()=>{console.log("loadMethodDetail - Opakuji vol\xe1n\xed API"),this.loadMethodDetail()},5e3))},complete:()=>{console.log("loadMethodDetail - Vol\xe1n\xed API dokon\u010deno")}});this.subscriptions.push(c)}catch(l){console.error("loadMethodDetail - Chyba p\u0159i vol\xe1n\xed API:",l),this.error="Nepoda\u0159ilo se na\u010d\xedst detail metody. Zkuste to pros\xedm znovu.",this.loading=!1}}loadMethodsByType(){switch(console.log("loadMethodsByType - Na\u010d\xedt\xe1m data podle typu:",this.methodsDisplayType),this.methodsDisplayType){case"slowest":default:this.loadSlowestMethods();break;case"most-called":this.loadMostCalledMethods();break;case"all":this.loadAllMethods()}}onMethodsFilterChange(t){console.log("onMethodsFilterChange - P\u0159ijat\xe1 data filtru:",t),JSON.stringify(this.aggregatedFilterData)===JSON.stringify(t)&&console.log("onMethodsFilterChange - Filtr se nezm\u011bnil, ale pokra\u010duji v na\u010d\xedt\xe1n\xed dat"),this.loading?console.warn("onMethodsFilterChange - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji"):("custom"===t.period?(t.fromDate&&"string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),t.toDate&&"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))):t.fromDate&&t.toDate&&("string"==typeof t.fromDate&&(t.fromDate=new Date(t.fromDate)),"string"==typeof t.toDate&&(t.toDate=new Date(t.toDate))),t.methodsDisplayType?this.methodsDisplayType=t.methodsDisplayType:(this.methodsDisplayType="slowest",t.methodsDisplayType="slowest"),this.aggregatedFilterData=t,console.log("onMethodsFilterChange - Zpracovan\xe1 data filtru:",this.aggregatedFilterData),this.loadMethodsByType(),setTimeout(()=>{this.initPopovers()},500))}onMethodDetailFilterChange(t){console.log("onMethodDetailFilterChange - P\u0159ijat\xe1 data filtru:",t),this.loading?console.warn("onMethodDetailFilterChange - Na\u010d\xedt\xe1n\xed dat ji\u017e prob\xedh\xe1, p\u0159eskakuji"):(this.methodDetailFilterData={...t},null==t.instanceId||"null"===String(t.instanceId)?(console.log("onMethodDetailFilterChange - Hodnota instanceId nen\xed nastavena, pou\u017eijeme 0 (v\u0161echny instance)"),this.methodDetailFilterData.instanceId=0):console.log("onMethodDetailFilterChange - Hodnota instanceId je nastavena:",t.instanceId),this.selectedMethod&&(this.selectedMethod.className!==this.methodDetailFilterData.className||this.selectedMethod.methodName!==this.methodDetailFilterData.methodName||this.selectedMethod.instanceId!==this.methodDetailFilterData.instanceId)&&(console.log("onMethodDetailFilterChange - Resetuji vybranou metodu, proto\u017ee se zm\u011bnil filtr"),this.selectedMethod=null),"custom"===t.period?(t.fromDate&&"string"==typeof t.fromDate&&(this.methodDetailFilterData.fromDate=new Date(t.fromDate)),t.toDate&&"string"==typeof t.toDate&&(this.methodDetailFilterData.toDate=new Date(t.toDate))):t.fromDate&&t.toDate&&("string"==typeof t.fromDate&&(this.methodDetailFilterData.fromDate=new Date(t.fromDate)),"string"==typeof t.toDate&&(this.methodDetailFilterData.toDate=new Date(t.toDate))),console.log("onMethodDetailFilterChange - Zpracovan\xe1 data filtru:",this.methodDetailFilterData),this.methodDetailFilterData.className&&this.methodDetailFilterData.methodName?(JSON.stringify(this.methodDetailFilterData)===JSON.stringify(t)?console.log("onMethodDetailFilterChange - Filtr se nezm\u011bnil, ale pokra\u010duji v na\u010d\xedt\xe1n\xed dat"):this.saveFilter("performance_method_detail",this.methodDetailFilterData),this.hasMethodDetailChartData=!0,this.loadMethodDetail()):console.warn("onMethodDetailFilterChange - Nejsou vypln\u011bny povinn\xe9 parametry (t\u0159\xedda nebo metoda), p\u0159eskakuji na\u010d\xedt\xe1n\xed dat"))}showMethodDetail(t){console.log("showMethodDetail - Zobrazuji detail metody:",t),this.selectedMethod=t;let o=t.instanceId;"aggregated"===this.activeTab||"methods"===this.activeTab?this.aggregatedFilterData&&void 0!==this.aggregatedFilterData.instanceId&&(o=this.aggregatedFilterData.instanceId,(null===o||"null"===String(o))&&(o=0)):"versions-comparison"===this.activeTab&&this.versionsComparisonFilterData&&void 0!==this.versionsComparisonFilterData.instanceId&&(o=this.versionsComparisonFilterData.instanceId,(null===o||"null"===String(o))&&(o=void 0)),void 0===o&&(o=t.instanceId),null===o&&(o=0),console.log("showMethodDetail - Nastavuji hodnoty filtru:",{instanceId:o,className:t.className,methodName:t.methodName}),this.methodDetailFilterData={...this.methodDetailFilterData,instanceId:o,className:t.className,methodName:t.methodName},this.saveFilter("performance_method_detail",this.methodDetailFilterData),this.hasMethodDetailChartData=!0,this.activeTab="method-detail",this.saveActiveTab("method-detail"),this.loadMethodDetail()}saveMetricsVisibility(t,o){try{const n={};o.forEach(a=>{if(a.label)if(this.aggregatedPerformanceChart&&"aggregated"===t){const s=o.indexOf(a);n[a.label]=this.aggregatedPerformanceChart.isDatasetVisible(s)}else if(this.instancesComparisonChart&&"instances-comparison"===t){const s=o.indexOf(a);n[a.label]=this.instancesComparisonChart.isDatasetVisible(s)}else if(this.versionsComparisonChart&&"versions-comparison"===t){const s=o.indexOf(a);n[a.label]=this.versionsComparisonChart.isDatasetVisible(s)}else if(this.methodDetailChart&&"method-detail"===t){const s=o.indexOf(a);n[a.label]=this.methodDetailChart.isDatasetVisible(s)}else n[a.label]=!a.hidden}),localStorage.setItem(`metrics_visibility_${t}`,JSON.stringify(n)),console.log(`Nastaven\xed viditelnosti metrik pro ${t} ulo\u017eeno:`,n)}catch(n){console.error(`Chyba p\u0159i ukl\xe1d\xe1n\xed nastaven\xed viditelnosti metrik pro ${t}:`,n)}}saveActiveTab(t){try{localStorage.setItem("performance_active_tab",t),console.log(`Ulo\u017eena aktivn\xed z\xe1lo\u017eka: ${t}`)}catch(o){console.error("Chyba p\u0159i ukl\xe1d\xe1n\xed aktivn\xed z\xe1lo\u017eky:",o)}}loadActiveTab(){try{const t=localStorage.getItem("performance_active_tab");return console.log(`Na\u010dtena aktivn\xed z\xe1lo\u017eka z localStorage: ${t}`),t}catch(t){return console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed aktivn\xed z\xe1lo\u017eky:",t),null}}saveFilter(t,o){try{const n=`last_filter_${t}`;localStorage.setItem(n,JSON.stringify(o)),console.log(`Ulo\u017een filtr pro ${t} do localStorage:`,o)}catch(n){console.error(`Chyba p\u0159i ukl\xe1d\xe1n\xed filtru pro ${t} do localStorage:`,n)}}onSort(t){console.log(`onSort - \u0158azen\xed podle sloupce: ${t}`);const o=this.tableSortingService.createNewSortingStateWithDefaults(this.sortColumn,this.sortDirection,t,["totalCount","min","max","avg","median","percentil95"]);this.sortColumn=o.column,this.sortDirection=o.direction,this.tableSortingService.saveSortingState("performance","methods",o),console.log(`onSort - Nov\xe9 \u0159azen\xed: ${this.sortColumn} ${this.sortDirection}`),this.applySortToMethods()}applySortToMethods(){console.log(`applySortToMethods - Aplikuji \u0159azen\xed: ${this.sortColumn} ${this.sortDirection}`),this.slowestMethods&&this.slowestMethods.length>0&&(this.slowestMethods=this.sortMethods([...this.slowestMethods])),this.mostCalledMethods&&this.mostCalledMethods.length>0&&(this.mostCalledMethods=this.sortMethods([...this.mostCalledMethods])),this.allMethods&&this.allMethods.length>0&&(this.allMethods=this.sortMethods([...this.allMethods]))}sortMethods(t){return t.sort((o,n)=>{let a,s;switch(this.sortColumn){case"instanceName":a=o.instanceName?.toLowerCase()||"",s=n.instanceName?.toLowerCase()||"";break;case"className":a=o.className?.toLowerCase()||"",s=n.className?.toLowerCase()||"";break;case"methodName":a=o.methodName?.toLowerCase()||"",s=n.methodName?.toLowerCase()||"";break;case"totalCount":a=o.totalCount||0,s=n.totalCount||0;break;case"min":a=o.min||0,s=n.min||0;break;case"max":a=o.max||0,s=n.max||0;break;case"avg":a=o.avg||0,s=n.avg||0;break;case"median":a=o.median||0,s=n.median||0;break;case"percentil95":a=o.percentil95||0,s=n.percentil95||0;break;default:a=0,s=0}return"asc"===this.sortDirection?"string"==typeof a?a.localeCompare(s):a-s:"string"==typeof a?s.localeCompare(a):s-a})}loadSortingState(){const o=this.tableSortingService.loadSortingState("performance","methods",{column:"avg",direction:"desc"});o&&(this.sortColumn=o.column,this.sortDirection=o.direction)}loadMetricsVisibility(t){try{const o=localStorage.getItem(`metrics_visibility_${t}`);if(o){const n=JSON.parse(o);return console.log(`Na\u010dteno nastaven\xed viditelnosti metrik pro ${t}:`,n),n}}catch(o){console.error(`Chyba p\u0159i na\u010d\xedt\xe1n\xed nastaven\xed viditelnosti metrik pro ${t}:`,o)}return null}renderAggregatedPerformanceChart(t){console.log("renderAggregatedPerformanceChart - data:",t),this.renderingAggregatedChart=!0,this.aggregatedPerformanceData=t;const o=this.prepareAggregatedPerformanceChartData(t);console.log("renderAggregatedPerformanceChart - chartData:",o);const n=o.labels&&Array.isArray(o.labels)&&o.labels.length>0;if(console.log("renderAggregatedPerformanceChart - hasData:",n,"labels:",o.labels),!n)return this.hasAggregatedChartData=!1,this.renderingAggregatedChart=!1,void(this.aggregatedPerformanceChart&&(this.aggregatedPerformanceChart.destroy(),this.aggregatedPerformanceChart=null));if(this.hasAggregatedChartData=!0,!this.aggregatedPerformanceChartRef)return console.warn("renderAggregatedPerformanceChart - Reference na canvas element nen\xed k dispozici, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderAggregatedPerformanceChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (2000ms)"),this.aggregatedPerformanceChartRef?this.renderAggregatedPerformanceChart(t):(console.error("renderAggregatedPerformanceChart - Canvas element st\xe1le nen\xed k dispozici po 2000ms!"),this.renderingAggregatedChart=!1)},2e3);console.log("renderAggregatedPerformanceChart - Reference na canvas element je k dispozici");try{if(!document.body.contains(this.aggregatedPerformanceChartRef.nativeElement))return console.warn("renderAggregatedPerformanceChart - Canvas element nen\xed v DOM, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderAggregatedPerformanceChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (element nen\xed v DOM)"),this.aggregatedPerformanceChartRef&&document.body.contains(this.aggregatedPerformanceChartRef.nativeElement)?this.renderAggregatedPerformanceChart(t):console.error("renderAggregatedPerformanceChart - Canvas element st\xe1le nen\xed v DOM po 2000ms!")},2e3);const a=this.aggregatedPerformanceChartRef.nativeElement.getContext("2d");if(!a)return void console.error("renderAggregatedPerformanceChart - Nepoda\u0159ilo se z\xedskat kontext canvas elementu!");console.log("renderAggregatedPerformanceChart - Kontext canvas elementu je k dispozici"),this.aggregatedPerformanceChart&&(console.log("renderAggregatedPerformanceChart - Ni\u010d\xedm existuj\xedc\xed graf"),this.aggregatedPerformanceChart.destroy());const s=o.labels.length,l=o.avgData.length,c=o.medianData.length,m=o.p95Data.length,u=o.p99Data.length,g=o.minData.length,h=o.maxData.length,v=o.totalCountData.length;console.log("renderAggregatedPerformanceChart - D\xe9lky pol\xed:",{labelsLength:s,avgDataLength:l,medianDataLength:c,p95DataLength:m,p99DataLength:u,minDataLength:g,maxDataLength:h,totalCountDataLength:v}),l!==s&&(console.warn("renderAggregatedPerformanceChart - avgData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.avgData=this.adjustArrayLength(o.avgData,s)),c!==s&&(console.warn("renderAggregatedPerformanceChart - medianData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.medianData=this.adjustArrayLength(o.medianData,s)),m!==s&&(console.warn("renderAggregatedPerformanceChart - p95Data m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.p95Data=this.adjustArrayLength(o.p95Data,s)),u!==s&&(console.warn("renderAggregatedPerformanceChart - p99Data m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.p99Data=this.adjustArrayLength(o.p99Data,s)),g!==s&&(console.warn("renderAggregatedPerformanceChart - minData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.minData=this.adjustArrayLength(o.minData,s)),h!==s&&(console.warn("renderAggregatedPerformanceChart - maxData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.maxData=this.adjustArrayLength(o.maxData,s)),v!==s&&(console.warn("renderAggregatedPerformanceChart - totalCountData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),o.totalCountData=this.adjustArrayLength(o.totalCountData,s));try{if(console.log("renderAggregatedPerformanceChart - Vytv\xe1\u0159\xedm nov\xfd graf"),!this.aggregatedPerformanceChartRef||!document.body.contains(this.aggregatedPerformanceChartRef.nativeElement))return void console.error("renderAggregatedPerformanceChart - Canvas element ji\u017e nen\xed platn\xfd!");console.log("renderAggregatedPerformanceChart - Canvas rozm\u011bry:",{width:this.aggregatedPerformanceChartRef.nativeElement.width,height:this.aggregatedPerformanceChartRef.nativeElement.height});const f=this.loadMetricsVisibility("aggregated"),z=[{label:"Pr\u016fm\u011br (ms)",data:o.avgData,borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!f&&!f["Pr\u016fm\u011br (ms)"]},{label:"Medi\xe1n (ms)",data:o.medianData,borderColor:"rgba(75, 192, 192, 1)",backgroundColor:"rgba(75, 192, 192, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!f&&!f["Medi\xe1n (ms)"]},{label:"95. percentil (ms)",data:o.p95Data,borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!f&&!f["95. percentil (ms)"]},{label:"99. percentil (ms)",data:o.p99Data,borderColor:"rgba(255, 159, 64, 1)",backgroundColor:"rgba(255, 159, 64, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!f||!f["99. percentil (ms)"]},{label:"Minimum (ms)",data:o.minData,borderColor:"rgba(153, 102, 255, 1)",backgroundColor:"rgba(153, 102, 255, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!f||!f["Minimum (ms)"]},{label:"Maximum (ms)",data:o.maxData,borderColor:"rgba(255, 205, 86, 1)",backgroundColor:"rgba(255, 205, 86, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!f||!f["Maximum (ms)"]},{label:"Po\u010det vol\xe1n\xed",data:o.totalCountData,borderColor:"rgba(201, 203, 207, 1)",backgroundColor:"rgba(201, 203, 207, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!f||!f["Po\u010det vol\xe1n\xed"],yAxisID:"y1"}];console.log("renderAggregatedPerformanceChart - calling getVisibleAxes");const y=this.getVisibleAxes(z);console.log("renderAggregatedPerformanceChart - visibleAxes:",y),this.aggregatedPerformanceChart=new D.kL(a,{type:"line",data:{labels:o.labels,datasets:z},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:o.title||"V\xfdkon v \u010dase"},tooltip:{mode:"index",intersect:!1},legend:{position:"top",labels:{boxWidth:12},onClick:(je,M,Ie)=>{const A=M.datasetIndex,k=Ie.chart;void 0!==A&&(k.isDatasetVisible(A)?(k.hide(A),M.hidden=!0):(k.show(A),M.hidden=!1),this.updateAxisVisibility(k),setTimeout(()=>{this.saveMetricsVisibility("aggregated",k.data.datasets)},0))}}},scales:{x:{display:!0,title:{display:!0,text:"Datum"}},y:{display:y.showLeftAxis,title:{display:y.showLeftAxis,text:"Doba odezvy (ms)"},beginAtZero:!0},y1:{display:y.showRightAxis,position:"right",title:{display:y.showRightAxis,text:"Po\u010det vol\xe1n\xed"},beginAtZero:!0,grid:{drawOnChartArea:!1}}}}}),console.log("renderAggregatedPerformanceChart - Graf \xfasp\u011b\u0161n\u011b vytvo\u0159en"),setTimeout(()=>{this.aggregatedPerformanceChart&&this.saveMetricsVisibility("aggregated",this.aggregatedPerformanceChart.data.datasets),this.renderingAggregatedChart=!1},100)}catch(b){console.error("renderAggregatedPerformanceChart - Chyba p\u0159i vytv\xe1\u0159en\xed grafu:",b),this.renderingAggregatedChart=!1}}catch(a){console.error("renderAggregatedPerformanceChart - Chyba p\u0159i z\xedsk\xe1v\xe1n\xed kontextu canvas elementu:",a),this.renderingAggregatedChart=!1}}renderInstancesComparisonChart(t){if(console.log("renderInstancesComparisonChart - data:",t),this.renderingInstancesComparisonChart=!0,this.instancesComparisonData=t,!(t&&t.labels&&Array.isArray(t.labels)&&t.labels.length>0&&t.datasets&&Array.isArray(t.datasets)&&t.datasets.length>0))return this.hasInstancesComparisonChartData=!1,this.renderingInstancesComparisonChart=!1,void(this.instancesComparisonChart&&(this.instancesComparisonChart.destroy(),this.instancesComparisonChart=null));if(this.hasInstancesComparisonChartData=!0,!this.instancesComparisonChartRef)return console.warn("renderInstancesComparisonChart - Reference na canvas element nen\xed k dispozici, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderInstancesComparisonChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (2000ms)"),this.instancesComparisonChartRef?this.renderInstancesComparisonChart(t):(console.error("renderInstancesComparisonChart - Canvas element st\xe1le nen\xed k dispozici po 2000ms!"),this.renderingInstancesComparisonChart=!1)},2e3);console.log("renderInstancesComparisonChart - Reference na canvas element je k dispozici");try{if(!document.body.contains(this.instancesComparisonChartRef.nativeElement))return console.warn("renderInstancesComparisonChart - Canvas element nen\xed v DOM, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderInstancesComparisonChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (element nen\xed v DOM)"),this.instancesComparisonChartRef&&document.body.contains(this.instancesComparisonChartRef.nativeElement)?this.renderInstancesComparisonChart(t):console.error("renderInstancesComparisonChart - Canvas element st\xe1le nen\xed v DOM po 2000ms!")},2e3);const n=this.instancesComparisonChartRef.nativeElement.getContext("2d");if(!n)return void console.error("renderInstancesComparisonChart - Nepoda\u0159ilo se z\xedskat kontext canvas elementu!");console.log("renderInstancesComparisonChart - Kontext canvas elementu je k dispozici"),this.instancesComparisonChart&&(console.log("renderInstancesComparisonChart - Ni\u010d\xedm existuj\xedc\xed graf"),this.instancesComparisonChart.destroy());try{if(console.log("renderInstancesComparisonChart - Vytv\xe1\u0159\xedm nov\xfd graf"),!this.instancesComparisonChartRef||!document.body.contains(this.instancesComparisonChartRef.nativeElement))return void console.error("renderInstancesComparisonChart - Canvas element ji\u017e nen\xed platn\xfd!");console.log("renderInstancesComparisonChart - Canvas rozm\u011bry:",{width:this.instancesComparisonChartRef.nativeElement.width,height:this.instancesComparisonChartRef.nativeElement.height});const l=this.loadMetricsVisibility("instances-comparison");l&&t.datasets&&t.datasets.forEach(c=>{c.label&&void 0!==l[c.label]&&(c.hidden=!l[c.label])}),this.instancesComparisonChart=new D.kL(n,{type:"bar",data:{labels:t.labels,datasets:t.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:t.title||"Srovn\xe1n\xed v\xfdkonu metod mezi instancemi"},tooltip:{mode:"index",intersect:!1},legend:{position:"top",labels:{boxWidth:12},onClick:(c,m,u)=>{const g=m.datasetIndex,h=u.chart;void 0!==g&&(h.isDatasetVisible(g)?(h.hide(g),m.hidden=!0):(h.show(g),m.hidden=!1),setTimeout(()=>{this.saveMetricsVisibility("instances-comparison",h.data.datasets)},0),h.update())}}},scales:{x:{display:!0,title:{display:!0,text:"Metoda"}},y:{display:!0,title:{display:!0,text:"Doba odezvy (ms)"},beginAtZero:!0}}}}),console.log("renderInstancesComparisonChart - Graf \xfasp\u011b\u0161n\u011b vytvo\u0159en"),setTimeout(()=>{this.instancesComparisonChart&&this.saveMetricsVisibility("instances-comparison",this.instancesComparisonChart.data.datasets),this.renderingInstancesComparisonChart=!1},100)}catch(a){console.error("renderInstancesComparisonChart - Chyba p\u0159i vytv\xe1\u0159en\xed grafu:",a),this.renderingInstancesComparisonChart=!1}}catch(n){console.error("renderInstancesComparisonChart - Chyba p\u0159i z\xedsk\xe1v\xe1n\xed kontextu canvas elementu:",n),this.renderingInstancesComparisonChart=!1}}renderVersionsComparisonChart(t){if(console.log("renderVersionsComparisonChart - data:",t),this.renderingVersionsComparisonChart=!0,this.versionsComparisonData=t,!(t&&t.labels&&Array.isArray(t.labels)&&t.labels.length>0&&t.datasets&&Array.isArray(t.datasets)&&t.datasets.length>0))return this.hasVersionsComparisonChartData=!1,this.renderingVersionsComparisonChart=!1,void(this.versionsComparisonChart&&(this.versionsComparisonChart.destroy(),this.versionsComparisonChart=null));if(this.hasVersionsComparisonChartData=!0,!this.versionsComparisonChartRef)return console.warn("renderVersionsComparisonChart - Reference na canvas element nen\xed k dispozici, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderVersionsComparisonChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (2000ms)"),this.versionsComparisonChartRef?this.renderVersionsComparisonChart(t):(console.error("renderVersionsComparisonChart - Canvas element st\xe1le nen\xed k dispozici po 2000ms!"),this.renderingVersionsComparisonChart=!1)},2e3);console.log("renderVersionsComparisonChart - Reference na canvas element je k dispozici");try{if(!document.body.contains(this.versionsComparisonChartRef.nativeElement))return console.warn("renderVersionsComparisonChart - Canvas element nen\xed v DOM, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderVersionsComparisonChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (element nen\xed v DOM)"),this.versionsComparisonChartRef&&document.body.contains(this.versionsComparisonChartRef.nativeElement)?this.renderVersionsComparisonChart(t):console.error("renderVersionsComparisonChart - Canvas element st\xe1le nen\xed v DOM po 2000ms!")},2e3);const n=this.versionsComparisonChartRef.nativeElement.getContext("2d");if(!n)return void console.error("renderVersionsComparisonChart - Nepoda\u0159ilo se z\xedskat kontext canvas elementu!");console.log("renderVersionsComparisonChart - Kontext canvas elementu je k dispozici"),this.versionsComparisonChart&&(console.log("renderVersionsComparisonChart - Ni\u010d\xedm existuj\xedc\xed graf"),this.versionsComparisonChart.destroy());try{if(console.log("renderVersionsComparisonChart - Vytv\xe1\u0159\xedm nov\xfd graf"),!this.versionsComparisonChartRef||!document.body.contains(this.versionsComparisonChartRef.nativeElement))return void console.error("renderVersionsComparisonChart - Canvas element ji\u017e nen\xed platn\xfd!");console.log("renderVersionsComparisonChart - Canvas rozm\u011bry:",{width:this.versionsComparisonChartRef.nativeElement.width,height:this.versionsComparisonChartRef.nativeElement.height});const l=this.loadMetricsVisibility("versions-comparison");l&&t.datasets&&t.datasets.forEach(c=>{c.label&&void 0!==l[c.label]&&(c.hidden=!l[c.label])}),this.versionsComparisonChart=new D.kL(n,{type:"bar",data:{labels:t.labels,datasets:t.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:t.title||"Srovn\xe1n\xed v\xfdkonu metod mezi verzemi"},tooltip:{mode:"index",intersect:!1},legend:{position:"top",labels:{boxWidth:12},onClick:(c,m,u)=>{const g=m.datasetIndex,h=u.chart;void 0!==g&&(h.isDatasetVisible(g)?(h.hide(g),m.hidden=!0):(h.show(g),m.hidden=!1),setTimeout(()=>{this.saveMetricsVisibility("versions-comparison",h.data.datasets)},0),h.update())}}},scales:{x:{display:!0,title:{display:!0,text:"Metoda"}},y:{display:!0,title:{display:!0,text:"Doba odezvy (ms)"},beginAtZero:!0}}}}),console.log("renderVersionsComparisonChart - Graf \xfasp\u011b\u0161n\u011b vytvo\u0159en"),setTimeout(()=>{this.versionsComparisonChart&&this.saveMetricsVisibility("versions-comparison",this.versionsComparisonChart.data.datasets),this.renderingVersionsComparisonChart=!1},100)}catch(a){console.error("renderVersionsComparisonChart - Chyba p\u0159i vytv\xe1\u0159en\xed grafu:",a),this.renderingVersionsComparisonChart=!1}}catch(n){console.error("renderVersionsComparisonChart - Chyba p\u0159i z\xedsk\xe1v\xe1n\xed kontextu canvas elementu:",n),this.renderingVersionsComparisonChart=!1}}renderMethodDetailChart(t){console.log("renderMethodDetailChart - data:",t),this.renderingMethodDetailChart=!0,this.methodDetailChartData=t;const o=t&&t.labels&&Array.isArray(t.labels)&&t.labels.length>0;if(!o)return this.hasMethodDetailChartData=!1,this.renderingMethodDetailChart=!1,void(this.methodDetailChart&&(this.methodDetailChart.destroy(),this.methodDetailChart=null));if(t.className&&t.methodName&&(console.log("renderMethodDetailChart - Aktualizuji hodnoty filtru podle dat grafu:",{className:t.className,methodName:t.methodName}),(this.methodDetailFilterData.className!==t.className||this.methodDetailFilterData.methodName!==t.methodName)&&(this.methodDetailFilterData.className=t.className,this.methodDetailFilterData.methodName=t.methodName,this.saveFilter("performance_method_detail",this.methodDetailFilterData))),this.hasMethodDetailChartData=!0,!this.methodDetailChartRef)return console.warn("renderMethodDetailChart - Reference na canvas element nen\xed k dispozici, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderMethodDetailChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (2000ms)"),this.methodDetailChartRef?this.renderMethodDetailChart(t):(console.error("renderMethodDetailChart - Canvas element st\xe1le nen\xed k dispozici po 2000ms!"),this.renderingMethodDetailChart=!1)},2e3);console.log("renderMethodDetailChart - Reference na canvas element je k dispozici");try{if(!document.body.contains(this.methodDetailChartRef.nativeElement))return console.warn("renderMethodDetailChart - Canvas element nen\xed v DOM, zkus\xedm pozd\u011bji"),void setTimeout(()=>{console.log("renderMethodDetailChart - Zkou\u0161\xedm znovu po del\u0161\xedm timeoutu (element nen\xed v DOM)"),this.methodDetailChartRef&&document.body.contains(this.methodDetailChartRef.nativeElement)?this.renderMethodDetailChart(t):console.error("renderMethodDetailChart - Canvas element st\xe1le nen\xed v DOM po 2000ms!")},2e3);const n=this.methodDetailChartRef.nativeElement.getContext("2d");if(!n)return void console.error("renderMethodDetailChart - Nepoda\u0159ilo se z\xedskat kontext canvas elementu!");console.log("renderMethodDetailChart - Kontext canvas elementu je k dispozici"),this.methodDetailChart&&(console.log("renderMethodDetailChart - Ni\u010d\xedm existuj\xedc\xed graf"),this.methodDetailChart.destroy());try{if(console.log("renderMethodDetailChart - Vytv\xe1\u0159\xedm nov\xfd graf"),!this.methodDetailChartRef||!document.body.contains(this.methodDetailChartRef.nativeElement))return void console.error("renderMethodDetailChart - Canvas element ji\u017e nen\xed platn\xfd!");if(console.log("renderMethodDetailChart - Canvas rozm\u011bry:",{width:this.methodDetailChartRef.nativeElement.width,height:this.methodDetailChartRef.nativeElement.height}),o){const m=t.labels.length;(!t.avgData||t.avgData.length!==m)&&(console.warn("renderMethodDetailChart - avgData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),t.avgData=this.adjustArrayLength(t.avgData||[],m)),(!t.medianData||t.medianData.length!==m)&&(console.warn("renderMethodDetailChart - medianData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),t.medianData=this.adjustArrayLength(t.medianData||[],m)),(!t.p95Data||t.p95Data.length!==m)&&(console.warn("renderMethodDetailChart - p95Data m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),t.p95Data=this.adjustArrayLength(t.p95Data||[],m)),(!t.totalCountData||t.totalCountData.length!==m)&&(console.warn("renderMethodDetailChart - totalCountData m\xe1 jinou d\xe9lku ne\u017e labels, upravuji"),t.totalCountData=this.adjustArrayLength(t.totalCountData||[],m))}const l=this.loadMetricsVisibility("method-detail");this.methodDetailChart=new D.kL(n,{type:"line",data:{labels:t.labels,datasets:[{label:"Pr\u016fm\u011br (ms)",data:t.avgData,borderColor:"rgba(54, 162, 235, 1)",backgroundColor:"rgba(54, 162, 235, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!l&&!l["Pr\u016fm\u011br (ms)"]},{label:"Medi\xe1n (ms)",data:t.medianData,borderColor:"rgba(75, 192, 192, 1)",backgroundColor:"rgba(75, 192, 192, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!l&&!l["Medi\xe1n (ms)"]},{label:"95. percentil (ms)",data:t.p95Data,borderColor:"rgba(255, 99, 132, 1)",backgroundColor:"rgba(255, 99, 132, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!l&&!l["95. percentil (ms)"]},{label:"Po\u010det vol\xe1n\xed",data:t.totalCountData,borderColor:"rgba(201, 203, 207, 1)",backgroundColor:"rgba(201, 203, 207, 0.2)",borderWidth:2,tension:.3,fill:!1,hidden:!!l&&!l["Po\u010det vol\xe1n\xed"],yAxisID:"y1"}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:t.title||"Detail metody"},tooltip:{mode:"index",intersect:!1},legend:{position:"top",labels:{boxWidth:12},onClick:(m,u,g)=>{const h=u.datasetIndex,v=g.chart;void 0!==h&&(v.isDatasetVisible(h)?(v.hide(h),u.hidden=!0):(v.show(h),u.hidden=!1),setTimeout(()=>{this.saveMetricsVisibility("method-detail",v.data.datasets)},0),v.update())}}},scales:{x:{display:!0,title:{display:!0,text:"Datum"}},y:{display:!0,title:{display:!0,text:"Doba odezvy (ms)"},beginAtZero:!0},y1:{display:!0,position:"right",title:{display:!0,text:"Po\u010det vol\xe1n\xed"},beginAtZero:!0,grid:{drawOnChartArea:!1}}}}}),console.log("renderMethodDetailChart - Graf \xfasp\u011b\u0161n\u011b vytvo\u0159en"),setTimeout(()=>{this.methodDetailChart&&this.saveMetricsVisibility("method-detail",this.methodDetailChart.data.datasets),this.renderingMethodDetailChart=!1},100)}catch(a){console.error("renderMethodDetailChart - Chyba p\u0159i vytv\xe1\u0159en\xed grafu:",a),this.renderingMethodDetailChart=!1}}catch(n){console.error("renderMethodDetailChart - Chyba p\u0159i z\xedsk\xe1v\xe1n\xed kontextu canvas elementu:",n),this.renderingMethodDetailChart=!1}}prepareAggregatedPerformanceChartData(t){console.log("prepareAggregatedPerformanceChartData - vstupn\xed data:",t);const o={labels:[],avgData:[],medianData:[],p95Data:[],p99Data:[],minData:[],maxData:[],totalCountData:[],title:"V\xfdkon v \u010dase"};if(!t)return console.warn("prepareAggregatedPerformanceChartData - Chyb\xed data pro graf"),o;try{if(t.labels&&Array.isArray(t.labels)&&t.labels.length>0)console.log("prepareAggregatedPerformanceChartData - Pou\u017e\xedv\xe1m p\u0159edp\u0159ipraven\xe1 data z API"),o.labels=t.labels,o.avgData=t.avgData||[],o.medianData=t.medianData||[],o.p95Data=t.p95Data||[],o.p99Data=t.p99Data||[],o.minData=t.minData||[],o.maxData=t.maxData||[],o.totalCountData=t.totalCountData||[],t.title&&(o.title=t.title);else if(t.dailyData&&Array.isArray(t.dailyData)&&t.dailyData.length>0){console.log("prepareAggregatedPerformanceChartData - Zpracov\xe1v\xe1m denn\xed data");const n=t.dailyData;n.sort((a,s)=>(a.date?new Date(a.date).getTime():0)-(s.date?new Date(s.date).getTime():0)),o.labels=n.map(a=>{if(a.date){const s=new Date(a.date);return this.formatDate(s)}return""}).filter(a=>""!==a),o.avgData=n.map(a=>void 0!==a.avg?a.avg:0),o.medianData=n.map(a=>void 0!==a.median?a.median:0),o.p95Data=n.map(a=>void 0!==a.percentil95?a.percentil95:0),o.p99Data=n.map(a=>void 0!==a.percentil99?a.percentil99:0),o.minData=n.map(a=>void 0!==a.min?a.min:0),o.maxData=n.map(a=>void 0!==a.max?a.max:0),o.totalCountData=n.map(a=>void 0!==a.totalCount?a.totalCount:0),t.title&&(o.title=t.title)}else if(console.warn("prepareAggregatedPerformanceChartData - Nezn\xe1m\xfd form\xe1t dat"),"object"==typeof t)for(const n in t)"labels"===n&&Array.isArray(t[n])?o.labels=t[n].map(a=>String(a)):"avgData"===n&&Array.isArray(t[n])?o.avgData=t[n].map(a=>Number(a)||0):"medianData"===n&&Array.isArray(t[n])?o.medianData=t[n].map(a=>Number(a)||0):"p95Data"===n&&Array.isArray(t[n])?o.p95Data=t[n].map(a=>Number(a)||0):"p99Data"===n&&Array.isArray(t[n])?o.p99Data=t[n].map(a=>Number(a)||0):"minData"===n&&Array.isArray(t[n])?o.minData=t[n].map(a=>Number(a)||0):"maxData"===n&&Array.isArray(t[n])?o.maxData=t[n].map(a=>Number(a)||0):"totalCountData"===n&&Array.isArray(t[n])?o.totalCountData=t[n].map(a=>Number(a)||0):"title"===n&&"string"==typeof t[n]&&(o.title=t[n])}catch(n){console.error("prepareAggregatedPerformanceChartData - Chyba p\u0159i zpracov\xe1n\xed dat:",n)}return console.log("prepareAggregatedPerformanceChartData - v\xfdstupn\xed data:",o),o}initPopovers(){console.log("initPopovers - Inicializuji popovery");const t={"aggregated-performance":"Graf zobrazuje agregovan\xe9 v\xfdkonnostn\xed metriky DIS metod v \u010dase. Pro ka\u017ed\xfd den jsou zobrazeny hodnoty jako pr\u016fm\u011br, medi\xe1n, 95. percentil, 99. percentil, minimum, maximum a po\u010det vol\xe1n\xed. Kliknut\xedm na legendu m\u016f\u017eete zobrazit nebo skr\xfdt jednotliv\xe9 metriky. V\xfdchoz\xed zobrazen\xed obsahuje pr\u016fm\u011br, medi\xe1n a 95. percentil, ostatn\xed metriky jsou skryt\xe9.","instances-comparison":"Graf umo\u017e\u0148uje srovn\xe1n\xed v\xfdkonu stejn\xfdch DIS metod mezi r\u016fzn\xfdmi instancemi. Vyberte alespo\u0148 dv\u011b instance, kter\xe9 chcete porovnat, a voliteln\u011b filtrujte podle konkr\xe9tn\xed t\u0159\xeddy nebo metody. Graf zobrazuje pr\u016fm\u011brnou dobu odezvy metod pro ka\u017edou vybranou instanci.","versions-comparison":"Graf umo\u017e\u0148uje srovn\xe1n\xed v\xfdkonu DIS metod mezi r\u016fzn\xfdmi verzemi jedn\xe9 instance. Vyberte instanci a voliteln\u011b filtrujte podle konkr\xe9tn\xed t\u0159\xeddy nebo metody. Graf zobrazuje pr\u016fm\u011brnou dobu odezvy metod pro ka\u017edou verzi aplikace v r\xe1mci vybran\xe9 instance.","slowest-methods":"Tabulka zobrazuje nejpomalej\u0161\xed DIS metody se\u0159azen\xe9 podle 95. percentilu doby odezvy. Pro ka\u017edou metodu jsou zobrazeny statistick\xe9 \xfadaje jako po\u010det vol\xe1n\xed, minim\xe1ln\xed, maxim\xe1ln\xed a pr\u016fm\u011brn\xe1 doba odezvy, medi\xe1n a 95. percentil. Kliknut\xedm na tla\u010d\xedtko s ikonou informace zobraz\xedte detail konkr\xe9tn\xed metody.",methods:"Tabulka zobrazuje DIS metody podle zvolen\xe9ho filtru (nejpomalej\u0161\xed, nej\u010dast\u011bji volan\xe9 nebo v\u0161echny). Pro ka\u017edou metodu jsou zobrazeny statistick\xe9 \xfadaje jako po\u010det vol\xe1n\xed, minim\xe1ln\xed, maxim\xe1ln\xed a pr\u016fm\u011brn\xe1 doba odezvy, medi\xe1n a 95. percentil. Kliknut\xedm na tla\u010d\xedtko s ikonou informace zobraz\xedte detail konkr\xe9tn\xed metody.","most-called-methods":"Tabulka zobrazuje nej\u010dast\u011bji volan\xe9 DIS metody se\u0159azen\xe9 podle po\u010dtu vol\xe1n\xed. Pro ka\u017edou metodu jsou zobrazeny statistick\xe9 \xfadaje jako po\u010det vol\xe1n\xed, minim\xe1ln\xed, maxim\xe1ln\xed a pr\u016fm\u011brn\xe1 doba odezvy, medi\xe1n a 95. percentil. Kliknut\xedm na tla\u010d\xedtko s ikonou informace zobraz\xedte detail konkr\xe9tn\xed metody.","all-methods":"Tabulka zobrazuje v\u0161echny DIS metody. Pro ka\u017edou metodu jsou zobrazeny statistick\xe9 \xfadaje jako po\u010det vol\xe1n\xed, minim\xe1ln\xed, maxim\xe1ln\xed a pr\u016fm\u011brn\xe1 doba odezvy, medi\xe1n a 95. percentil. Kliknut\xedm na tla\u010d\xedtko s ikonou informace zobraz\xedte detail konkr\xe9tn\xed metody.","method-detail":"Graf zobrazuje v\xfdkon konkr\xe9tn\xed DIS metody v \u010dase. Pro ka\u017ed\xfd den jsou zobrazeny hodnoty jako pr\u016fm\u011br, medi\xe1n, 95. percentil, 99. percentil, minimum, maximum a po\u010det vol\xe1n\xed. Kliknut\xedm na legendu m\u016f\u017eete zobrazit nebo skr\xfdt jednotliv\xe9 metriky. Nad grafem jsou zobrazeny souhrnn\xe9 statistiky metody za cel\xe9 zvolen\xe9 obdob\xed.","avg-response-time":"Pr\u016fm\u011brn\xe1 doba odezvy metody v milisekund\xe1ch za cel\xe9 zvolen\xe9 obdob\xed. Pr\u016fm\u011br je vypo\u010d\xedt\xe1n jako aritmetick\xfd pr\u016fm\u011br v\u0161ech nam\u011b\u0159en\xfdch hodnot. M\u016f\u017ee b\xfdt ovlivn\u011bn extr\xe9mn\xedmi hodnotami, proto je vhodn\xe9 sledovat tak\xe9 medi\xe1n.","median-response-time":"Medi\xe1n doby odezvy metody v milisekund\xe1ch za cel\xe9 zvolen\xe9 obdob\xed. Medi\xe1n p\u0159edstavuje prost\u0159edn\xed hodnotu ze v\u0161ech nam\u011b\u0159en\xfdch \u010das\u016f (50. percentil). Polovina vol\xe1n\xed byla rychlej\u0161\xed a polovina pomalej\u0161\xed ne\u017e tato hodnota. Medi\xe1n nen\xed ovlivn\u011bn extr\xe9mn\xedmi hodnotami, proto l\xe9pe reprezentuje typickou dobu odezvy ne\u017e pr\u016fm\u011br.","p95-response-time":"95. percentil doby odezvy metody v milisekund\xe1ch za cel\xe9 zvolen\xe9 obdob\xed. Tato hodnota znamen\xe1, \u017ee 95% v\u0161ech vol\xe1n\xed metody bylo rychlej\u0161\xedch ne\u017e tato hodnota. Zb\xfdvaj\xedc\xedch 5% vol\xe1n\xed bylo pomalej\u0161\xedch. Tento ukazatel je d\u016fle\u017eit\xfd pro identifikaci probl\xe9m\u016f s v\xfdkonem, kter\xe9 ovliv\u0148uj\xed men\u0161\xed \u010d\xe1st u\u017eivatel\u016f.","total-calls":"Celkov\xfd po\u010det vol\xe1n\xed metody za zvolen\xe9 \u010dasov\xe9 obdob\xed. Tento \xfadaj ukazuje, jak \u010dasto je metoda pou\u017e\xedv\xe1na. Vysok\xfd po\u010det vol\xe1n\xed v kombinaci s del\u0161\xed dobou odezvy m\u016f\u017ee indikovat potenci\xe1ln\xed probl\xe9m s v\xfdkonem aplikace."};try{setTimeout(()=>{try{if(typeof bootstrap>"u")return void console.error("initPopovers - Bootstrap nen\xed k dispozici!");if(typeof bootstrap.Popover>"u")return void console.error("initPopovers - Bootstrap.Popover nen\xed k dispozici!");[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).forEach(a=>{try{const s=bootstrap.Popover.getInstance(a);s&&s.dispose()}catch(s){console.warn("initPopovers - Chyba p\u0159i ru\u0161en\xed existuj\xedc\xedho popoveru:",s)}});const n=[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));console.log("initPopovers - Nalezeno",n.length,"element\u016f s popovery"),n.forEach(a=>{try{const s=a.getAttribute("data-help-type"),l=t[s]||"N\xe1pov\u011bda nen\xed k dispozici.";let c="";switch(s){case"aggregated-performance":c="Agregovan\xfd v\xfdkon v \u010dase";break;case"instances-comparison":c="Srovn\xe1n\xed v\xfdkonu metod mezi instancemi";break;case"versions-comparison":c="Srovn\xe1n\xed v\xfdkonu metod mezi verzemi";break;case"slowest-methods":c="Nejpomalej\u0161\xed metody";break;case"most-called-methods":c="Nej\u010dast\u011bji volan\xe9 metody";break;case"all-methods":c="V\u0161echny metody";break;case"method-detail":c="Detail metody";break;case"avg-response-time":c="Pr\u016fm\u011brn\xe1 doba odezvy";break;case"median-response-time":c="Medi\xe1n doby odezvy";break;case"p95-response-time":c="95. percentil";break;case"total-calls":c="Celkov\xfd po\u010det vol\xe1n\xed";break;default:c="N\xe1pov\u011bda"}new bootstrap.Popover(a,{container:"body",trigger:"hover",placement:"top",title:c,content:l,html:!0})}catch(s){console.error("initPopovers - Chyba p\u0159i inicializaci popoveru:",s)}}),console.log("initPopovers - Popovery \xfasp\u011b\u0161n\u011b inicializov\xe1ny")}catch(o){console.error("initPopovers - Chyba p\u0159i inicializaci popover\u016f:",o)}},500)}catch(o){console.error("initPopovers - Chyba p\u0159i inicializaci popover\u016f:",o)}}formatDate(t){return this.datePipe.transform(t,"dd.MM.yyyy")||""}adjustArrayLength(t,o){if(!t)return new Array(o).fill(0);if(t.length===o)return t;if(t.length>o)return t.slice(0,o);const n=[...t];for(;n.length<o;)n.push(0);return n}getVisibleAxes(t,o){let n=!1,a=!1;return console.log("getVisibleAxes - datasets:",t),t.forEach((s,l)=>{const c=o?o.isDatasetVisible(l):!s.hidden;console.log(`getVisibleAxes - dataset: ${s.label}, hidden: ${s.hidden}, isVisible: ${c}, yAxisID: ${s.yAxisID}`),c&&("y1"===s.yAxisID?(a=!0,console.log("getVisibleAxes - showRightAxis = true")):(n=!0,console.log("getVisibleAxes - showLeftAxis = true")))}),console.log(`getVisibleAxes - result: showLeftAxis=${n}, showRightAxis=${a}`),{showLeftAxis:n,showRightAxis:a}}updateAxisVisibility(t){if(console.log("updateAxisVisibility - called"),!t||!t.data||!t.data.datasets)return void console.log("updateAxisVisibility - chart or data missing");const o=this.getVisibleAxes(t.data.datasets,t);console.log("updateAxisVisibility - visibleAxes:",o),t.options&&t.options.scales&&(t.options.scales.y&&(console.log(`updateAxisVisibility - setting y.display = ${o.showLeftAxis}`),t.options.scales.y.display=o.showLeftAxis,t.options.scales.y.title.display=o.showLeftAxis),t.options.scales.y1&&(console.log(`updateAxisVisibility - setting y1.display = ${o.showRightAxis}`),t.options.scales.y1.display=o.showRightAxis,t.options.scales.y1.title.display=o.showRightAxis)),console.log("updateAxisVisibility - calling chart.update()"),t.update()}};let r=d;return d.\u0275fac=function(o){return new(o||d)(e.Y36(I),e.Y36(T._),e.Y36(_.uU),e.Y36(j.i),e.Y36(e.sBO),e.Y36(S.U))},d.\u0275cmp=e.Xpm({type:d,selectors:[["app-performance"]],viewQuery:function(o,n){if(1&o&&(e.Gf(E,5),e.Gf(V,5),e.Gf(O,5),e.Gf(U,5)),2&o){let a;e.iGM(a=e.CRH())&&(n.aggregatedPerformanceChartRef=a.first),e.iGM(a=e.CRH())&&(n.instancesComparisonChartRef=a.first),e.iGM(a=e.CRH())&&(n.versionsComparisonChartRef=a.first),e.iGM(a=e.CRH())&&(n.methodDetailChartRef=a.first)}},decls:11,vars:8,consts:[[1,"container"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],[3,"tabs","activeTabId","tabChange"],["class","alert alert-danger alert-dismissible fade show","role","alert",4,"ngIf"],[4,"ngIf"],["role","alert",1,"alert","alert-danger","alert-dismissible","fade","show"],["type","button",1,"btn-close",3,"click"],[3,"entityType","fields","filterChange"],["class","text-center my-5",4,"ngIf"],[1,"text-center","my-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2"],[1,"card","mb-4"],[1,"card-header","d-flex","justify-content-between","align-items-center"],["data-bs-toggle","popover","data-help-type","aggregated-performance",1,"mb-0",2,"cursor","help"],["title","Zobrazit graf na celou obrazovku",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-arrows-fullscreen"],[1,"card-body"],[1,"chart-container",2,"position","relative","height","400px","width","100%"],["class","chart-loading-overlay",4,"ngIf"],["class","no-data-message",4,"ngIf"],["width","800","height","400","style","display: block;",4,"ngIf"],["data-bs-toggle","popover","data-help-type","slowest-methods",1,"mb-0",2,"cursor","help"],[1,"table-responsive"],[1,"table","table-striped","table-hover"],[1,"dark-header","table-header-override"],[1,"dark-header-row"],[1,"sortable-header",3,"click"],[1,"bi"],["style","cursor: pointer;",4,"ngFor","ngForOf"],[1,"chart-loading-overlay"],[1,"no-data-message"],[1,"bi","bi-bar-chart-line"],["width","800","height","400",2,"display","block"],["aggregatedPerformanceChart",""],[2,"cursor","pointer"],["title","Zobrazit detail",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-info-circle"],["colspan","10",1,"text-center"],["data-bs-toggle","popover","data-help-type","instances-comparison",1,"mb-0",2,"cursor","help"],["instancesComparisonChart",""],["data-bs-toggle","popover","data-help-type","versions-comparison",1,"mb-0",2,"cursor","help"],["versionsComparisonChart",""],[1,"mb-0",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","slowest-methods",4,"ngIf"],["data-bs-toggle","popover","data-help-type","most-called-methods",4,"ngIf"],["data-bs-toggle","popover","data-help-type","all-methods",4,"ngIf"],["data-bs-toggle","popover","data-help-type","slowest-methods"],["data-bs-toggle","popover","data-help-type","most-called-methods"],["data-bs-toggle","popover","data-help-type","all-methods"],[1,"row","mb-4"],[1,"col-md-3","mb-3"],[1,"card","h-100"],[1,"card-body","text-center"],["data-bs-toggle","popover","data-help-type","avg-response-time",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0"],[1,"text-muted"],["data-bs-toggle","popover","data-help-type","median-response-time",1,"card-title",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","p95-response-time",1,"card-title",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","total-calls",1,"card-title",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","method-detail",1,"mb-0",2,"cursor","help"],["methodDetailChart",""]],template:function(o,n){1&o&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h2"),e._uU(3,"V\xfdkon DIS"),e.qZA()(),e.TgZ(4,"app-tab-navigation",2),e.NdJ("tabChange",function(s){return n.changeTab(s)}),e.qZA(),e.YNc(5,J,3,1,"div",3),e.YNc(6,H,4,4,"div",4),e.YNc(7,oe,4,4,"div",4),e.YNc(8,le,4,4,"div",4),e.YNc(9,ke,4,4,"div",4),e.YNc(10,we,4,4,"div",4),e.qZA()),2&o&&(e.xp6(4),e.Q6J("tabs",n.tabs)("activeTabId",n.activeTab),e.xp6(1),e.Q6J("ngIf",n.error),e.xp6(1),e.Q6J("ngIf","aggregated"===n.activeTab),e.xp6(1),e.Q6J("ngIf","instances-comparison"===n.activeTab),e.xp6(1),e.Q6J("ngIf","versions-comparison"===n.activeTab),e.xp6(1),e.Q6J("ngIf","methods"===n.activeTab),e.xp6(1),e.Q6J("ngIf","method-detail"===n.activeTab))},dependencies:[_.sg,_.O5,x.W,q.j,_.JJ],styles:[".chart-container[_ngcontent-%COMP%]{position:relative;height:400px;width:100%}.chart-loading-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background-color:#fffc;display:flex;flex-direction:column;justify-content:center;align-items:center;z-index:10}.no-data-message[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;z-index:5}.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:1rem;color:#6c757d}.no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;color:#6c757d}@media (prefers-color-scheme: dark){.chart-loading-overlay[_ngcontent-%COMP%]{background-color:#212529cc}.no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .no-data-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#adb5bd}}.table-responsive[_ngcontent-%COMP%]{overflow-x:auto}.form-label[_ngcontent-%COMP%]{font-weight:500}.card-title[_ngcontent-%COMP%]{cursor:help}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{white-space:nowrap}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{vertical-align:middle}.btn-outline-info[_ngcontent-%COMP%]{color:#0dcaf0;border-color:#0dcaf0}.btn-outline-info[_ngcontent-%COMP%]:hover{color:#fff;background-color:#0dcaf0;border-color:#0dcaf0}.sortable-header[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;position:relative}.sortable-header[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.8rem;margin-left:5px;opacity:.7;display:inline-block;vertical-align:middle}.sortable-header[_ngcontent-%COMP%]   i.bi-sort-up[_ngcontent-%COMP%], .sortable-header[_ngcontent-%COMP%]   i.bi-sort-down[_ngcontent-%COMP%]{opacity:1;color:#fff;font-size:1rem}"]}),r})();var Pe=p(4466);let Ne=(()=>{const d=class{};let r=d;return d.\u0275fac=function(o){return new(o||d)},d.\u0275mod=e.oAB({type:d}),d.\u0275inj=e.cJS({providers:[_.uU],imports:[_.ez,w.u5,w.UX,Pe.m,P.Bz.forChild([{path:"",component:ze}])]}),r})()}}]);