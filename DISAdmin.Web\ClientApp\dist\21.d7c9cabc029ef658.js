"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[21],{21:(H,h,l)=>{l.r(h),l.d(h,{InstanceMetricsModule:()=>F});var p=l(6895),g=l(6123),u=l(433),v=l(5861),d=l(3192),t=l(1571),b=l(4612),C=l(985),Z=l(9523),f=l(9032),y=l(8253),A=l(9991),_=l(18);const T=["apiCallsChart"],U=["apiResponseTimeChart"],x=["errorRateChart"],q=["resourceUsageChart"];function z(s,o){if(1&s){const i=t.EpF();t.TgZ(0,"div",34),t._uU(1),t.TgZ(2,"button",35),t.NdJ("click",function(){t.CHM(i);const e=t.oxw();return t.KtG(e.error=null)}),t.qZA()()}if(2&s){const i=t.oxw();t.xp6(1),t.hij(" ",i.error," ")}}function D(s,o){1&s&&(t.TgZ(0,"div",36)(1,"div",37)(2,"span",38),t._uU(3,"Na\u010d\xedt\xe1n\xed..."),t.qZA()(),t.TgZ(4,"p",39),t._uU(5,"Na\u010d\xedt\xe1n\xed metrik..."),t.qZA()())}const m=function(s,o,i){return{"text-success":s,"text-warning":o,"text-danger":i}};function E(s,o){if(1&s&&(t.TgZ(0,"tr")(1,"td"),t._uU(2),t.ALo(3,"localDate"),t.qZA(),t.TgZ(4,"td"),t._uU(5),t.qZA(),t.TgZ(6,"td"),t._uU(7),t.qZA(),t.TgZ(8,"td",19),t._uU(9),t.qZA(),t.TgZ(10,"td"),t._uU(11),t.qZA()()),2&s){const i=o.$implicit;t.xp6(2),t.Oqu(t.xi3(3,6,i.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(3),t.Oqu(i.endpoint),t.xp6(2),t.Oqu(i.method),t.xp6(1),t.Q6J("ngClass",t.kEZ(9,m,i.statusCode<400,i.statusCode>=400&&i.statusCode<500,i.statusCode>=500)),t.xp6(1),t.Oqu(i.statusCode),t.xp6(2),t.Oqu(i.responseTime)}}function R(s,o){1&s&&(t.TgZ(0,"tr")(1,"td",59),t._uU(2,"\u017d\xe1dn\xe1 API vol\xe1n\xed k zobrazen\xed"),t.qZA()())}function k(s,o){if(1&s&&(t.TgZ(0,"tr")(1,"td"),t._uU(2),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA(),t.TgZ(5,"td"),t._uU(6),t.qZA(),t.TgZ(7,"td"),t._uU(8),t.qZA(),t.TgZ(9,"td"),t._uU(10),t.qZA(),t.TgZ(11,"td"),t._uU(12),t.qZA(),t.TgZ(13,"td"),t._uU(14),t.qZA(),t.TgZ(15,"td"),t._uU(16),t.qZA(),t.TgZ(17,"td"),t._uU(18),t.qZA()()),2&s){const i=o.$implicit;t.xp6(2),t.Oqu(i.className),t.xp6(2),t.Oqu(i.methodName),t.xp6(2),t.Oqu(i.totalCount),t.xp6(2),t.Oqu(i.min),t.xp6(2),t.Oqu(i.max),t.xp6(2),t.Oqu(i.avg),t.xp6(2),t.Oqu(i.median),t.xp6(2),t.Oqu(i.percentil95),t.xp6(2),t.Oqu(i.versionNumber||"N/A")}}function I(s,o){1&s&&(t.TgZ(0,"tr")(1,"td",60),t._uU(2,"\u017d\xe1dn\xe9 v\xfdkonnostn\xed metriky k zobrazen\xed"),t.qZA()())}function M(s,o){if(1&s&&(t.TgZ(0,"tr")(1,"td"),t._uU(2),t.ALo(3,"localDate"),t.qZA(),t.TgZ(4,"td"),t._uU(5),t.qZA(),t.TgZ(6,"td"),t._uU(7),t.qZA(),t.TgZ(8,"td"),t._uU(9),t.qZA()()),2&s){const i=o.$implicit;t.xp6(2),t.Oqu(t.xi3(3,4,i.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(3),t.Oqu(i.type),t.xp6(2),t.Oqu(i.message),t.xp6(2),t.Oqu(i.source)}}function j(s,o){1&s&&(t.TgZ(0,"tr")(1,"td",61),t._uU(2,"\u017d\xe1dn\xe9 chyby k zobrazen\xed"),t.qZA()())}function P(s,o){if(1&s){const i=t.EpF();t.TgZ(0,"div")(1,"div",17)(2,"div",40)(3,"div",22)(4,"div",41)(5,"h5",42),t._uU(6,"API vol\xe1n\xed v \u010dase"),t.qZA(),t.TgZ(7,"button",43),t.NdJ("click",function(){t.CHM(i);const e=t.MAs(12),a=t.oxw();return t.KtG(a.openFullscreenChart(e,"API vol\xe1n\xed v \u010dase"))}),t._UZ(8,"i",44),t.qZA()(),t.TgZ(9,"div",8)(10,"div",45),t._UZ(11,"canvas",null,46),t.qZA()()()(),t.TgZ(13,"div",40)(14,"div",22)(15,"div",41)(16,"h5",47),t._uU(17,"Odezva DIS metod"),t.qZA(),t.TgZ(18,"button",43),t.NdJ("click",function(){t.CHM(i);const e=t.MAs(23),a=t.oxw();return t.KtG(a.openFullscreenChart(e,"Odezva DIS metod"))}),t._UZ(19,"i",44),t.qZA()(),t.TgZ(20,"div",8)(21,"div",45),t._UZ(22,"canvas",null,48),t.qZA()()()()(),t.TgZ(24,"div",17)(25,"div",40)(26,"div",22)(27,"div",41)(28,"h5",49),t._uU(29,"Chybovost v \u010dase"),t.qZA(),t.TgZ(30,"button",43),t.NdJ("click",function(){t.CHM(i);const e=t.MAs(35),a=t.oxw();return t.KtG(a.openFullscreenChart(e,"Chybovost v \u010dase"))}),t._UZ(31,"i",44),t.qZA()(),t.TgZ(32,"div",8)(33,"div",45),t._UZ(34,"canvas",null,50),t.qZA()()()(),t.TgZ(36,"div",40)(37,"div",22)(38,"div",41)(39,"h5",51),t._uU(40,"Vyu\u017eit\xed zdroj\u016f"),t.qZA(),t.TgZ(41,"button",43),t.NdJ("click",function(){t.CHM(i);const e=t.MAs(46),a=t.oxw();return t.KtG(a.openFullscreenChart(e,"Vyu\u017eit\xed zdroj\u016f"))}),t._UZ(42,"i",44),t.qZA()(),t.TgZ(43,"div",8)(44,"div",45),t._UZ(45,"canvas",null,52),t.qZA()()()()(),t.TgZ(47,"div",5)(48,"div",6)(49,"h5",53),t._uU(50,"Posledn\xed API vol\xe1n\xed"),t.qZA()(),t.TgZ(51,"div",8)(52,"div",54)(53,"table",55)(54,"thead")(55,"tr")(56,"th"),t._uU(57,"Datum a \u010das"),t.qZA(),t.TgZ(58,"th"),t._uU(59,"Endpoint"),t.qZA(),t.TgZ(60,"th"),t._uU(61,"Metoda"),t.qZA(),t.TgZ(62,"th"),t._uU(63,"Status"),t.qZA(),t.TgZ(64,"th"),t._uU(65,"Doba odezvy (ms)"),t.qZA()()(),t.TgZ(66,"tbody"),t.YNc(67,E,12,13,"tr",56),t.YNc(68,R,3,0,"tr",33),t.qZA()()()()(),t.TgZ(69,"div",5)(70,"div",41)(71,"h5",57),t._uU(72,"V\xfdkonnostn\xed metriky DIS metod"),t.qZA()(),t.TgZ(73,"div",8)(74,"div",54)(75,"table",55)(76,"thead")(77,"tr")(78,"th"),t._uU(79,"T\u0159\xedda"),t.qZA(),t.TgZ(80,"th"),t._uU(81,"Metoda"),t.qZA(),t.TgZ(82,"th"),t._uU(83,"Po\u010det vol\xe1n\xed"),t.qZA(),t.TgZ(84,"th"),t._uU(85,"Min (ms)"),t.qZA(),t.TgZ(86,"th"),t._uU(87,"Max (ms)"),t.qZA(),t.TgZ(88,"th"),t._uU(89,"Pr\u016fm\u011br (ms)"),t.qZA(),t.TgZ(90,"th"),t._uU(91,"Medi\xe1n (ms)"),t.qZA(),t.TgZ(92,"th"),t._uU(93,"95. percentil (ms)"),t.qZA(),t.TgZ(94,"th"),t._uU(95,"Verze"),t.qZA()()(),t.TgZ(96,"tbody"),t.YNc(97,k,19,9,"tr",56),t.YNc(98,I,3,0,"tr",33),t.qZA()()()()(),t.TgZ(99,"div",5)(100,"div",6)(101,"h5",58),t._uU(102,"Posledn\xed chyby"),t.qZA()(),t.TgZ(103,"div",8)(104,"div",54)(105,"table",55)(106,"thead")(107,"tr")(108,"th"),t._uU(109,"Datum a \u010das"),t.qZA(),t.TgZ(110,"th"),t._uU(111,"Typ"),t.qZA(),t.TgZ(112,"th"),t._uU(113,"Zpr\xe1va"),t.qZA(),t.TgZ(114,"th"),t._uU(115,"Zdroj"),t.qZA()()(),t.TgZ(116,"tbody"),t.YNc(117,M,10,7,"tr",56),t.YNc(118,j,3,0,"tr",33),t.qZA()()()()()()}if(2&s){const i=t.oxw();t.xp6(67),t.Q6J("ngForOf",i.metrics.recentApiCalls),t.xp6(1),t.Q6J("ngIf",!i.metrics.recentApiCalls||0===i.metrics.recentApiCalls.length),t.xp6(29),t.Q6J("ngForOf",i.metrics.performanceMetrics),t.xp6(1),t.Q6J("ngIf",!i.metrics.performanceMetrics||0===i.metrics.performanceMetrics.length),t.xp6(19),t.Q6J("ngForOf",i.metrics.recentErrors),t.xp6(1),t.Q6J("ngIf",!i.metrics.recentErrors||0===i.metrics.recentErrors.length)}}const S=function(s,o){return{"text-success":s,"text-danger":o}};d.kL.register(...d.zX);let O=(()=>{const o=class{constructor(r,e,a,n,c,w,L,J){this.route=r,this.monitoringService=e,this.chartService=a,this.instanceService=n,this.signalRService=c,this.fb=w,this.breadcrumbService=L,this.chartModalService=J,this.instanceId=0,this.instance={},this.loading=!0,this.error=null,this.metrics={},this.apiCallsChart=null,this.apiResponseTimeChart=null,this.errorRateChart=null,this.resourceUsageChart=null,this.updateSubscription=null,this.signalRSubscriptions=[],this.routeSubscription=null,this.filterForm=this.fb.group({dateRange:["30"]})}ngOnInit(){this.routeSubscription=this.route.params.subscribe(r=>{this.instanceId=+r.id,this.loadInstanceDetails(),this.loadInstanceMetrics()}),this.initSignalR(),this.filterForm.valueChanges.subscribe(()=>{this.refreshData()})}ngOnDestroy(){this.updateSubscription&&this.updateSubscription.unsubscribe(),this.routeSubscription&&this.routeSubscription.unsubscribe(),this.signalRSubscriptions.forEach(r=>r.unsubscribe()),this.signalRService.stopConnection(),this.destroyCharts(),this.breadcrumbService.resetBreadcrumbs()}loadInstanceDetails(){this.instanceService.getInstance(this.instanceId).subscribe({next:r=>{this.instance=r,this.breadcrumbService.setBreadcrumbs([{label:"Monitoring",url:"/monitoring",icon:"graph-up"},{label:`Instance ${r.name}`,url:`/instance-metrics/${this.instanceId}`,icon:"speedometer2"}])},error:r=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed detail\u016f instance",r),this.error="Nepoda\u0159ilo se na\u010d\xedst detaily instance"}})}loadInstanceMetrics(){const r=parseInt(this.filterForm.get("dateRange")?.value||"30");this.monitoringService.getDetailedInstanceMetrics(this.instanceId,r).subscribe({next:e=>{this.metrics=e,this.loading=!1},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed metrik instance",e),this.error="Nepoda\u0159ilo se na\u010d\xedst metriky instance",this.loading=!1}})}refreshData(){this.loadInstanceMetrics(),this.refreshCharts()}initCharts(){this.loading=!0,this.loadApiCallsChart(),this.loadApiResponseTimeChart(),this.loadErrorRateChart(),this.loadResourceUsageChart(),this.loading=!1}refreshCharts(){this.loadApiCallsChart(),this.loadApiResponseTimeChart(),this.loadErrorRateChart(),this.loadResourceUsageChart()}destroyCharts(){this.apiCallsChart&&(this.apiCallsChart.destroy(),this.apiCallsChart=null),this.apiResponseTimeChart&&(this.apiResponseTimeChart.destroy(),this.apiResponseTimeChart=null),this.errorRateChart&&(this.errorRateChart.destroy(),this.errorRateChart=null),this.resourceUsageChart&&(this.resourceUsageChart.destroy(),this.resourceUsageChart=null)}loadApiCallsChart(){const r=parseInt(this.filterForm.get("dateRange")?.value||"30");this.chartService.getApiCallsChartData(this.instanceId,r).subscribe({next:e=>{this.apiCallsChart?(this.apiCallsChart.data.labels=e.labels,this.apiCallsChart.data.datasets[0].data=e.data,this.apiCallsChart.update()):this.apiCallsChartRef&&(this.apiCallsChart=new d.kL(this.apiCallsChartRef.nativeElement,{type:"line",data:{labels:e.labels,datasets:[{label:"Po\u010det API vol\xe1n\xed",data:e.data,backgroundColor:"rgba(54, 162, 235, 0.2)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:2,tension:.3,fill:!0}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:e.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det vol\xe1n\xed"}},x:{title:{display:!0,text:"Datum"}}}}}))},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf API vol\xe1n\xed",e),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf API vol\xe1n\xed"}})}loadApiResponseTimeChart(){const r=parseInt(this.filterForm.get("dateRange")?.value||"30");this.chartService.getApiPerformanceChartData(this.instanceId,r).subscribe({next:e=>{this.apiResponseTimeChart?(this.apiResponseTimeChart.data.labels=e.labels,this.apiResponseTimeChart.data.datasets[0].data=e.avgData,this.apiResponseTimeChart.data.datasets[1].data=e.maxData,this.apiResponseTimeChart.update()):this.apiResponseTimeChartRef&&(this.apiResponseTimeChart=new d.kL(this.apiResponseTimeChartRef.nativeElement,{type:"bar",data:{labels:e.labels,datasets:[{label:"Pr\u016fm\u011brn\xe1 doba odezvy (ms)",data:e.avgData,backgroundColor:"rgba(54, 162, 235, 0.5)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:1},{label:"Maxim\xe1ln\xed doba odezvy (ms)",data:e.maxData,backgroundColor:"rgba(255, 99, 132, 0.5)",borderColor:"rgba(255, 99, 132, 1)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:e.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Doba odezvy (ms)"}},x:{title:{display:!0,text:"Endpoint"}}}}}))},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf doby odezvy API",e),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf doby odezvy API"}})}loadErrorRateChart(){const r=parseInt(this.filterForm.get("dateRange")?.value||"30");this.chartService.getErrorRateChartData(this.instanceId,r).subscribe({next:e=>{this.errorRateChart?(this.errorRateChart.data.labels=e.labels,this.errorRateChart.data.datasets=e.datasets,this.errorRateChart.update()):this.errorRateChartRef&&(this.errorRateChart=new d.kL(this.errorRateChartRef.nativeElement,{type:"line",data:{labels:e.labels,datasets:e.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:e.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det chyb"}},x:{title:{display:!0,text:"Datum"}}}}}))},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf chybovosti",e),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf chybovosti"}})}loadResourceUsageChart(){const r=parseInt(this.filterForm.get("dateRange")?.value||"30");this.chartService.getResourceUsageChartData(this.instanceId,r).subscribe({next:e=>{this.resourceUsageChart?(this.resourceUsageChart.data.labels=e.labels,this.resourceUsageChart.data.datasets=e.datasets,this.resourceUsageChart.update()):this.resourceUsageChartRef&&(this.resourceUsageChart=new d.kL(this.resourceUsageChartRef.nativeElement,{type:"line",data:{labels:e.labels,datasets:e.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:e.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Vyu\u017eit\xed (%)"}},x:{title:{display:!0,text:"Datum a \u010das"}}}}}))},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf vyu\u017eit\xed zdroj\u016f",e),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf vyu\u017eit\xed zdroj\u016f"}})}openFullscreenChart(r,e){if(r instanceof HTMLCanvasElement){const a=d.kL.getChart(r);this.chartModalService.openChartModal(a||null,e)}else this.chartModalService.openChartModal(r,e)}exportData(){const r=parseInt(this.filterForm.get("dateRange")?.value||"30");this.monitoringService.exportMetricsData(this.instanceId,void 0,r,"all").subscribe({next:e=>{const a=new Blob([e],{type:"text/csv"}),n=window.URL.createObjectURL(a),c=document.createElement("a");c.href=n,c.download=`metriky_instance_${this.instanceId}_${(new Date).toISOString().slice(0,10)}.csv`,document.body.appendChild(c),c.click(),window.URL.revokeObjectURL(n),document.body.removeChild(c)},error:e=>{console.error("Chyba p\u0159i exportu dat",e),this.error="Nepoda\u0159ilo se exportovat data"}})}initSignalR(){var r=this;return(0,v.Z)(function*(){try{if(console.log("Initializing SignalR connection..."),yield r.signalRService.startConnection(),!(yield r.signalRService.testConnection()))return void console.warn("SignalR connection test failed. Falling back to interval-based updates.");console.log("SignalR connection test successful. Joining groups..."),yield r.signalRService.joinGroup("api-metrics"),r.signalRSubscriptions.push(r.signalRService.apiMetrics$.subscribe(a=>{Object.keys(a).length>0&&a.instanceId===r.instanceId&&r.updateCharts(a)})),console.log("SignalR initialization completed successfully.")}catch(e){console.error("Error during SignalR initialization:",e),console.warn("Falling back to interval-based updates.")}})()}updateCharts(r){if(this.apiCallsChart&&r.apiCalls){const a=new Date(r.apiCalls.timestamp).toLocaleTimeString(),n=this.apiCallsChart.data.labels,c=this.apiCallsChart.data.datasets[0].data;n.length>0&&n[n.length-1]===a?c[c.length-1]=r.apiCalls.count:(n.push(a),c.push(r.apiCalls.count),n.length>20&&(n.shift(),c.shift())),this.apiCallsChart.update("none")}}ngAfterViewInit(){setTimeout(()=>{this.initCharts(),setTimeout(()=>{this.initPopovers()},300)},500)}initPopovers(){const r={"dis-response":"Graf zobrazuje top 10 nejpomalej\u0161\xedch metod se\u0159azen\xfdch podle 95. percentilu doby odezvy. Pro ka\u017edou metodu jsou zobrazeny dva sloupce: pr\u016fm\u011brn\xe1 doba odezvy (modr\xfd sloupec) a maxim\xe1ln\xed doba odezvy (\u010derven\xfd sloupec). Data jsou z\xedsk\xe1v\xe1na z tabulky PerformanceMetrics za zvolen\xe9 obdob\xed.","avg-response":"Tato statistika zobrazuje pr\u016fm\u011brnou dobu odezvy metod pro tuto instanci. Hodnota je vypo\u010d\xedt\xe1na jako v\xe1\u017een\xfd pr\u016fm\u011br hodnot Avg ze v\u0161ech z\xe1znam\u016f v tabulce PerformanceMetrics za zvolen\xe9 obdob\xed, kde v\xe1hou je po\u010det nenulov\xfdch vol\xe1n\xed (NonZeroCount).","performance-metrics":"Tabulka zobrazuje podrobn\xe9 statistiky o v\xfdkonu jednotliv\xfdch metod v DIS instanci. Pro ka\u017edou metodu jsou zobrazeny \xfadaje jako po\u010det vol\xe1n\xed, minim\xe1ln\xed, maxim\xe1ln\xed a pr\u016fm\u011brn\xe1 doba b\u011bhu, medi\xe1n, 95. percentil a verze DIS instance, ve kter\xe9 byla data po\u0159\xedzena.","api-calls-24h":"Tato statistika zobrazuje celkov\xfd po\u010det API vol\xe1n\xed za posledn\xedch 24 hodin pro tuto instanci. Data jsou z\xedsk\xe1v\xe1na z tabulky DiagnosticLogs.","errors-24h":"Tato statistika zobrazuje celkov\xfd po\u010det chyb za posledn\xedch 24 hodin pro tuto instanci. Zahrnuje v\u0161echny typy chyb, v\u010detn\u011b aplika\u010dn\xedch v\xfdjimek a HTTP chyb. Data jsou z\xedsk\xe1v\xe1na z tabulky ErrorLogs.",availability:"Tato statistika zobrazuje dostupnost instance za posledn\xedch 24 hodin vyj\xe1d\u0159enou v procentech. Dostupnost je vypo\u010d\xedt\xe1na jako pom\u011br \xfasp\u011b\u0161n\xfdch odpov\u011bd\xed k celkov\xe9mu po\u010dtu po\u017eadavk\u016f. Hodnota nad 99% je pova\u017eov\xe1na za v\xfdbornou, 95-99% za dobrou a pod 95% za problematickou.","api-calls-time":"Graf zobrazuje po\u010det API vol\xe1n\xed v \u010dase pro tuto instanci. Data jsou z\xedsk\xe1v\xe1na z tabulky DiagnosticLogs a jsou agregov\xe1na podle \u010dasov\xfdch interval\u016f.","error-rate":"Graf zobrazuje po\u010det chyb v \u010dase pro tuto instanci. Data jsou z\xedsk\xe1v\xe1na z tabulky ErrorLogs a jsou agregov\xe1na podle \u010dasov\xfdch interval\u016f a typu chyby.","resource-usage":"Graf zobrazuje vyu\u017eit\xed syst\xe9mov\xfdch zdroj\u016f (CPU, pam\u011b\u0165, disk) v \u010dase pro tuto instanci. Data jsou z\xedsk\xe1v\xe1na z tabulky ResourceUsageLogs.","recent-api-calls":"Tabulka zobrazuje posledn\xedch n\u011bkolik API vol\xe1n\xed pro tuto instanci. Zahrnuje informace o \u010dasu vol\xe1n\xed, endpointu, HTTP metod\u011b, stavov\xe9m k\xf3du a dob\u011b odezvy.","recent-errors":"Tabulka zobrazuje posledn\xedch n\u011bkolik chyb pro tuto instanci. Zahrnuje informace o \u010dasu v\xfdskytu, typu chyby, zpr\xe1v\u011b a zdroji chyby."};document.querySelectorAll('[data-bs-toggle="popover"]').forEach(e=>{const a=bootstrap.Popover.getInstance(e);a&&a.dispose()}),document.querySelectorAll('[data-bs-toggle="popover"]').forEach(e=>{const a=e.getAttribute("data-help-type");if(a&&a in r)try{new bootstrap.Popover(e,{content:r[a],html:!0,trigger:"hover",placement:"top",container:"body"})}catch(n){console.error("Error initializing popover:",n)}else a&&console.warn("Help content not found for type:",a)})}};let s=o;return o.\u0275fac=function(e){return new(e||o)(t.Y36(g.gz),t.Y36(b.W),t.Y36(C.C),t.Y36(Z._),t.Y36(f.M),t.Y36(u.qu),t.Y36(y.p),t.Y36(A.i))},o.\u0275cmp=t.Xpm({type:o,selectors:[["app-instance-metrics"]],viewQuery:function(e,a){if(1&e&&(t.Gf(T,5),t.Gf(U,5),t.Gf(x,5),t.Gf(q,5)),2&e){let n;t.iGM(n=t.CRH())&&(a.apiCallsChartRef=n.first),t.iGM(n=t.CRH())&&(a.apiResponseTimeChartRef=n.first),t.iGM(n=t.CRH())&&(a.errorRateChartRef=n.first),t.iGM(n=t.CRH())&&(a.resourceUsageChartRef=n.first)}},decls:111,vars:38,consts:[[1,"container-fluid"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],["type","button",1,"btn","btn-primary",3,"click"],[1,"bi","bi-download","me-1"],["class","alert alert-danger alert-dismissible fade show","role","alert",4,"ngIf"],[1,"card","mb-4"],[1,"card-header"],[1,"mb-0"],[1,"card-body"],[1,"row","g-3",3,"formGroup"],[1,"col-md-3"],["for","dateRange",1,"form-label"],["id","dateRange","formControlName","dateRange",1,"form-select"],["value","1"],["value","7"],["value","30"],["value","90"],[1,"row"],[1,"col-md-6"],[3,"ngClass"],[1,"row","mb-4"],[1,"col-md-3","mb-3"],[1,"card","h-100"],[1,"card-body","text-center"],["data-bs-toggle","popover","data-help-type","api-calls-24h",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0"],[1,"text-muted"],["data-bs-toggle","popover","data-help-type","avg-response",1,"card-title",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","errors-24h",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0","text-danger"],["data-bs-toggle","popover","data-help-type","availability",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0",3,"ngClass"],["class","text-center my-5",4,"ngIf"],[4,"ngIf"],["role","alert",1,"alert","alert-danger","alert-dismissible","fade","show"],["type","button","aria-label","Close",1,"btn-close",3,"click"],[1,"text-center","my-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2"],[1,"col-lg-6","mb-4"],[1,"card-header","d-flex","justify-content-between","align-items-center"],["data-bs-toggle","popover","data-help-type","api-calls-time",1,"mb-0",2,"cursor","help"],["title","Zobrazit graf na celou obrazovku",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-arrows-fullscreen"],[1,"chart-container",2,"position","relative","height","300px"],["apiCallsChart",""],["data-bs-toggle","popover","data-help-type","dis-response",1,"mb-0",2,"cursor","help"],["apiResponseTimeChart",""],["data-bs-toggle","popover","data-help-type","error-rate",1,"mb-0",2,"cursor","help"],["errorRateChart",""],["data-bs-toggle","popover","data-help-type","resource-usage",1,"mb-0",2,"cursor","help"],["resourceUsageChart",""],["data-bs-toggle","popover","data-help-type","recent-api-calls",1,"mb-0",2,"cursor","help"],[1,"table-responsive"],[1,"table","table-striped","table-hover"],[4,"ngFor","ngForOf"],["data-bs-toggle","popover","data-help-type","performance-metrics",1,"mb-0",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","recent-errors",1,"mb-0",2,"cursor","help"],["colspan","5",1,"text-center"],["colspan","9",1,"text-center"],["colspan","4",1,"text-center"]],template:function(e,a){1&e&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1"),t._uU(3),t.qZA(),t.TgZ(4,"div")(5,"button",2),t.NdJ("click",function(){return a.exportData()}),t._UZ(6,"i",3),t._uU(7," Exportovat data "),t.qZA()()(),t.YNc(8,z,3,1,"div",4),t.TgZ(9,"div",5)(10,"div",6)(11,"h5",7),t._uU(12,"Filtry"),t.qZA()(),t.TgZ(13,"div",8)(14,"form",9)(15,"div",10)(16,"label",11),t._uU(17,"\u010casov\xe9 obdob\xed"),t.qZA(),t.TgZ(18,"select",12)(19,"option",13),t._uU(20,"Posledn\xedch 24 hodin"),t.qZA(),t.TgZ(21,"option",14),t._uU(22,"Posledn\xedch 7 dn\xed"),t.qZA(),t.TgZ(23,"option",15),t._uU(24,"Posledn\xedch 30 dn\xed"),t.qZA(),t.TgZ(25,"option",16),t._uU(26,"Posledn\xedch 90 dn\xed"),t.qZA()()()()()(),t.TgZ(27,"div",5)(28,"div",6)(29,"h5",7),t._uU(30,"Z\xe1kladn\xed informace"),t.qZA()(),t.TgZ(31,"div",8)(32,"div",17)(33,"div",18)(34,"p")(35,"strong"),t._uU(36,"N\xe1zev instance:"),t.qZA(),t._uU(37),t.qZA(),t.TgZ(38,"p")(39,"strong"),t._uU(40,"Z\xe1kazn\xedk:"),t.qZA(),t._uU(41),t.qZA(),t.TgZ(42,"p")(43,"strong"),t._uU(44,"Verze:"),t.qZA(),t._uU(45),t.qZA(),t.TgZ(46,"p")(47,"strong"),t._uU(48,"Posledn\xed p\u0159ipojen\xed:"),t.qZA(),t._uU(49),t.ALo(50,"localDate"),t.qZA()(),t.TgZ(51,"div",18)(52,"p")(53,"strong"),t._uU(54,"IP adresa:"),t.qZA(),t._uU(55),t.qZA(),t.TgZ(56,"p")(57,"strong"),t._uU(58,"Certifik\xe1t platn\xfd do:"),t.qZA(),t._uU(59),t.ALo(60,"localDate"),t.qZA(),t.TgZ(61,"p")(62,"strong"),t._uU(63,"Stav:"),t.qZA(),t.TgZ(64,"span",19),t._uU(65),t.qZA()(),t.TgZ(66,"p")(67,"strong"),t._uU(68,"API kl\xed\u010d:"),t.qZA(),t._uU(69),t.qZA()()()()(),t.TgZ(70,"div",20)(71,"div",21)(72,"div",22)(73,"div",23)(74,"h5",24),t._uU(75,"API vol\xe1n\xed (24h)"),t.qZA(),t.TgZ(76,"p",25),t._uU(77),t.qZA(),t.TgZ(78,"p",26),t._uU(79,"Celkem za posledn\xedch 24 hodin"),t.qZA()()()(),t.TgZ(80,"div",21)(81,"div",22)(82,"div",23)(83,"h5",27),t._uU(84,"Pr\u016fm\u011brn\xe1 odezva DIS metod"),t.qZA(),t.TgZ(85,"p",25),t._uU(86),t.ALo(87,"number"),t.qZA(),t.TgZ(88,"p",26),t._uU(89,"ms"),t.qZA()()()(),t.TgZ(90,"div",21)(91,"div",22)(92,"div",23)(93,"h5",28),t._uU(94,"Chyby (24h)"),t.qZA(),t.TgZ(95,"p",29),t._uU(96),t.qZA(),t.TgZ(97,"p",26),t._uU(98,"Celkem za posledn\xedch 24 hodin"),t.qZA()()()(),t.TgZ(99,"div",21)(100,"div",22)(101,"div",23)(102,"h5",30),t._uU(103,"Dostupnost"),t.qZA(),t.TgZ(104,"p",31),t._uU(105),t.ALo(106,"number"),t.qZA(),t.TgZ(107,"p",26),t._uU(108,"Za posledn\xedch 24 hodin"),t.qZA()()()()(),t.YNc(109,D,6,0,"div",32),t.YNc(110,P,119,6,"div",33),t.qZA()),2&e&&(t.xp6(3),t.hij("Metriky instance: ",a.instance.name,""),t.xp6(5),t.Q6J("ngIf",a.error),t.xp6(6),t.Q6J("formGroup",a.filterForm),t.xp6(23),t.hij(" ",a.instance.name,""),t.xp6(4),t.hij(" ",a.instance.customerName,""),t.xp6(4),t.hij(" ",a.metrics.currentVersion||"Nezn\xe1m\xe1",""),t.xp6(4),t.hij(" ",t.xi3(50,19,a.instance.lastConnectionDate,"dd.MM.yyyy HH:mm:ss"),""),t.xp6(6),t.hij(" ",a.instance.lastKnownIpAddress||"Nezn\xe1m\xe1",""),t.xp6(4),t.hij(" ",t.xi3(60,22,a.instance.certificateExpirationDate,"dd.MM.yyyy"),""),t.xp6(5),t.Q6J("ngClass",t.WLB(31,S,a.instance.isActive,!a.instance.isActive)),t.xp6(1),t.Oqu(a.instance.isActive?"Aktivn\xed":"Neaktivn\xed"),t.xp6(4),t.hij(" ",a.instance.apiKey,""),t.xp6(8),t.Oqu(a.metrics.apiCallsLast24h||0),t.xp6(9),t.Oqu(t.xi3(87,25,a.metrics.avgApiResponseTime||0,"1.0-2")),t.xp6(10),t.Oqu(a.metrics.errorsLast24h||0),t.xp6(8),t.Q6J("ngClass",t.kEZ(34,m,(a.metrics.availability||0)>99,(a.metrics.availability||0)<=99&&(a.metrics.availability||0)>=95,(a.metrics.availability||0)<95)),t.xp6(1),t.hij(" ",t.xi3(106,28,a.metrics.availability||0,"1.0-2"),"% "),t.xp6(4),t.Q6J("ngIf",a.loading),t.xp6(1),t.Q6J("ngIf",!a.loading))},dependencies:[p.mk,p.sg,p.O5,u._Y,u.YN,u.Kr,u.EJ,u.JJ,u.JL,u.sg,u.u,p.JJ,_.H],styles:[".chart-container[_ngcontent-%COMP%]{position:relative;height:300px;width:100%}.card[_ngcontent-%COMP%]{box-shadow:0 .125rem .25rem #00000013;border-radius:.5rem;border:1px solid rgba(0,0,0,.125)}.card-header[_ngcontent-%COMP%]{background-color:#00000008;border-bottom:1px solid rgba(0,0,0,.125)}.display-4[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:300;line-height:1.2}.text-success[_ngcontent-%COMP%]{color:#28a745!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#86b7fe;box-shadow:0 0 0 .25rem #0d6efd40}.btn-primary[_ngcontent-%COMP%]{background-color:#0d6efd;border-color:#0d6efd}.btn-primary[_ngcontent-%COMP%]:hover{background-color:#0b5ed7;border-color:#0a58ca}@media (max-width: 768px){.display-4[_ngcontent-%COMP%]{font-size:2rem}.card-title[_ngcontent-%COMP%]{font-size:1rem}}"]}),s})();var N=l(4466);let F=(()=>{const o=class{};let s=o;return o.\u0275fac=function(e){return new(e||o)},o.\u0275mod=t.oAB({type:o}),o.\u0275inj=t.cJS({imports:[p.ez,u.u5,u.UX,N.m,g.Bz.forChild([{path:"",component:O}])]}),s})()}}]);