{"ast": null, "code": "export var MatchType = /*#__PURE__*/(() => {\n  (function (MatchType) {\n    MatchType[MatchType[\"ExactMatch\"] = 0] = \"ExactMatch\";\n    MatchType[MatchType[\"Contains\"] = 1] = \"Contains\";\n    MatchType[MatchType[\"StartsWith\"] = 2] = \"StartsWith\";\n    MatchType[MatchType[\"EndsWith\"] = 3] = \"EndsWith\";\n  })(MatchType || (MatchType = {}));\n  return MatchType;\n})();\nexport function getMatchTypeName(matchType) {\n  switch (matchType) {\n    case MatchType.ExactMatch:\n      return 'Přesná shoda';\n    case MatchType.Contains:\n      return 'Obsahuje';\n    case MatchType.StartsWith:\n      return 'Začín<PERSON> na';\n    case MatchType.EndsWith:\n      return 'Kon<PERSON>í na';\n    default:\n      return 'Neznámý';\n  }\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}