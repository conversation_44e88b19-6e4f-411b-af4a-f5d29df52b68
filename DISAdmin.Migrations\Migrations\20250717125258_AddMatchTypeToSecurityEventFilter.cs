﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DISAdmin.Migrations.Migrations
{
    /// <inheritdoc />
    public partial class AddMatchTypeToSecurityEventFilter : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "DescriptionMatchType",
                table: "SecurityEventFilters",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "IpAddressMatchType",
                table: "SecurityEventFilters",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DescriptionMatchType",
                table: "SecurityEventFilters");

            migrationBuilder.DropColumn(
                name: "IpAddressMatchType",
                table: "SecurityEventFilters");
        }
    }
}
