using DISAdmin.Core.Data;
using DISAdmin.Core.Data.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace DISAdmin.Core.Services;

/// <summary>
/// Služba pro správu filtrů bezpečnostních událostí
/// </summary>
public class SecurityEventFilterService
{
    private readonly DISAdminDbContext _context;
    private readonly CachingService _cachingService;
    private readonly ILogger<SecurityEventFilterService> _logger;

    // Konstanty pro klíče keše
    private const string CACHE_KEY_ALL_FILTERS = "securityeventfilters:all";

    public SecurityEventFilterService(
        DISAdminDbContext context,
        CachingService cachingService,
        ILogger<SecurityEventFilterService> logger)
    {
        _context = context;
        _cachingService = cachingService;
        _logger = logger;
    }

    /// <summary>
    /// Získá všechny aktivní filtry
    /// </summary>
    public async Task<List<SecurityEventFilter>> GetAllFiltersAsync()
    {
        return await _cachingService.GetOrCreateAsync(
            CACHE_KEY_ALL_FILTERS,
            async () =>
            {
                return await _context.SecurityEventFilters
                    .OrderByDescending(f => f.CreatedAt)
                    .ToListAsync();
            },
            30, // 30 minut absolutní expirace
            15  // 15 minut sliding expirace
        ) ?? new List<SecurityEventFilter>();
    }

    /// <summary>
    /// Vytvoří nový filtr
    /// </summary>
    public async Task<SecurityEventFilter> CreateFilterAsync(SecurityEventType eventType, string? description, DISAdmin.Core.Data.Entities.MatchType descriptionMatchType, string? ipAddress, DISAdmin.Core.Data.Entities.MatchType ipAddressMatchType, string createdBy)
    {
        var filter = new SecurityEventFilter
        {
            EventType = eventType,
            Description = description,
            DescriptionMatchType = descriptionMatchType,
            IpAddress = ipAddress,
            IpAddressMatchType = ipAddressMatchType,
            CreatedAt = DateTime.UtcNow,
            CreatedBy = createdBy
        };

        _context.SecurityEventFilters.Add(filter);
        await _context.SaveChangesAsync();

        // Invalidace keše
        await InvalidateFiltersCache();

        return filter;
    }

    /// <summary>
    /// Smaže filtr podle ID
    /// </summary>
    public async Task<bool> DeleteFilterAsync(int filterId)
    {
        var filter = await _context.SecurityEventFilters.FindAsync(filterId);
        if (filter == null)
        {
            return false;
        }

        _context.SecurityEventFilters.Remove(filter);
        await _context.SaveChangesAsync();

        // Invalidace keše
        await InvalidateFiltersCache();

        return true;
    }

    /// <summary>
    /// Zkontroluje, zda bezpečnostní událost odpovídá některému z filtrů
    /// </summary>
    public async Task<bool> ShouldFilterEventAsync(SecurityEventType eventType, string? description, string? ipAddress)
    {
        var filters = await GetAllFiltersAsync();

        foreach (var filter in filters)
        {
            // Přesná shoda pro EventType
            if (filter.EventType != eventType)
                continue;

            // Kontrola Description podle typu porovnávání
            if (!MatchesPattern(description, filter.Description, filter.DescriptionMatchType))
                continue;

            // Kontrola IpAddress podle typu porovnávání
            if (!MatchesPattern(ipAddress, filter.IpAddress, filter.IpAddressMatchType))
                continue;

            // Pokud jsme se dostali sem, událost odpovídá filtru
            _logger.LogDebug($"Bezpečnostní událost filtrována: {eventType}, {description}, {ipAddress}");
            return true;
        }

        return false;
    }

    /// <summary>
    /// Smaže existující bezpečnostní události podle vzoru
    /// </summary>
    public async Task<int> DeleteEventsByPatternAsync(SecurityEventType eventType, string? description, MatchType descriptionMatchType, string? ipAddress, MatchType ipAddressMatchType)
    {
        var allEvents = await _context.SecurityEvents
            .Where(e => e.EventType == eventType)
            .ToListAsync();

        // Filtrování v paměti podle typu porovnávání
        var eventsToDelete = allEvents
            .Where(e => MatchesPattern(e.Description, description, descriptionMatchType))
            .Where(e => MatchesPattern(e.IpAddress, ipAddress, ipAddressMatchType))
            .ToList();

        var count = eventsToDelete.Count;

        if (count > 0)
        {
            _context.SecurityEvents.RemoveRange(eventsToDelete);
            await _context.SaveChangesAsync();

            _logger.LogDebug($"Smazáno {count} bezpečnostních událostí podle vzoru: {eventType}, {description} ({descriptionMatchType}), {ipAddress} ({ipAddressMatchType})");
        }

        return count;
    }

    /// <summary>
    /// Invaliduje keš filtrů
    /// </summary>
    private async Task InvalidateFiltersCache()
    {
        await _cachingService.RemoveAsync(CACHE_KEY_ALL_FILTERS);
    }

    /// <summary>
    /// Porovná dva texty podle zadaného typu porovnávání
    /// </summary>
    /// <param name="text">Text k porovnání</param>
    /// <param name="pattern">Vzor pro porovnání</param>
    /// <param name="matchType">Typ porovnávání</param>
    /// <returns>True pokud text odpovídá vzoru</returns>
    private static bool MatchesPattern(string? text, string? pattern, MatchType matchType)
    {
        if (string.IsNullOrEmpty(pattern))
            return true; // Prázdný vzor odpovídá všemu

        if (string.IsNullOrEmpty(text))
            return false; // Prázdný text neodpovídá neprázdnému vzoru

        return matchType switch
        {
            MatchType.ExactMatch => text.Equals(pattern, StringComparison.OrdinalIgnoreCase),
            MatchType.Contains => text.Contains(pattern, StringComparison.OrdinalIgnoreCase),
            MatchType.StartsWith => text.StartsWith(pattern, StringComparison.OrdinalIgnoreCase),
            MatchType.EndsWith => text.EndsWith(pattern, StringComparison.OrdinalIgnoreCase),
            _ => false
        };
    }
}
