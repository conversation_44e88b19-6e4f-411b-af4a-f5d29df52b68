using DISAdmin.Api.Models;
using DISAdmin.Core.Data.Entities;
using DISAdmin.Core.Services;

namespace DISAdmin.Api.Controllers;

[ApiController]
[Route("api/security-event-filters")]
[Auth.Authorize(adminOnly: true)]
public class SecurityEventFilterController : ControllerBase
{
    private readonly SecurityEventFilterService _filterService;
    private readonly ILogger<SecurityEventFilterController> _logger;

    public SecurityEventFilterController(
        SecurityEventFilterService filterService,
        ILogger<SecurityEventFilterController> logger)
    {
        _filterService = filterService;
        _logger = logger;
    }

    /// <summary>
    /// Získá seznam všech filtrů bezpečnostních událostí
    /// </summary>
    [HttpGet]
    public async Task<ActionResult<SecurityEventFilterApiResponse<List<SecurityEventFilterResponse>>>> GetFilters()
    {
        try
        {
            var filters = await _filterService.GetAllFiltersAsync();
            var response = filters.Select(f => new SecurityEventFilterResponse
            {
                Id = f.Id,
                EventType = (int)f.EventType,
                EventTypeName = GetEventTypeName(f.EventType),
                Description = f.Description,
                DescriptionMatchType = (int)f.DescriptionMatchType,
                DescriptionMatchTypeName = GetMatchTypeName(f.DescriptionMatchType),
                IpAddress = f.IpAddress,
                IpAddressMatchType = (int)f.IpAddressMatchType,
                IpAddressMatchTypeName = GetMatchTypeName(f.IpAddressMatchType),
                CreatedAt = f.CreatedAt,
                CreatedBy = f.CreatedBy
            }).ToList();

            return Ok(new SecurityEventFilterApiResponse<List<SecurityEventFilterResponse>>
            {
                Success = true,
                Data = response,
                Message = null
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při načítání filtrů bezpečnostních událostí");
            return StatusCode(500, new SecurityEventFilterApiResponse<List<SecurityEventFilterResponse>>
            {
                Success = false,
                Data = null,
                Message = "Chyba při načítání filtrů bezpečnostních událostí"
            });
        }
    }

    /// <summary>
    /// Vytvoří nový filtr bezpečnostních událostí
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<SecurityEventFilterApiResponse<SecurityEventFilterResponse>>> CreateFilter(
        [FromBody] CreateSecurityEventFilterRequest request)
    {
        try
        {
            var eventType = (SecurityEventType)request.EventType;

            // Získání aktuálního uživatele z kontextu
            var currentUser = (DISAdmin.Core.Data.Entities.User?)HttpContext.Items["User"];
            var username = currentUser != null ? $"{currentUser.FirstName} {currentUser.LastName}".Trim() : "unknown";

            var descriptionMatchType = (MatchType)request.DescriptionMatchType;
            var ipAddressMatchType = (MatchType)request.IpAddressMatchType;

            // Smazání existujících událostí podle vzoru (pokud je požadováno)
            if (request.DeleteExistingEvents)
            {
                var deletedCount = await _filterService.DeleteEventsByPatternAsync(
                    eventType, request.Description, descriptionMatchType, request.IpAddress, ipAddressMatchType);
                _logger.LogDebug($"Smazáno {deletedCount} existujících bezpečnostních událostí podle vzoru");
            }

            // Vytvoření filtru
            var filter = await _filterService.CreateFilterAsync(
                eventType, request.Description, descriptionMatchType, request.IpAddress, ipAddressMatchType, username);

            var response = new SecurityEventFilterResponse
            {
                Id = filter.Id,
                EventType = (int)filter.EventType,
                EventTypeName = GetEventTypeName(filter.EventType),
                Description = filter.Description,
                DescriptionMatchType = (int)filter.DescriptionMatchType,
                DescriptionMatchTypeName = GetMatchTypeName(filter.DescriptionMatchType),
                IpAddress = filter.IpAddress,
                IpAddressMatchType = (int)filter.IpAddressMatchType,
                IpAddressMatchTypeName = GetMatchTypeName(filter.IpAddressMatchType),
                CreatedAt = filter.CreatedAt,
                CreatedBy = filter.CreatedBy
            };

            return Ok(new SecurityEventFilterApiResponse<SecurityEventFilterResponse>
            {
                Success = true,
                Data = response,
                Message = "Filtr byl úspěšně vytvořen"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při vytváření filtru bezpečnostních událostí");
            return StatusCode(500, new SecurityEventFilterApiResponse<SecurityEventFilterResponse>
            {
                Success = false,
                Data = null,
                Message = "Chyba při vytváření filtru bezpečnostních událostí"
            });
        }
    }

    /// <summary>
    /// Smaže filtr bezpečnostních událostí
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<ActionResult<SecurityEventFilterApiResponse<object>>> DeleteFilter(int id)
    {
        try
        {
            var success = await _filterService.DeleteFilterAsync(id);
            if (!success)
            {
                return NotFound(new SecurityEventFilterApiResponse<object>
                {
                    Success = false,
                    Data = null,
                    Message = "Filtr nebyl nalezen"
                });
            }

            return Ok(new SecurityEventFilterApiResponse<object>
            {
                Success = true,
                Data = null,
                Message = "Filtr byl úspěšně smazán"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Chyba při mazání filtru bezpečnostních událostí s ID {id}");
            return StatusCode(500, new SecurityEventFilterApiResponse<object>
            {
                Success = false,
                Data = null,
                Message = "Chyba při mazání filtru bezpečnostních událostí"
            });
        }
    }

    /// <summary>
    /// Smaže existující bezpečnostní události podle vzoru
    /// </summary>
    [HttpDelete("events/by-pattern")]
    public async Task<ActionResult<SecurityEventFilterApiResponse<object>>> DeleteEventsByPattern(
        [FromBody] DeleteEventsByPatternRequest request)
    {
        try
        {
            var eventType = (SecurityEventType)request.EventType;
            var descriptionMatchType = (MatchType)request.DescriptionMatchType;
            var ipAddressMatchType = (MatchType)request.IpAddressMatchType;

            var deletedCount = await _filterService.DeleteEventsByPatternAsync(
                eventType, request.Description, descriptionMatchType, request.IpAddress, ipAddressMatchType);

            return Ok(new SecurityEventFilterApiResponse<object>
            {
                Success = true,
                Data = new { DeletedCount = deletedCount },
                Message = $"Bylo smazáno {deletedCount} bezpečnostních událostí"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Chyba při mazání bezpečnostních událostí podle vzoru");
            return StatusCode(500, new SecurityEventFilterApiResponse<object>
            {
                Success = false,
                Data = null,
                Message = "Chyba při mazání bezpečnostních událostí podle vzoru"
            });
        }
    }

    private static string GetEventTypeName(SecurityEventType eventType)
    {
        return eventType switch
        {
            SecurityEventType.FailedAccessAttempt => "Neúspěšný pokus o přístup",
            SecurityEventType.SuspiciousActivity => "Podezřelá aktivita",
            SecurityEventType.IpBlocked => "Blokovaná IP adresa",
            SecurityEventType.CertificateValidationFailure => "Selhání validace certifikátu",
            SecurityEventType.ApiKeyMisuse => "Nesprávné použití API klíče",
            SecurityEventType.UnauthorizedAccess => "Neautorizovaný přístup",
            SecurityEventType.Other => "Ostatní",
            _ => eventType.ToString()
        };
    }

    private static string GetMatchTypeName(MatchType matchType)
    {
        return matchType switch
        {
            MatchType.ExactMatch => "Přesná shoda",
            MatchType.Contains => "Obsahuje",
            MatchType.StartsWith => "Začíná na",
            MatchType.EndsWith => "Končí na",
            _ => matchType.ToString()
        };
    }
}
