{"ast": null, "code": "export var MatchType;\n(function (MatchType) {\n  MatchType[MatchType[\"ExactMatch\"] = 0] = \"ExactMatch\";\n  MatchType[MatchType[\"Contains\"] = 1] = \"Contains\";\n  MatchType[MatchType[\"StartsWith\"] = 2] = \"StartsWith\";\n  MatchType[MatchType[\"EndsWith\"] = 3] = \"EndsWith\";\n})(MatchType || (MatchType = {}));\nexport function getMatchTypeName(matchType) {\n  switch (matchType) {\n    case MatchType.ExactMatch:\n      return 'Přesná shoda';\n    case MatchType.Contains:\n      return 'Obsahuje';\n    case MatchType.StartsWith:\n      return 'Za<PERSON><PERSON><PERSON> na';\n    case MatchType.EndsWith:\n      return 'Kon<PERSON><PERSON> na';\n    default:\n      return '<PERSON>ez<PERSON>mý';\n  }\n}", "map": {"version": 3, "mappings": "AAAA,WAAYA,SAKX;AALD,WAAYA,SAAS;EACnBA,qDAAc;EACdA,iDAAY;EACZA,qDAAc;EACdA,iDAAY;AACd,CAAC,EALWA,SAAS,KAATA,SAAS;AAwFrB,OAAM,SAAUC,gBAAgB,CAACC,SAAoB;EACnD,QAAQA,SAAS;IACf,KAAKF,SAAS,CAACG,UAAU;MACvB,OAAO,cAAc;IACvB,KAAKH,SAAS,CAACI,QAAQ;MACrB,OAAO,UAAU;IACnB,KAAKJ,SAAS,CAACK,UAAU;MACvB,OAAO,WAAW;IACpB,KAAKL,SAAS,CAACM,QAAQ;MACrB,OAAO,UAAU;IACnB;MACE,OAAO,SAAS;EAAC;AAEvB", "names": ["MatchType", "getMatchTypeName", "matchType", "ExactMatch", "Contains", "StartsWith", "EndsWith"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\models\\security.model.ts"], "sourcesContent": ["export enum MatchType {\n  ExactMatch = 0,\n  Contains = 1,\n  StartsWith = 2,\n  EndsWith = 3\n}\n\nexport interface SecurityDashboardResponse {\n  recentSecurityEvents: SecurityEventResponse[];\n  activeAlerts: AlertResponse[];\n  failedConnectionStats: FailedConnectionStatsResponse[];\n}\n\nexport interface SecurityEventResponse {\n  id: number;\n  timestamp: Date;\n  eventType: string;\n  ipAddress: string;\n  username?: string;\n  description: string;\n  severity: number;\n  isResolved: boolean;\n  isIgnored: boolean;\n  resolvedAt?: Date;\n  resolvedBy?: string;\n  resolution?: string;\n}\n\nexport interface AlertResponse {\n  id: number;\n  timestamp: Date;\n  alertType: string;\n  instanceId?: number;\n  instanceName?: string;\n  customerName?: string;\n  customerAbbreviation?: string;\n  description: string;\n  severity: number;\n  isResolved: boolean;\n  resolvedAt?: Date;\n  resolvedBy?: string;\n  resolution?: string;\n  isIgnored?: boolean;\n}\n\nexport interface FailedConnectionStatsResponse {\n  instanceId: number;\n  instanceName: string;\n  failedCertificateValidationCount: number;\n  lastFailedCertificateValidation?: Date;\n  failedApiKeyValidationCount: number;\n  lastFailedApiKeyValidation?: Date;\n  lastKnownIpAddress?: string;\n}\n\nexport interface ResolveAlertRequest {\n  resolution: string;\n}\n\nexport interface SecurityEventFilterResponse {\n  id: number;\n  eventType: number;\n  eventTypeName: string;\n  description?: string;\n  descriptionMatchType: number;\n  descriptionMatchTypeName: string;\n  ipAddress?: string;\n  ipAddressMatchType: number;\n  ipAddressMatchTypeName: string;\n  createdAt: Date;\n  createdBy: string;\n}\n\nexport interface CreateSecurityEventFilterRequest {\n  eventType: number;\n  description?: string;\n  descriptionMatchType: number;\n  ipAddress?: string;\n  ipAddressMatchType: number;\n  deleteExistingEvents: boolean;\n}\n\nexport interface SecurityEventFilterApiResponse<T> {\n  success: boolean;\n  data?: T;\n  message?: string;\n}\n\nexport function getMatchTypeName(matchType: MatchType): string {\n  switch (matchType) {\n    case MatchType.ExactMatch:\n      return 'Přesná shoda';\n    case MatchType.Contains:\n      return 'Obsahuje';\n    case MatchType.StartsWith:\n      return 'Začíná na';\n    case MatchType.EndsWith:\n      return 'Končí na';\n    default:\n      return 'Neznámý';\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}