{"ast": null, "code": "import { MatchType } from '../models/security.model';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SharedModule } from '../shared/shared.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/security.service\";\nimport * as i2 from \"../services/modal.service\";\nimport * as i3 from \"ngx-toastr\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../shared/components/resolve-alert-modal/resolve-alert-modal.component\";\nfunction SecurityComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction SecurityComponent_div_9_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 ne\\u00FAsp\\u011B\\u0161n\\u00E9 pokusy o p\\u0159ipojen\\u00ED. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_8_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\")(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\")(10, \"span\", 43);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stat_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r12.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", stat_r12.failedCertificateValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r12.failedCertificateValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 8, stat_r12.lastFailedCertificateValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", stat_r12.failedApiKeyValidationCount > 10 ? \"text-danger\" : \"text-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stat_r12.failedApiKeyValidationCount, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 11, stat_r12.lastFailedApiKeyValidation, \"dd.MM.yyyy HH:mm\" || \"-\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r12.lastKnownIpAddress || \"-\");\n  }\n}\nfunction SecurityComponent_div_9_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\")(4, \"th\", 41);\n    i0.ɵɵtext(5, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 41);\n    i0.ɵɵtext(7, \"Ne\\u00FAsp. validace cert.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 41);\n    i0.ɵɵtext(9, \"Posledn\\u00ED ne\\u00FAsp. validace\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 41);\n    i0.ɵɵtext(11, \"Ne\\u00FAsp. validace API kl\\u00ED\\u010De\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 41);\n    i0.ɵɵtext(13, \"Posledn\\u00ED ne\\u00FAsp. validace API\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 41);\n    i0.ɵɵtext(15, \"Posledn\\u00ED IP\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, SecurityComponent_div_9_div_8_tr_17_Template, 17, 14, \"tr\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r8.failedConnectionStats);\n  }\n}\nfunction SecurityComponent_div_9_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_19_tr_19_button_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_19_tr_19_button_20_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const event_r14 = i0.ɵɵnextContext().$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r16.openResolveSecurityEventModal(event_r14));\n    });\n    i0.ɵɵelement(1, \"i\", 49);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_9_div_19_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 41)(2, \"span\", 43);\n    i0.ɵɵelement(3, \"i\", 43);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"td\", 41)(6, \"span\", 43);\n    i0.ɵɵelement(7, \"i\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\", 41);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\", 41);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"div\", 44);\n    i0.ɵɵtemplate(20, SecurityComponent_div_9_div_19_tr_19_button_20_Template, 2, 0, \"button\", 45);\n    i0.ɵɵelementStart(21, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_div_19_tr_19_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r20);\n      const event_r14 = restoredCtx.$implicit;\n      const ctx_r19 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r19.openBlockEventModal(event_r14));\n    });\n    i0.ɵɵelement(22, \"i\", 47);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const event_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getSeverityClass(event_r14.severity));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getSeverityIcon(event_r14.severity) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getSeverityText(event_r14.severity), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getEventTypeClass(event_r14.eventType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r13.getEventTypeIcon(event_r14.eventType) + \" me-1\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.getEventTypeText(event_r14.eventType), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 11, event_r14.timestamp, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r14.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r14.ipAddress);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r14.username || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !event_r14.isResolved && !event_r14.isIgnored);\n  }\n}\nfunction SecurityComponent_div_9_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Z\\u00E1va\\u017Enost\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Typ\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"\\u010Cas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 41);\n    i0.ɵɵtext(13, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"U\\u017Eivatel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_19_tr_19_Template, 23, 14, \"tr\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r10.securityEvents);\n  }\n}\nfunction SecurityComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 30)(2, \"div\", 31)(3, \"h5\", 32);\n    i0.ɵɵelement(4, \"i\", 33);\n    i0.ɵɵtext(5, \"Statistiky ne\\u00FAsp\\u011B\\u0161n\\u00FDch p\\u0159ipojen\\u00ED \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtemplate(7, SecurityComponent_div_9_div_7_Template, 2, 0, \"div\", 24);\n    i0.ɵɵtemplate(8, SecurityComponent_div_9_div_8_Template, 18, 1, \"div\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 30)(10, \"div\", 31)(11, \"h5\", 32);\n    i0.ɵɵelement(12, \"i\", 35);\n    i0.ɵɵtext(13, \"Ned\\u00E1vn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_9_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r21.openManageFiltersModal());\n    });\n    i0.ɵɵelement(15, \"i\", 37);\n    i0.ɵɵtext(16, \"Spravovat filtry \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 34);\n    i0.ɵɵtemplate(18, SecurityComponent_div_9_div_18_Template, 2, 0, \"div\", 24);\n    i0.ɵɵtemplate(19, SecurityComponent_div_9_div_19_Template, 20, 1, \"div\", 25);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.failedConnectionStats.length > 0);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.securityEvents.length > 0);\n  }\n}\nfunction SecurityComponent_div_19_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"label\", 69)(2, \"strong\");\n    i0.ɵɵtext(3, \"IP adresa:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 55)(5, \"input\", 70);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_div_37_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r25 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r25.filterIpAddress = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"select\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_div_37_Template_select_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r27 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r27.ipAddressMatchType = $event);\n    });\n    i0.ɵɵelementStart(7, \"option\", 58);\n    i0.ɵɵtext(8, \"P\\u0159esn\\u00E1 shoda\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"option\", 58);\n    i0.ɵɵtext(10, \"Obsahuje\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"option\", 58);\n    i0.ɵɵtext(12, \"Za\\u010D\\u00EDn\\u00E1 na\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 58);\n    i0.ɵɵtext(14, \"Kon\\u010D\\u00ED na\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"small\", 59);\n    i0.ɵɵtext(16, \"Vyberte, jak m\\u00E1 b\\u00FDt IP adresa porovn\\u00E1v\\u00E1na\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r23 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.filterIpAddress);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r23.ipAddressMatchType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 3);\n  }\n}\nfunction SecurityComponent_div_19_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"small\");\n    i0.ɵɵtext(2, \"Filtr bude aplikov\\u00E1n na v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SecurityComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"Pozor!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Vytvo\\u0159\\u00EDte filtr, kter\\u00FD zablokuje podobn\\u00E9 bezpe\\u010Dnostn\\u00ED ud\\u00E1losti v budoucnu. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\");\n    i0.ɵɵtext(7, \"N\\u00E1hled filtru:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 52)(9, \"div\", 34)(10, \"p\")(11, \"strong\");\n    i0.ɵɵtext(12, \"Typ ud\\u00E1losti:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 53)(15, \"label\", 54)(16, \"strong\");\n    i0.ɵɵtext(17, \"Popis:\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 55)(19, \"input\", 56);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_input_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.selectedEvent.description = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"select\", 57);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_select_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.descriptionMatchType = $event);\n    });\n    i0.ɵɵelementStart(21, \"option\", 58);\n    i0.ɵɵtext(22, \"P\\u0159esn\\u00E1 shoda\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 58);\n    i0.ɵɵtext(24, \"Obsahuje\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 58);\n    i0.ɵɵtext(26, \"Za\\u010D\\u00EDn\\u00E1 na\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"option\", 58);\n    i0.ɵɵtext(28, \"Kon\\u010D\\u00ED na\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"small\", 59);\n    i0.ɵɵtext(30, \"Vyberte, jak m\\u00E1 b\\u00FDt popis porovn\\u00E1v\\u00E1n\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 53)(32, \"div\", 60)(33, \"input\", 61);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_input_ngModelChange_33_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.applyToAllIps = $event);\n    })(\"change\", function SecurityComponent_div_19_Template_input_change_33_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.onApplyToAllIpsChange());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"label\", 62)(35, \"strong\");\n    i0.ɵɵtext(36, \"Pou\\u017E\\u00EDt pro v\\u0161echny IP adresy\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, SecurityComponent_div_19_div_37_Template, 17, 6, \"div\", 63);\n    i0.ɵɵtemplate(38, SecurityComponent_div_19_div_38_Template, 3, 0, \"div\", 64);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(39, \"div\", 65)(40, \"input\", 66);\n    i0.ɵɵlistener(\"ngModelChange\", function SecurityComponent_div_19_Template_input_ngModelChange_40_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r33 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r33.deleteExistingEvents = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"label\", 67);\n    i0.ɵɵtext(42, \" Smazat tak\\u00E9 existuj\\u00EDc\\u00ED ud\\u00E1losti odpov\\u00EDdaj\\u00EDc\\u00ED tomuto vzoru \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getEventTypeText(ctx_r3.selectedEvent.eventType), \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.selectedEvent.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.descriptionMatchType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.applyToAllIps);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.applyToAllIps);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.applyToAllIps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.deleteExistingEvents);\n  }\n}\nfunction SecurityComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"div\", 73)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction SecurityComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1, \" \\u017D\\u00E1dn\\u00E9 filtry nejsou nastaveny. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SecurityComponent_div_36_tr_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"small\", 74);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\")(11, \"small\", 74);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"localDate\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\")(19, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function SecurityComponent_div_36_tr_21_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r37);\n      const filter_r35 = restoredCtx.$implicit;\n      const ctx_r36 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r36.confirmDeleteFilter(filter_r35));\n    });\n    i0.ɵɵelement(20, \"i\", 76);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const filter_r35 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r35.eventTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r35.description || \"-\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r35.descriptionMatchTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(filter_r35.ipAddress || \"V\\u0161echny IP\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r35.ipAddressMatchTypeName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 7, filter_r35.createdAt, \"dd.MM.yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(filter_r35.createdBy);\n  }\n}\nfunction SecurityComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"table\", 40)(2, \"thead\")(3, \"tr\")(4, \"th\", 41);\n    i0.ɵɵtext(5, \"Typ ud\\u00E1losti\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\", 41);\n    i0.ɵɵtext(7, \"Popis\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\", 41);\n    i0.ɵɵtext(9, \"Typ porovn\\u00E1v\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\", 41);\n    i0.ɵɵtext(11, \"IP adresa\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 41);\n    i0.ɵɵtext(13, \"Typ porovn\\u00E1v\\u00E1n\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\", 41);\n    i0.ɵɵtext(15, \"Vytvo\\u0159eno\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\", 41);\n    i0.ɵɵtext(17, \"Vytvo\\u0159il\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"th\", 41);\n    i0.ɵɵtext(19, \"Akce\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"tbody\");\n    i0.ɵɵtemplate(21, SecurityComponent_div_36_tr_21_Template, 21, 10, \"tr\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.securityEventFilters);\n  }\n}\nexport class SecurityComponent {\n  constructor(securityService, modalService, toastr) {\n    this.securityService = securityService;\n    this.modalService = modalService;\n    this.toastr = toastr;\n    this.loading = false;\n    this.error = null;\n    this.securityEvents = [];\n    this.failedConnectionStats = [];\n    this.selectedSecurityEvent = null;\n    // Security Event Filters\n    this.securityEventFilters = [];\n    this.selectedEvent = null;\n    this.deleteExistingEvents = false;\n    this.loadingFilters = false;\n    this.applyToAllIps = true; // Výchozí stav - použít pro všechny IP\n    this.filterIpAddress = '';\n    this.descriptionMatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro popis\n    this.ipAddressMatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro IP adresu\n  }\n\n  ngOnInit() {\n    this.loadSecurityDashboard();\n  }\n  loadSecurityDashboard() {\n    this.loading = true;\n    this.error = null;\n    this.securityService.getSecurityDashboard().subscribe({\n      next: response => {\n        // Převod časů na lokální čas\n        this.securityEvents = response.recentSecurityEvents.map(event => ({\n          ...event,\n          timestamp: new Date(event.timestamp)\n        }));\n        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n          ...stat,\n          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n        }));\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading security dashboard', err);\n        this.error = 'Chyba při načítání bezpečnostního dashboardu';\n        this.loading = false;\n      }\n    });\n  }\n  openResolveSecurityEventModal(securityEvent) {\n    this.selectedSecurityEvent = securityEvent;\n    this.modalService.open('resolveSecurityEventModal');\n  }\n  onResolveSecurityEvent(resolution) {\n    if (!this.selectedSecurityEvent) {\n      return;\n    }\n    this.securityService.resolveSecurityEvent(this.selectedSecurityEvent.id, resolution).subscribe({\n      next: () => {\n        this.toastr.success('Bezpečnostní událost byla úspěšně vyřešena', 'Úspěch');\n        this.loadSecurityDashboard();\n      },\n      error: err => {\n        console.error('Error resolving security event', err);\n        this.error = 'Chyba při řešení bezpečnostní události';\n        this.toastr.error('Chyba při řešení bezpečnostní události', 'Chyba');\n      }\n    });\n  }\n  onIgnoreSecurityEvent() {\n    if (!this.selectedSecurityEvent) {\n      return;\n    }\n    this.securityService.ignoreSecurityEvent(this.selectedSecurityEvent.id).subscribe({\n      next: () => {\n        this.toastr.success('Bezpečnostní událost byla ignorována', 'Úspěch');\n        this.loadSecurityDashboard();\n      },\n      error: err => {\n        console.error('Error ignoring security event', err);\n        this.error = 'Chyba při ignorování bezpečnostní události';\n        this.toastr.error('Chyba při ignorování bezpečnostní události', 'Chyba');\n      }\n    });\n  }\n  getSeverityClass(severity) {\n    switch (severity) {\n      case 5:\n        return 'text-danger fw-bold';\n      case 4:\n        return 'text-danger';\n      case 3:\n        return 'text-warning';\n      case 2:\n        return 'text-info';\n      case 1:\n        return 'text-success';\n      default:\n        return 'text-muted';\n    }\n  }\n  getSeverityIcon(severity) {\n    switch (severity) {\n      case 5:\n      case 4:\n        return 'bi-exclamation-triangle-fill';\n      case 3:\n        return 'bi-exclamation-circle-fill';\n      case 2:\n        return 'bi-info-circle-fill';\n      default:\n        return 'bi-check-circle-fill';\n    }\n  }\n  getSeverityText(severity) {\n    switch (severity) {\n      case 5:\n        return 'Kritická';\n      case 4:\n        return 'Vysoká';\n      case 3:\n        return 'Střední';\n      case 2:\n        return 'Nízká';\n      case 1:\n        return 'Informační';\n      default:\n        return 'Neznámá';\n    }\n  }\n  getEventTypeClass(eventType) {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'CertificateValidationFailure':\n        return 'text-warning';\n      case 'IpBlocked':\n        return 'text-danger';\n      case 'FailedAccessAttempt':\n        return 'text-warning';\n      default:\n        return 'text-info';\n    }\n  }\n  getEventTypeIcon(eventType) {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'CertificateValidationFailure':\n        return 'bi-shield-exclamation';\n      case 'IpBlocked':\n        return 'bi-slash-circle-fill';\n      case 'FailedAccessAttempt':\n        return 'bi-x-circle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'UnauthorizedAccess':\n        return 'bi-shield-x';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n  getEventTypeText(eventType) {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 'Neúspěšný pokus o přístup';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'IpBlocked':\n        return 'Blokovaná IP adresa';\n      case 'CertificateValidationFailure':\n        return 'Selhání validace certifikátu';\n      case 'ApiKeyMisuse':\n        return 'Nesprávné použití API klíče';\n      case 'UnauthorizedAccess':\n        return 'Neautorizovaný přístup';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return eventType;\n    }\n  }\n  // Security Event Filter methods\n  openBlockEventModal(event) {\n    this.selectedEvent = event;\n    this.deleteExistingEvents = false;\n    this.applyToAllIps = true; // Výchozí stav\n    this.filterIpAddress = event.ipAddress || '';\n    const modal = new bootstrap.Modal(document.getElementById('blockEventModal'));\n    modal.show();\n  }\n  onApplyToAllIpsChange() {\n    if (!this.applyToAllIps && this.selectedEvent) {\n      // Pokud se vypne \"pro všechny IP\", nastav IP adresu z události\n      this.filterIpAddress = this.selectedEvent.ipAddress || '';\n    }\n  }\n  confirmBlockEvent() {\n    if (!this.selectedEvent) return;\n    const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);\n    const request = {\n      eventType: eventTypeNumber,\n      description: this.selectedEvent.description,\n      descriptionMatchType: this.descriptionMatchType,\n      ipAddress: this.applyToAllIps ? undefined : this.filterIpAddress,\n      ipAddressMatchType: this.ipAddressMatchType,\n      deleteExistingEvents: this.deleteExistingEvents\n    };\n    this.securityService.createSecurityEventFilter(request).subscribe({\n      next: response => {\n        if (response.success) {\n          this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');\n          this.loadSecurityDashboard(); // Refresh data\n          const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal'));\n          modal?.hide();\n        } else {\n          this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');\n        }\n      },\n      error: error => {\n        console.error('Error creating filter:', error);\n        this.toastr.error('Chyba při vytváření filtru', 'Chyba');\n      }\n    });\n  }\n  openManageFiltersModal() {\n    this.loadSecurityEventFilters();\n    const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal'));\n    modal.show();\n  }\n  loadSecurityEventFilters() {\n    this.loadingFilters = true;\n    this.securityService.getSecurityEventFilters().subscribe({\n      next: response => {\n        if (response.success && response.data) {\n          this.securityEventFilters = response.data.map(filter => ({\n            ...filter,\n            createdAt: new Date(filter.createdAt)\n          }));\n        } else {\n          this.securityEventFilters = [];\n        }\n        this.loadingFilters = false;\n      },\n      error: error => {\n        console.error('Error loading filters:', error);\n        this.toastr.error('Chyba při načítání filtrů', 'Chyba');\n        this.loadingFilters = false;\n      }\n    });\n  }\n  confirmDeleteFilter(filter) {\n    if (confirm(`Opravdu chcete smazat filtr pro \"${filter.eventTypeName}\"?`)) {\n      this.securityService.deleteSecurityEventFilter(filter.id).subscribe({\n        next: response => {\n          if (response.success) {\n            this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');\n            this.loadSecurityEventFilters(); // Refresh filters\n          } else {\n            this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');\n          }\n        },\n        error: error => {\n          console.error('Error deleting filter:', error);\n          this.toastr.error('Chyba při mazání filtru', 'Chyba');\n        }\n      });\n    }\n  }\n  getEventTypeNumber(eventType) {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 0;\n      case 'SuspiciousActivity':\n        return 1;\n      case 'IpBlocked':\n        return 2;\n      case 'CertificateValidationFailure':\n        return 3;\n      case 'ApiKeyMisuse':\n        return 4;\n      case 'UnauthorizedAccess':\n        return 5;\n      case 'Other':\n        return 6;\n      default:\n        return 6;\n      // Other\n    }\n  }\n\n  static {\n    this.ɵfac = function SecurityComponent_Factory(t) {\n      return new (t || SecurityComponent)(i0.ɵɵdirectiveInject(i1.SecurityService), i0.ɵɵdirectiveInject(i2.ModalService), i0.ɵɵdirectiveInject(i3.ToastrService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SecurityComponent,\n      selectors: [[\"app-security\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 40,\n      vars: 8,\n      consts: [[1, \"container\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-4\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"bi\", \"bi-arrow-clockwise\", \"me-2\"], [\"class\", \"d-flex justify-content-center my-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger mb-4\", 4, \"ngIf\"], [4, \"ngIf\"], [\"modalId\", \"resolveSecurityEventModal\", \"entityType\", \"securityEvent\", 3, \"securityEvent\", \"resolve\", \"ignore\", \"close\"], [\"id\", \"blockEventModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"blockEventModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-warning\", \"text-dark\"], [\"id\", \"blockEventModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Zav\\u0159\\u00EDt\", 1, \"btn-close\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-warning\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\", \"me-1\"], [\"id\", \"manageFiltersModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"manageFiltersModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-xl\"], [1, \"modal-header\"], [\"id\", \"manageFiltersModalLabel\", 1, \"modal-title\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"my-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"alert\", \"alert-danger\", \"mb-4\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-0\"], [1, \"bi\", \"bi-shield-exclamation\", \"me-2\"], [1, \"card-body\"], [1, \"bi\", \"bi-journal-text\", \"me-2\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-funnel\", \"me-1\"], [1, \"alert\", \"alert-info\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [1, \"text-nowrap\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", \"class\", \"btn btn-outline-primary btn-sm\", \"title\", \"Vy\\u0159e\\u0161it\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Blokovat ud\\u00E1lost\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-slash-circle\"], [\"type\", \"button\", \"title\", \"Vy\\u0159e\\u0161it\", 1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-check-circle\"], [1, \"alert\", \"alert-warning\"], [1, \"bi\", \"bi-exclamation-triangle\", \"me-1\"], [1, \"card\"], [1, \"mb-3\"], [1, \"form-label\"], [1, \"input-group\"], [\"type\", \"text\", \"readonly\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"form-select\", 2, \"max-width\", \"150px\", 3, \"ngModel\", \"ngModelChange\"], [3, \"value\"], [1, \"form-text\", \"text-muted\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"applyToAllIps\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"for\", \"applyToAllIps\", 1, \"form-check-label\"], [\"class\", \"mt-2\", 4, \"ngIf\"], [\"class\", \"mt-2 text-muted\", 4, \"ngIf\"], [1, \"form-check\", \"mt-3\"], [\"type\", \"checkbox\", \"id\", \"deleteExistingEvents\", 1, \"form-check-input\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"deleteExistingEvents\", 1, \"form-check-label\"], [1, \"mt-2\"], [\"for\", \"filterIpAddress\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"filterIpAddress\", \"placeholder\", \"Zadejte IP adresu\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"mt-2\", \"text-muted\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"spinner-border\"], [1, \"text-muted\"], [\"type\", \"button\", \"title\", \"Smazat filtr\", 1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"bi\", \"bi-trash\"]],\n      template: function SecurityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\");\n          i0.ɵɵtext(3, \"Bezpe\\u010Dnostn\\u00ED ud\\u00E1losti\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_4_listener() {\n            return ctx.loadSecurityDashboard();\n          });\n          i0.ɵɵelement(5, \"i\", 3);\n          i0.ɵɵtext(6, \"Aktualizovat \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, SecurityComponent_div_7_Template, 4, 0, \"div\", 4);\n          i0.ɵɵtemplate(8, SecurityComponent_div_8_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(9, SecurityComponent_div_9_Template, 20, 4, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"app-resolve-alert-modal\", 7);\n          i0.ɵɵlistener(\"resolve\", function SecurityComponent_Template_app_resolve_alert_modal_resolve_10_listener($event) {\n            return ctx.onResolveSecurityEvent($event);\n          })(\"ignore\", function SecurityComponent_Template_app_resolve_alert_modal_ignore_10_listener() {\n            return ctx.onIgnoreSecurityEvent();\n          })(\"close\", function SecurityComponent_Template_app_resolve_alert_modal_close_10_listener() {\n            return ctx.selectedSecurityEvent = null;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"div\", 10)(14, \"div\", 11)(15, \"h5\", 12);\n          i0.ɵɵtext(16, \"Blokovat bezpe\\u010Dnostn\\u00ED ud\\u00E1lost\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"button\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 14);\n          i0.ɵɵtemplate(19, SecurityComponent_div_19_Template, 43, 11, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"button\", 16);\n          i0.ɵɵtext(22, \"Zru\\u0161it\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function SecurityComponent_Template_button_click_23_listener() {\n            return ctx.confirmBlockEvent();\n          });\n          i0.ɵɵelement(24, \"i\", 18);\n          i0.ɵɵtext(25, \"Blokovat ud\\u00E1lost \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(26, \"div\", 19)(27, \"div\", 20)(28, \"div\", 10)(29, \"div\", 21)(30, \"h5\", 22);\n          i0.ɵɵtext(31, \"Spravovat filtry bezpe\\u010Dnostn\\u00EDch ud\\u00E1lost\\u00ED\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"button\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 14);\n          i0.ɵɵtemplate(34, SecurityComponent_div_34_Template, 4, 0, \"div\", 23);\n          i0.ɵɵtemplate(35, SecurityComponent_div_35_Template, 2, 0, \"div\", 24);\n          i0.ɵɵtemplate(36, SecurityComponent_div_36_Template, 22, 1, \"div\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"div\", 15)(38, \"button\", 16);\n          i0.ɵɵtext(39, \"Zav\\u0159\\u00EDt\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"securityEvent\", ctx.selectedSecurityEvent);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEvent);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.loadingFilters);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loadingFilters && ctx.securityEventFilters.length > 0);\n        }\n      },\n      dependencies: [LocalDatePipe, CommonModule, i4.NgClass, i4.NgForOf, i4.NgIf, FormsModule, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.CheckboxControlValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, ReactiveFormsModule, SharedModule, i6.ResolveAlertModalComponent],\n      styles: [\".card[_ngcontent-%COMP%] {\\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\\n  border-radius: 0.5rem;\\n  border: 1px solid rgba(0, 0, 0, 0.125);\\n}\\n\\n.card-header[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.03);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.125);\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  border-top: none;\\n  font-weight: 600;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 0.75rem 1rem;\\n  vertical-align: middle;\\n}\\n\\n.btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.875rem;\\n}\\n\\n.alert[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n  border-radius: 0.25rem;\\n}\\n\\n.text-danger[_ngcontent-%COMP%] {\\n  color: #dc3545 !important;\\n}\\n\\n.text-warning[_ngcontent-%COMP%] {\\n  color: #ffc107 !important;\\n}\\n\\n.text-info[_ngcontent-%COMP%] {\\n  color: #0dcaf0 !important;\\n}\\n\\n.text-success[_ngcontent-%COMP%] {\\n  color: #198754 !important;\\n}\\n\\n\\n@media (prefers-color-scheme: dark) {\\n  .card[_ngcontent-%COMP%] {\\n    background-color: #2b3035;\\n    border-color: #373b3e;\\n  }\\n  \\n  .table[_ngcontent-%COMP%] {\\n    color: #e9ecef;\\n  }\\n  \\n  .table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-bottom-color: #373b3e;\\n  }\\n  \\n  .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    border-top-color: #373b3e;\\n  }\\n  \\n  .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(255, 255, 255, 0.075);\\n  }\\n  \\n  .alert-info[_ngcontent-%COMP%] {\\n    background-color: #0d3b66;\\n    border-color: #0d3b66;\\n    color: #e9ecef;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": "AAEA,SAKEA,SAAS,QACJ,0BAA0B;AAIjC,OAAO,KAAKC,SAAS,MAAM,WAAW;AACtC,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,YAAY,QAAQ,yBAAyB;;;;;;;;;;ICRpDC,+BAAgE;IAE9BA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAAmD;IACjDA,YACF;IAAAA,iBAAM;;;;IADJA,eACF;IADEA,6CACF;;;;;IAWMA,+BAAyE;IACvEA,kGACF;IAAAA,iBAAM;;;;;IAcAA,0BAA+C;IACzCA,YAAuB;IAAAA,iBAAK;IAChCA,0BAAI;IAEAA,YACF;IAAAA,iBAAO;IAETA,0BAAI;IAAAA,YAAgF;;IAAAA,iBAAK;IACzFA,0BAAI;IAEAA,aACF;IAAAA,iBAAO;IAETA,2BAAI;IAAAA,aAA2E;;IAAAA,iBAAK;IACpFA,2BAAI;IAAAA,aAAoC;IAAAA,iBAAK;;;;IAbzCA,eAAuB;IAAvBA,2CAAuB;IAEnBA,eAAuF;IAAvFA,yGAAuF;IAC3FA,eACF;IADEA,0EACF;IAEEA,eAAgF;IAAhFA,+GAAgF;IAE5EA,eAAkF;IAAlFA,oGAAkF;IACtFA,eACF;IADEA,qEACF;IAEEA,eAA2E;IAA3EA,4GAA2E;IAC3EA,eAAoC;IAApCA,wDAAoC;;;;;IA3BhDA,+BAAuE;IAIvCA,wBAAQ;IAAAA,iBAAK;IACrCA,8BAAwB;IAAAA,0CAAqB;IAAAA,iBAAK;IAClDA,8BAAwB;IAAAA,kDAAwB;IAAAA,iBAAK;IACrDA,+BAAwB;IAAAA,yDAAyB;IAAAA,iBAAK;IACtDA,+BAAwB;IAAAA,uDAA4B;IAAAA,iBAAK;IACzDA,+BAAwB;IAAAA,iCAAW;IAAAA,iBAAK;IAG5CA,8BAAO;IACLA,iFAeK;IACPA,iBAAQ;;;;IAhBeA,gBAAwB;IAAxBA,sDAAwB;;;;;IAiCnDA,+BAAkE;IAChEA,4EACF;IAAAA,iBAAM;;;;;;IAiCMA,kCAEwB;IADhBA;MAAAA;MAAA;MAAA;MAAA,OAASA,+DAAoC;IAAA,EAAC;IAEpDA,wBAAkC;IACpCA,iBAAS;;;;;;IAtBfA,0BAAyC;IAGnCA,wBAA6D;IAC7DA,YACF;IAAAA,iBAAO;IAETA,8BAAwB;IAEpBA,wBAA+D;IAAAA,YACjE;IAAAA,iBAAO;IAETA,8BAAwB;IAAAA,aAAoD;;IAAAA,iBAAK;IACjFA,2BAAI;IAAAA,aAAuB;IAAAA,iBAAK;IAChCA,2BAAI;IAAAA,aAAqB;IAAAA,iBAAK;IAC9BA,+BAAwB;IAAAA,aAA2B;IAAAA,iBAAK;IACxDA,2BAAI;IAEAA,8FAIS;IACTA,mCAEiC;IADzBA;MAAA;MAAA;MAAA;MAAA,OAASA,qDAA0B;IAAA,EAAC;IAE1CA,yBAAkC;IACpCA,iBAAS;;;;;IAzBLA,eAA4C;IAA5CA,sEAA4C;IAC7CA,eAAqD;IAArDA,+EAAqD;IACxDA,eACF;IADEA,4EACF;IAGMA,eAA8C;IAA9CA,wEAA8C;IAC/CA,eAAuD;IAAvDA,iFAAuD;IAAKA,eACjE;IADiEA,6EACjE;IAEsBA,eAAoD;IAApDA,qFAAoD;IACxEA,eAAuB;IAAvBA,2CAAuB;IACvBA,eAAqB;IAArBA,yCAAqB;IACDA,eAA2B;IAA3BA,+CAA2B;IAGtCA,eAA2C;IAA3CA,oEAA2C;;;;;IAhChEA,+BAAgE;IAIpDA,mCAAS;IAAAA,iBAAK;IAClBA,0BAAI;IAAAA,mBAAG;IAAAA,iBAAK;IACZA,0BAAI;IAAAA,wBAAG;IAAAA,iBAAK;IACZA,2BAAI;IAAAA,sBAAK;IAAAA,iBAAK;IACdA,+BAAwB;IAAAA,0BAAS;IAAAA,iBAAK;IACtCA,2BAAI;IAAAA,8BAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,qBAAI;IAAAA,iBAAK;IAGjBA,8BAAO;IACLA,kFA8BK;IACPA,iBAAQ;;;;IA/BgBA,gBAAiB;IAAjBA,gDAAiB;;;;;;IA3EnDA,2BAAgC;IAKxBA,wBAA6C;IAAAA,+EAC/C;IAAAA,iBAAK;IAEPA,+BAAuB;IACrBA,yEAEM;IACNA,0EA+BM;IACRA,iBAAM;IAIRA,+BAAuB;IAGjBA,yBAAuC;IAAAA,wEACzC;IAAAA,iBAAK;IACLA,mCAAgG;IAAnCA;MAAAA;MAAA;MAAA,OAASA,+CAAwB;IAAA,EAAC;IAC7FA,yBAAiC;IAAAA,kCACnC;IAAAA,iBAAS;IAEXA,gCAAuB;IACrBA,2EAEM;IACNA,4EA+CM;IACRA,iBAAM;;;;IApGEA,eAAwC;IAAxCA,gEAAwC;IAGxCA,eAAsC;IAAtCA,8DAAsC;IA8CtCA,gBAAiC;IAAjCA,yDAAiC;IAGjCA,eAA+B;IAA/BA,uDAA+B;;;;;;IAwG7BA,+BAAyC;IACiBA,0BAAU;IAAAA,iBAAS;IAC3EA,+BAAyB;IACsCA;MAAAA;MAAA;MAAA;IAAA,EAA6B;IAA1FA,iBAA2H;IAC3HA,kCAAuF;IAAjCA;MAAAA;MAAA;MAAA;IAAA,EAAgC;IACpFA,kCAAoB;IAAAA,sCAAY;IAAAA,iBAAS;IACzCA,kCAAoB;IAAAA,yBAAQ;IAAAA,iBAAS;IACrCA,mCAAoB;IAAAA,yCAAS;IAAAA,iBAAS;IACtCA,mCAAoB;IAAAA,mCAAQ;IAAAA,iBAAS;IAGzCA,kCAAoC;IAAAA,8EAAyC;IAAAA,iBAAQ;;;;IARtBA,eAA6B;IAA7BA,iDAA6B;IACpCA,eAAgC;IAAhCA,oDAAgC;IAC5EA,eAAW;IAAXA,yBAAW;IACXA,eAAW;IAAXA,yBAAW;IACXA,eAAW;IAAXA,yBAAW;IACXA,eAAW;IAAXA,yBAAW;;;;;IAKzBA,+BAAmD;IAC1CA,mEAAyC;IAAAA,iBAAQ;;;;;;IA9ClEA,2BAA2B;IAEvBA,wBAA+C;IAC/CA,8BAAQ;IAAAA,sBAAM;IAAAA,iBAAS;IAACA,+HAC1B;IAAAA,iBAAM;IAENA,0BAAI;IAAAA,mCAAc;IAAAA,iBAAK;IACvBA,+BAAkB;IAEHA,mCAAa;IAAAA,iBAAS;IAACA,aAA+C;IAAAA,iBAAI;IAErFA,gCAAkB;IACkBA,uBAAM;IAAAA,iBAAS;IACjDA,gCAAyB;IACiBA;MAAAA;MAAA;MAAA,OAAaA,0DAAiC;IAAA,EAAP;IAA/EA,iBAAyF;IACzFA,mCAAyF;IAAnCA;MAAAA;MAAA;MAAA;IAAA,EAAkC;IACtFA,mCAAoB;IAAAA,uCAAY;IAAAA,iBAAS;IACzCA,mCAAoB;IAAAA,yBAAQ;IAAAA,iBAAS;IACrCA,mCAAoB;IAAAA,yCAAS;IAAAA,iBAAS;IACtCA,mCAAoB;IAAAA,mCAAQ;IAAAA,iBAAS;IAGzCA,kCAAoC;IAAAA,yEAAoC;IAAAA,iBAAQ;IAGlFA,gCAAkB;IAEqDA;MAAAA;MAAA;MAAA;IAAA,EAA2B;MAAAA;MAAA;MAAA,OAAWA,8CAAuB;IAAA,EAAlC;IAA9FA,iBAAkI;IAClIA,kCAAoD;IAC1CA,4DAA4B;IAAAA,iBAAS;IAGjDA,6EAYM;IACNA,4EAEM;IACRA,iBAAM;IAIVA,gCAA6B;IAC+CA;MAAAA;MAAA;MAAA;IAAA,EAAkC;IAA5GA,iBAA6G;IAC7GA,kCAA2D;IACzDA,+GACF;IAAAA,iBAAQ;;;;IA/C4BA,gBAA+C;IAA/CA,uFAA+C;IAKrCA,eAAuC;IAAvCA,0DAAuC;IACzBA,eAAkC;IAAlCA,qDAAkC;IAC9EA,eAAW;IAAXA,yBAAW;IACXA,eAAW;IAAXA,yBAAW;IACXA,eAAW;IAAXA,yBAAW;IACXA,eAAW;IAAXA,yBAAW;IAQ8CA,eAA2B;IAA3BA,8CAA2B;IAK1FA,eAAoB;IAApBA,4CAAoB;IAapBA,eAAmB;IAAnBA,2CAAmB;IAQ6CA,eAAkC;IAAlCA,qDAAkC;;;;;IA0BhHA,+BAAgD;IAEdA,+CAAW;IAAAA,iBAAO;;;;;IAIpDA,+BAA2F;IACzFA,+DACF;IAAAA,iBAAM;;;;;;IAiBAA,0BAAgD;IAC1CA,YAA0B;IAAAA,iBAAK;IACnCA,0BAAI;IAAAA,YAA+B;IAAAA,iBAAK;IACxCA,0BAAI;IAA0BA,YAAqC;IAAAA,iBAAQ;IAC3EA,0BAAI;IAAAA,YAAsC;IAAAA,iBAAK;IAC/CA,2BAAI;IAA0BA,aAAmC;IAAAA,iBAAQ;IACzEA,2BAAI;IAAAA,aAAqD;;IAAAA,iBAAK;IAC9DA,2BAAI;IAAAA,aAAsB;IAAAA,iBAAK;IAC/BA,2BAAI;IAEMA;MAAA;MAAA;MAAA;MAAA,OAASA,sDAA2B;IAAA,EAAC;IAE3CA,yBAA2B;IAC7BA,iBAAS;;;;IAZPA,eAA0B;IAA1BA,8CAA0B;IAC1BA,eAA+B;IAA/BA,mDAA+B;IACLA,eAAqC;IAArCA,yDAAqC;IAC/DA,eAAsC;IAAtCA,+DAAsC;IACZA,eAAmC;IAAnCA,uDAAmC;IAC7DA,eAAqD;IAArDA,qFAAqD;IACrDA,eAAsB;IAAtBA,0CAAsB;;;;;IAtBlCA,+BAAyF;IAIzDA,iCAAY;IAAAA,iBAAK;IACzCA,8BAAwB;IAAAA,qBAAK;IAAAA,iBAAK;IAClCA,8BAAwB;IAAAA,8CAAe;IAAAA,iBAAK;IAC5CA,+BAAwB;IAAAA,0BAAS;IAAAA,iBAAK;IACtCA,+BAAwB;IAAAA,+CAAe;IAAAA,iBAAK;IAC5CA,+BAAwB;IAAAA,+BAAS;IAAAA,iBAAK;IACtCA,+BAAwB;IAAAA,8BAAQ;IAAAA,iBAAK;IACrCA,+BAAwB;IAAAA,qBAAI;IAAAA,iBAAK;IAGrCA,8BAAO;IACLA,4EAeK;IACPA,iBAAQ;;;;IAhBiBA,gBAAuB;IAAvBA,qDAAuB;;;ADtO5D,OAAM,MAAOC,iBAAiB;EAmB5BC,YACUC,eAAgC,EAChCC,YAA0B,EAC1BC,MAAqB;IAFrB,oBAAe,GAAfF,eAAe;IACf,iBAAY,GAAZC,YAAY;IACZ,WAAM,GAANC,MAAM;IArBhB,YAAO,GAAY,KAAK;IACxB,UAAK,GAAkB,IAAI;IAE3B,mBAAc,GAA4B,EAAE;IAC5C,0BAAqB,GAAoC,EAAE;IAE3D,0BAAqB,GAAiC,IAAI;IAE1D;IACA,yBAAoB,GAAkC,EAAE;IACxD,kBAAa,GAAiC,IAAI;IAClD,yBAAoB,GAAY,KAAK;IACrC,mBAAc,GAAY,KAAK;IAC/B,kBAAa,GAAY,IAAI,CAAC,CAAC;IAC/B,oBAAe,GAAW,EAAE;IAC5B,yBAAoB,GAAcZ,SAAS,CAACa,UAAU,CAAC,CAAC;IACxD,uBAAkB,GAAcb,SAAS,CAACa,UAAU,CAAC,CAAC;EAOtD;;EAEAC,QAAQ;IACN,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAA,qBAAqB;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACP,eAAe,CAACQ,oBAAoB,EAAE,CAACC,SAAS,CAAC;MACpDC,IAAI,EAAGC,QAAQ,IAAI;QACjB;QACA,IAAI,CAACC,cAAc,GAAGD,QAAQ,CAACE,oBAAoB,CAACC,GAAG,CAACC,KAAK,KAAK;UAChE,GAAGA,KAAK;UACRC,SAAS,EAAE,IAAIC,IAAI,CAACF,KAAK,CAACC,SAAS;SACpC,CAAC,CAAC;QAEH,IAAI,CAACE,qBAAqB,GAAGP,QAAQ,CAACO,qBAAqB,CAACJ,GAAG,CAACK,IAAI,KAAK;UACvE,GAAGA,IAAI;UACPC,+BAA+B,EAAED,IAAI,CAACC,+BAA+B,GAAG,IAAIH,IAAI,CAACE,IAAI,CAACC,+BAA+B,CAAC,GAAGC,SAAS;UAClIC,0BAA0B,EAAEH,IAAI,CAACG,0BAA0B,GAAG,IAAIL,IAAI,CAACE,IAAI,CAACG,0BAA0B,CAAC,GAAGD;SAC3G,CAAC,CAAC;QAEH,IAAI,CAACf,OAAO,GAAG,KAAK;MACtB,CAAC;MACDC,KAAK,EAAGgB,GAAG,IAAI;QACbC,OAAO,CAACjB,KAAK,CAAC,kCAAkC,EAAEgB,GAAG,CAAC;QACtD,IAAI,CAAChB,KAAK,GAAG,8CAA8C;QAC3D,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAmB,6BAA6B,CAACC,aAAoC;IAChE,IAAI,CAACC,qBAAqB,GAAGD,aAAa;IAC1C,IAAI,CAACzB,YAAY,CAAC2B,IAAI,CAAC,2BAA2B,CAAC;EACrD;EAEAC,sBAAsB,CAACC,UAAkB;IACvC,IAAI,CAAC,IAAI,CAACH,qBAAqB,EAAE;MAC/B;;IAGF,IAAI,CAAC3B,eAAe,CAAC+B,oBAAoB,CAAC,IAAI,CAACJ,qBAAqB,CAACK,EAAE,EAAEF,UAAU,CAAC,CAACrB,SAAS,CAAC;MAC7FC,IAAI,EAAE,MAAK;QACT,IAAI,CAACR,MAAM,CAAC+B,OAAO,CAAC,4CAA4C,EAAE,QAAQ,CAAC;QAC3E,IAAI,CAAC5B,qBAAqB,EAAE;MAC9B,CAAC;MACDE,KAAK,EAAGgB,GAAG,IAAI;QACbC,OAAO,CAACjB,KAAK,CAAC,gCAAgC,EAAEgB,GAAG,CAAC;QACpD,IAAI,CAAChB,KAAK,GAAG,wCAAwC;QACrD,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,wCAAwC,EAAE,OAAO,CAAC;MACtE;KACD,CAAC;EACJ;EAEA2B,qBAAqB;IACnB,IAAI,CAAC,IAAI,CAACP,qBAAqB,EAAE;MAC/B;;IAGF,IAAI,CAAC3B,eAAe,CAACmC,mBAAmB,CAAC,IAAI,CAACR,qBAAqB,CAACK,EAAE,CAAC,CAACvB,SAAS,CAAC;MAChFC,IAAI,EAAE,MAAK;QACT,IAAI,CAACR,MAAM,CAAC+B,OAAO,CAAC,sCAAsC,EAAE,QAAQ,CAAC;QACrE,IAAI,CAAC5B,qBAAqB,EAAE;MAC9B,CAAC;MACDE,KAAK,EAAGgB,GAAG,IAAI;QACbC,OAAO,CAACjB,KAAK,CAAC,+BAA+B,EAAEgB,GAAG,CAAC;QACnD,IAAI,CAAChB,KAAK,GAAG,4CAA4C;QACzD,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,4CAA4C,EAAE,OAAO,CAAC;MAC1E;KACD,CAAC;EACJ;EAEA6B,gBAAgB,CAACC,QAAgB;IAC/B,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B,KAAK,CAAC;QACJ,OAAO,aAAa;MACtB,KAAK,CAAC;QACJ,OAAO,cAAc;MACvB,KAAK,CAAC;QACJ,OAAO,WAAW;MACpB,KAAK,CAAC;QACJ,OAAO,cAAc;MACvB;QACE,OAAO,YAAY;IAAC;EAE1B;EAEAC,eAAe,CAACD,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,CAAC;MACN,KAAK,CAAC;QACJ,OAAO,8BAA8B;MACvC,KAAK,CAAC;QACJ,OAAO,4BAA4B;MACrC,KAAK,CAAC;QACJ,OAAO,qBAAqB;MAC9B;QACE,OAAO,sBAAsB;IAAC;EAEpC;EAEAE,eAAe,CAACF,QAAgB;IAC9B,QAAQA,QAAQ;MACd,KAAK,CAAC;QACJ,OAAO,UAAU;MACnB,KAAK,CAAC;QACJ,OAAO,QAAQ;MACjB,KAAK,CAAC;QACJ,OAAO,SAAS;MAClB,KAAK,CAAC;QACJ,OAAO,OAAO;MAChB,KAAK,CAAC;QACJ,OAAO,YAAY;MACrB;QACE,OAAO,SAAS;IAAC;EAEvB;EAEAG,iBAAiB,CAACC,SAAiB;IACjC,QAAQA,SAAS;MACf,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,8BAA8B;QACjC,OAAO,cAAc;MACvB,KAAK,WAAW;QACd,OAAO,aAAa;MACtB,KAAK,qBAAqB;QACxB,OAAO,cAAc;MACvB;QACE,OAAO,WAAW;IAAC;EAEzB;EAEAC,gBAAgB,CAACD,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,oBAAoB;QACvB,OAAO,8BAA8B;MACvC,KAAK,8BAA8B;QACjC,OAAO,uBAAuB;MAChC,KAAK,WAAW;QACd,OAAO,sBAAsB;MAC/B,KAAK,qBAAqB;QACxB,OAAO,kBAAkB;MAC3B,KAAK,cAAc;QACjB,OAAO,aAAa;MACtB,KAAK,oBAAoB;QACvB,OAAO,aAAa;MACtB,KAAK,OAAO;QACV,OAAO,yBAAyB;MAClC;QACE,OAAO,qBAAqB;IAAC;EAEnC;EAEAE,gBAAgB,CAACF,SAAiB;IAChC,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,2BAA2B;MACpC,KAAK,oBAAoB;QACvB,OAAO,oBAAoB;MAC7B,KAAK,WAAW;QACd,OAAO,qBAAqB;MAC9B,KAAK,8BAA8B;QACjC,OAAO,8BAA8B;MACvC,KAAK,cAAc;QACjB,OAAO,6BAA6B;MACtC,KAAK,oBAAoB;QACvB,OAAO,wBAAwB;MACjC,KAAK,OAAO;QACV,OAAO,SAAS;MAClB;QACE,OAAOA,SAAS;IAAC;EAEvB;EAEA;EACAG,mBAAmB,CAAC7B,KAA4B;IAC9C,IAAI,CAAC8B,aAAa,GAAG9B,KAAK;IAC1B,IAAI,CAAC+B,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,aAAa,GAAG,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACC,eAAe,GAAGjC,KAAK,CAACkC,SAAS,IAAI,EAAE;IAC5C,MAAMC,KAAK,GAAG,IAAI3D,SAAS,CAAC4D,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAE,CAAC;IAC9EH,KAAK,CAACI,IAAI,EAAE;EACd;EAEAC,qBAAqB;IACnB,IAAI,CAAC,IAAI,CAACR,aAAa,IAAI,IAAI,CAACF,aAAa,EAAE;MAC7C;MACA,IAAI,CAACG,eAAe,GAAG,IAAI,CAACH,aAAa,CAACI,SAAS,IAAI,EAAE;;EAE7D;EAEAO,iBAAiB;IACf,IAAI,CAAC,IAAI,CAACX,aAAa,EAAE;IAEzB,MAAMY,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACb,aAAa,CAACJ,SAAS,CAAC;IAE7E,MAAMkB,OAAO,GAAqC;MAChDlB,SAAS,EAAEgB,eAAe;MAC1BG,WAAW,EAAE,IAAI,CAACf,aAAa,CAACe,WAAW;MAC3CC,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/CZ,SAAS,EAAE,IAAI,CAACF,aAAa,GAAG1B,SAAS,GAAG,IAAI,CAAC2B,eAAe;MAChEc,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3ChB,oBAAoB,EAAE,IAAI,CAACA;KAC5B;IAED,IAAI,CAAC9C,eAAe,CAAC+D,yBAAyB,CAACJ,OAAO,CAAC,CAAClD,SAAS,CAAC;MAChEC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACsB,OAAO,EAAE;UACpB,IAAI,CAAC/B,MAAM,CAAC+B,OAAO,CAAC,4BAA4B,EAAE,QAAQ,CAAC;UAC3D,IAAI,CAAC5B,qBAAqB,EAAE,CAAC,CAAC;UAC9B,MAAM6C,KAAK,GAAG3D,SAAS,CAAC4D,KAAK,CAACa,WAAW,CAACZ,QAAQ,CAACC,cAAc,CAAC,iBAAiB,CAAE,CAAC;UACtFH,KAAK,EAAEe,IAAI,EAAE;SACd,MAAM;UACL,IAAI,CAAC/D,MAAM,CAACK,KAAK,CAACI,QAAQ,CAACuD,OAAO,IAAI,4BAA4B,EAAE,OAAO,CAAC;;MAEhF,CAAC;MACD3D,KAAK,EAAGA,KAAK,IAAI;QACfiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,4BAA4B,EAAE,OAAO,CAAC;MAC1D;KACD,CAAC;EACJ;EAEA4D,sBAAsB;IACpB,IAAI,CAACC,wBAAwB,EAAE;IAC/B,MAAMlB,KAAK,GAAG,IAAI3D,SAAS,CAAC4D,KAAK,CAACC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAE,CAAC;IACjFH,KAAK,CAACI,IAAI,EAAE;EACd;EAEAc,wBAAwB;IACtB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACrE,eAAe,CAACsE,uBAAuB,EAAE,CAAC7D,SAAS,CAAC;MACvDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACsB,OAAO,IAAItB,QAAQ,CAAC4D,IAAI,EAAE;UACrC,IAAI,CAACC,oBAAoB,GAAG7D,QAAQ,CAAC4D,IAAI,CAACzD,GAAG,CAAC2D,MAAM,KAAK;YACvD,GAAGA,MAAM;YACTC,SAAS,EAAE,IAAIzD,IAAI,CAACwD,MAAM,CAACC,SAAS;WACrC,CAAC,CAAC;SACJ,MAAM;UACL,IAAI,CAACF,oBAAoB,GAAG,EAAE;;QAEhC,IAAI,CAACH,cAAc,GAAG,KAAK;MAC7B,CAAC;MACD9D,KAAK,EAAGA,KAAK,IAAI;QACfiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,2BAA2B,EAAE,OAAO,CAAC;QACvD,IAAI,CAAC8D,cAAc,GAAG,KAAK;MAC7B;KACD,CAAC;EACJ;EAEAM,mBAAmB,CAACF,MAAmC;IACrD,IAAIG,OAAO,CAAC,oCAAoCH,MAAM,CAACI,aAAa,IAAI,CAAC,EAAE;MACzE,IAAI,CAAC7E,eAAe,CAAC8E,yBAAyB,CAACL,MAAM,CAACzC,EAAE,CAAC,CAACvB,SAAS,CAAC;QAClEC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACsB,OAAO,EAAE;YACpB,IAAI,CAAC/B,MAAM,CAAC+B,OAAO,CAAC,0BAA0B,EAAE,QAAQ,CAAC;YACzD,IAAI,CAACmC,wBAAwB,EAAE,CAAC,CAAC;WAClC,MAAM;YACL,IAAI,CAAClE,MAAM,CAACK,KAAK,CAACI,QAAQ,CAACuD,OAAO,IAAI,yBAAyB,EAAE,OAAO,CAAC;;QAE7E,CAAC;QACD3D,KAAK,EAAGA,KAAK,IAAI;UACfiB,OAAO,CAACjB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,yBAAyB,EAAE,OAAO,CAAC;QACvD;OACD,CAAC;;EAEN;EAEQmD,kBAAkB,CAACjB,SAAiB;IAC1C,QAAQA,SAAS;MACf,KAAK,qBAAqB;QACxB,OAAO,CAAC;MACV,KAAK,oBAAoB;QACvB,OAAO,CAAC;MACV,KAAK,WAAW;QACd,OAAO,CAAC;MACV,KAAK,8BAA8B;QACjC,OAAO,CAAC;MACV,KAAK,cAAc;QACjB,OAAO,CAAC;MACV,KAAK,oBAAoB;QACvB,OAAO,CAAC;MACV,KAAK,OAAO;QACV,OAAO,CAAC;MACV;QACE,OAAO,CAAC;MAAE;IAAA;EAEhB;;;;uBA/TW3C,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAiF;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UCzB9BxF,8BAAuB;UAEfA,oDAAqB;UAAAA,iBAAK;UAC9BA,iCAAkE;UAAlCA;YAAA,OAASyF,2BAAuB;UAAA,EAAC;UAC/DzF,uBAA0C;UAAAA,6BAC5C;UAAAA,iBAAS;UAGXA,kEAIM;UAENA,kEAEM;UAENA,mEA+GM;UACRA,iBAAM;UAGNA,mDAMyC;UAFvCA;YAAA,OAAWyF,kCAA8B;UAAA,EAAC;YAAA,OAChCA,2BAAuB;UAAA,EADS;YAAA,mCAET,IAAI;UAAA,EAFK;UAG5CzF,iBAA0B;UAG1BA,+BAAqH;UAI3DA,6DAA6B;UAAAA,iBAAK;UACpFA,8BAA6F;UAC/FA,iBAAM;UACNA,gCAAwB;UACtBA,sEA0DM;UACRA,iBAAM;UACNA,gCAA0B;UACgDA,4BAAM;UAAAA,iBAAS;UACvFA,mCAA4E;UAA9BA;YAAA,OAASyF,uBAAmB;UAAA,EAAC;UACzEzF,yBAAuC;UAAAA,uCACzC;UAAAA,iBAAS;UAOjBA,gCAA2H;UAI9DA,6EAAwC;UAAAA,iBAAK;UAClGA,8BAA6F;UAC/FA,iBAAM;UACNA,gCAAwB;UACtBA,qEAIM;UAENA,qEAEM;UAENA,sEAiCM;UACRA,iBAAM;UACNA,gCAA0B;UACgDA,iCAAM;UAAAA,iBAAS;;;UA5QvFA,eAAa;UAAbA,kCAAa;UAMbA,eAAW;UAAXA,gCAAW;UAIXA,eAAwB;UAAxBA,iDAAwB;UAoH9BA,eAAuC;UAAvCA,yDAAuC;UAiB3BA,eAAmB;UAAnBA,wCAAmB;UA+EnBA,gBAAoB;UAApBA,yCAAoB;UAMpBA,eAA0D;UAA1DA,mFAA0D;UAI1DA,eAAwD;UAAxDA,iFAAwD;;;qBD1N1DL,aAAa,EAAEC,YAAY,mCAAEC,WAAW,0KAAEC,mBAAmB,EAAEC,YAAY;MAAA2F;IAAA;EAAA", "names": ["MatchType", "bootstrap", "LocalDatePipe", "CommonModule", "FormsModule", "ReactiveFormsModule", "SharedModule", "i0", "SecurityComponent", "constructor", "securityService", "modalService", "toastr", "ExactMatch", "ngOnInit", "loadSecurityDashboard", "loading", "error", "getSecurityDashboard", "subscribe", "next", "response", "securityEvents", "recentSecurityEvents", "map", "event", "timestamp", "Date", "failedConnectionStats", "stat", "lastFailedCertificateValidation", "undefined", "lastFailedApiKeyValidation", "err", "console", "openResolveSecurityEventModal", "securityEvent", "selectedSecurityEvent", "open", "onResolveSecurityEvent", "resolution", "resolveSecurityEvent", "id", "success", "onIgnoreSecurityEvent", "ignoreSecurityEvent", "getSeverityClass", "severity", "getSeverityIcon", "getSeverityText", "getEventTypeClass", "eventType", "getEventTypeIcon", "getEventTypeText", "openBlockEventModal", "selectedEvent", "deleteExistingEvents", "applyToAllIps", "filterIpAddress", "ip<PERSON><PERSON><PERSON>", "modal", "Modal", "document", "getElementById", "show", "onApplyToAllIpsChange", "confirmBlockEvent", "eventTypeNumber", "getEventTypeNumber", "request", "description", "descriptionMatchType", "ipAddressMatchType", "createSecurityEventFilter", "getInstance", "hide", "message", "openManageFiltersModal", "loadSecurityEventFilters", "loadingFilters", "getSecurityEventFilters", "data", "securityEventFilters", "filter", "createdAt", "confirmDeleteFilter", "confirm", "eventTypeName", "deleteSecurityEventFilter", "selectors", "standalone", "features", "decls", "vars", "consts", "template", "ctx", "styles"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\security\\security.component.ts", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\security\\security.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { SecurityService } from '../services/security.service';\nimport {\n  SecurityEventResponse,\n  FailedConnectionStatsResponse,\n  SecurityEventFilterResponse,\n  CreateSecurityEventFilterRequest,\n  MatchType\n} from '../models/security.model';\n\nimport { ModalService } from '../services/modal.service';\nimport { ToastrService } from 'ngx-toastr';\nimport * as bootstrap from 'bootstrap';\nimport { LocalDatePipe } from '../shared/pipes/local-date.pipe';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { SharedModule } from '../shared/shared.module';\n\n@Component({\n  selector: 'app-security',\n  templateUrl: './security.component.html',\n  styleUrls: ['./security.component.css'],\n  imports: [LocalDatePipe, CommonModule, FormsModule, ReactiveFormsModule, SharedModule],\n  standalone: true\n})\nexport class SecurityComponent implements OnInit {\n  loading: boolean = false;\n  error: string | null = null;\n\n  securityEvents: SecurityEventResponse[] = [];\n  failedConnectionStats: FailedConnectionStatsResponse[] = [];\n\n  selectedSecurityEvent: SecurityEventResponse | null = null;\n\n  // Security Event Filters\n  securityEventFilters: SecurityEventFilterResponse[] = [];\n  selectedEvent: SecurityEventResponse | null = null;\n  deleteExistingEvents: boolean = false;\n  loadingFilters: boolean = false;\n  applyToAllIps: boolean = true; // Výchozí stav - použít pro všechny IP\n  filterIpAddress: string = '';\n  descriptionMatchType: MatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro popis\n  ipAddressMatchType: MatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro IP adresu\n\n  constructor(\n    private securityService: SecurityService,\n    private modalService: ModalService,\n    private toastr: ToastrService\n  ) {\n  }\n\n  ngOnInit(): void {\n    this.loadSecurityDashboard();\n  }\n\n  loadSecurityDashboard(): void {\n    this.loading = true;\n    this.error = null;\n\n    this.securityService.getSecurityDashboard().subscribe({\n      next: (response) => {\n        // Převod časů na lokální čas\n        this.securityEvents = response.recentSecurityEvents.map(event => ({\n          ...event,\n          timestamp: new Date(event.timestamp)\n        }));\n\n        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({\n          ...stat,\n          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,\n          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined\n        }));\n\n        this.loading = false;\n      },\n      error: (err) => {\n        console.error('Error loading security dashboard', err);\n        this.error = 'Chyba při načítání bezpečnostního dashboardu';\n        this.loading = false;\n      }\n    });\n  }\n\n  openResolveSecurityEventModal(securityEvent: SecurityEventResponse): void {\n    this.selectedSecurityEvent = securityEvent;\n    this.modalService.open('resolveSecurityEventModal');\n  }\n\n  onResolveSecurityEvent(resolution: string): void {\n    if (!this.selectedSecurityEvent) {\n      return;\n    }\n\n    this.securityService.resolveSecurityEvent(this.selectedSecurityEvent.id, resolution).subscribe({\n      next: () => {\n        this.toastr.success('Bezpečnostní událost byla úspěšně vyřešena', 'Úspěch');\n        this.loadSecurityDashboard();\n      },\n      error: (err) => {\n        console.error('Error resolving security event', err);\n        this.error = 'Chyba při řešení bezpečnostní události';\n        this.toastr.error('Chyba při řešení bezpečnostní události', 'Chyba');\n      }\n    });\n  }\n\n  onIgnoreSecurityEvent(): void {\n    if (!this.selectedSecurityEvent) {\n      return;\n    }\n\n    this.securityService.ignoreSecurityEvent(this.selectedSecurityEvent.id).subscribe({\n      next: () => {\n        this.toastr.success('Bezpečnostní událost byla ignorována', 'Úspěch');\n        this.loadSecurityDashboard();\n      },\n      error: (err) => {\n        console.error('Error ignoring security event', err);\n        this.error = 'Chyba při ignorování bezpečnostní události';\n        this.toastr.error('Chyba při ignorování bezpečnostní události', 'Chyba');\n      }\n    });\n  }\n\n  getSeverityClass(severity: number): string {\n    switch (severity) {\n      case 5:\n        return 'text-danger fw-bold';\n      case 4:\n        return 'text-danger';\n      case 3:\n        return 'text-warning';\n      case 2:\n        return 'text-info';\n      case 1:\n        return 'text-success';\n      default:\n        return 'text-muted';\n    }\n  }\n\n  getSeverityIcon(severity: number): string {\n    switch (severity) {\n      case 5:\n      case 4:\n        return 'bi-exclamation-triangle-fill';\n      case 3:\n        return 'bi-exclamation-circle-fill';\n      case 2:\n        return 'bi-info-circle-fill';\n      default:\n        return 'bi-check-circle-fill';\n    }\n  }\n\n  getSeverityText(severity: number): string {\n    switch (severity) {\n      case 5:\n        return 'Kritická';\n      case 4:\n        return 'Vysoká';\n      case 3:\n        return 'Střední';\n      case 2:\n        return 'Nízká';\n      case 1:\n        return 'Informační';\n      default:\n        return 'Neznámá';\n    }\n  }\n\n  getEventTypeClass(eventType: string): string {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'text-danger';\n      case 'CertificateValidationFailure':\n        return 'text-warning';\n      case 'IpBlocked':\n        return 'text-danger';\n      case 'FailedAccessAttempt':\n        return 'text-warning';\n      default:\n        return 'text-info';\n    }\n  }\n\n  getEventTypeIcon(eventType: string): string {\n    switch (eventType) {\n      case 'SuspiciousActivity':\n        return 'bi-exclamation-triangle-fill';\n      case 'CertificateValidationFailure':\n        return 'bi-shield-exclamation';\n      case 'IpBlocked':\n        return 'bi-slash-circle-fill';\n      case 'FailedAccessAttempt':\n        return 'bi-x-circle-fill';\n      case 'ApiKeyMisuse':\n        return 'bi-key-fill';\n      case 'UnauthorizedAccess':\n        return 'bi-shield-x';\n      case 'Other':\n        return 'bi-question-circle-fill';\n      default:\n        return 'bi-info-circle-fill';\n    }\n  }\n\n  getEventTypeText(eventType: string): string {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 'Neúspěšný pokus o přístup';\n      case 'SuspiciousActivity':\n        return 'Podezřelá aktivita';\n      case 'IpBlocked':\n        return 'Blokovaná IP adresa';\n      case 'CertificateValidationFailure':\n        return 'Selhání validace certifikátu';\n      case 'ApiKeyMisuse':\n        return 'Nesprávné použití API klíče';\n      case 'UnauthorizedAccess':\n        return 'Neautorizovaný přístup';\n      case 'Other':\n        return 'Ostatní';\n      default:\n        return eventType;\n    }\n  }\n\n  // Security Event Filter methods\n  openBlockEventModal(event: SecurityEventResponse): void {\n    this.selectedEvent = event;\n    this.deleteExistingEvents = false;\n    this.applyToAllIps = true; // Výchozí stav\n    this.filterIpAddress = event.ipAddress || '';\n    const modal = new bootstrap.Modal(document.getElementById('blockEventModal')!);\n    modal.show();\n  }\n\n  onApplyToAllIpsChange(): void {\n    if (!this.applyToAllIps && this.selectedEvent) {\n      // Pokud se vypne \"pro všechny IP\", nastav IP adresu z události\n      this.filterIpAddress = this.selectedEvent.ipAddress || '';\n    }\n  }\n\n  confirmBlockEvent(): void {\n    if (!this.selectedEvent) return;\n\n    const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);\n\n    const request: CreateSecurityEventFilterRequest = {\n      eventType: eventTypeNumber,\n      description: this.selectedEvent.description,\n      descriptionMatchType: this.descriptionMatchType,\n      ipAddress: this.applyToAllIps ? undefined : this.filterIpAddress,\n      ipAddressMatchType: this.ipAddressMatchType,\n      deleteExistingEvents: this.deleteExistingEvents\n    };\n\n    this.securityService.createSecurityEventFilter(request).subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');\n          this.loadSecurityDashboard(); // Refresh data\n          const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal')!);\n          modal?.hide();\n        } else {\n          this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');\n        }\n      },\n      error: (error) => {\n        console.error('Error creating filter:', error);\n        this.toastr.error('Chyba při vytváření filtru', 'Chyba');\n      }\n    });\n  }\n\n  openManageFiltersModal(): void {\n    this.loadSecurityEventFilters();\n    const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal')!);\n    modal.show();\n  }\n\n  loadSecurityEventFilters(): void {\n    this.loadingFilters = true;\n    this.securityService.getSecurityEventFilters().subscribe({\n      next: (response) => {\n        if (response.success && response.data) {\n          this.securityEventFilters = response.data.map(filter => ({\n            ...filter,\n            createdAt: new Date(filter.createdAt)\n          }));\n        } else {\n          this.securityEventFilters = [];\n        }\n        this.loadingFilters = false;\n      },\n      error: (error) => {\n        console.error('Error loading filters:', error);\n        this.toastr.error('Chyba při načítání filtrů', 'Chyba');\n        this.loadingFilters = false;\n      }\n    });\n  }\n\n  confirmDeleteFilter(filter: SecurityEventFilterResponse): void {\n    if (confirm(`Opravdu chcete smazat filtr pro \"${filter.eventTypeName}\"?`)) {\n      this.securityService.deleteSecurityEventFilter(filter.id).subscribe({\n        next: (response) => {\n          if (response.success) {\n            this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');\n            this.loadSecurityEventFilters(); // Refresh filters\n          } else {\n            this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');\n          }\n        },\n        error: (error) => {\n          console.error('Error deleting filter:', error);\n          this.toastr.error('Chyba při mazání filtru', 'Chyba');\n        }\n      });\n    }\n  }\n\n  private getEventTypeNumber(eventType: string): number {\n    switch (eventType) {\n      case 'FailedAccessAttempt':\n        return 0;\n      case 'SuspiciousActivity':\n        return 1;\n      case 'IpBlocked':\n        return 2;\n      case 'CertificateValidationFailure':\n        return 3;\n      case 'ApiKeyMisuse':\n        return 4;\n      case 'UnauthorizedAccess':\n        return 5;\n      case 'Other':\n        return 6;\n      default:\n        return 6; // Other\n    }\n  }\n}\n", "<div class=\"container\">\n  <div class=\"d-flex justify-content-between align-items-center mb-4\">\n    <h2>Bezpečnostní u<PERSON></h2>\n    <button class=\"btn btn-primary\" (click)=\"loadSecurityDashboard()\">\n      <i class=\"bi bi-arrow-clockwise me-2\"></i>Aktualizovat\n    </button>\n  </div>\n\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center my-5\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Načítání...</span>\n    </div>\n  </div>\n\n  <div *ngIf=\"error\" class=\"alert alert-danger mb-4\">\n    {{ error }}\n  </div>\n\n  <div *ngIf=\"!loading && !error\">\n    <!-- Statistiky neúspěšných připojení -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header d-flex justify-content-between align-items-center\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-shield-exclamation me-2\"></i>Statistiky neúspěšných připojení\n        </h5>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"failedConnectionStats.length === 0\" class=\"alert alert-info\">\n          Žádné neúspěšné pokusy o připojení.\n        </div>\n        <div *ngIf=\"failedConnectionStats.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th class=\"text-nowrap\">Instance</th>\n                <th class=\"text-nowrap\">Neúsp. validace cert.</th>\n                <th class=\"text-nowrap\">Poslední neúsp. validace</th>\n                <th class=\"text-nowrap\">Neúsp. validace API klíče</th>\n                <th class=\"text-nowrap\">Poslední neúsp. validace API</th>\n                <th class=\"text-nowrap\">Poslední IP</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let stat of failedConnectionStats\">\n                <td>{{ stat.instanceName }}</td>\n                <td>\n                  <span [ngClass]=\"stat.failedCertificateValidationCount > 10 ? 'text-danger' : 'text-warning'\">\n                    {{ stat.failedCertificateValidationCount }}\n                  </span>\n                </td>\n                <td>{{ stat.lastFailedCertificateValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>\n                <td>\n                  <span [ngClass]=\"stat.failedApiKeyValidationCount > 10 ? 'text-danger' : 'text-warning'\">\n                    {{ stat.failedApiKeyValidationCount }}\n                  </span>\n                </td>\n                <td>{{ stat.lastFailedApiKeyValidation | localDate:'dd.MM.yyyy HH:mm' || '-' }}</td>\n                <td>{{ stat.lastKnownIpAddress || '-' }}</td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n\n    <!-- Nedávné bezpečnostní události -->\n    <div class=\"card mb-4\">\n      <div class=\"card-header d-flex justify-content-between align-items-center\">\n        <h5 class=\"mb-0\">\n          <i class=\"bi bi-journal-text me-2\"></i>Nedávné bezpečnostní události\n        </h5>\n        <button type=\"button\" class=\"btn btn-outline-primary btn-sm\" (click)=\"openManageFiltersModal()\">\n          <i class=\"bi bi-funnel me-1\"></i>Spravovat filtry\n        </button>\n      </div>\n      <div class=\"card-body\">\n        <div *ngIf=\"securityEvents.length === 0\" class=\"alert alert-info\">\n          Žádné bezpečnostní události.\n        </div>\n        <div *ngIf=\"securityEvents.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th>Závažnost</th>\n                <th>Typ</th>\n                <th>Čas</th>\n                <th>Popis</th>\n                <th class=\"text-nowrap\">IP adresa</th>\n                <th>Uživatel</th>\n                <th>Akce</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let event of securityEvents\">\n                <td class=\"text-nowrap\">\n                  <span [ngClass]=\"getSeverityClass(event.severity)\">\n                    <i [ngClass]=\"getSeverityIcon(event.severity) + ' me-1'\"></i>\n                    {{ getSeverityText(event.severity) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">\n                  <span [ngClass]=\"getEventTypeClass(event.eventType)\">\n                    <i [ngClass]=\"getEventTypeIcon(event.eventType) + ' me-1'\"></i>{{ getEventTypeText(event.eventType) }}\n                  </span>\n                </td>\n                <td class=\"text-nowrap\">{{ event.timestamp | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ event.description }}</td>\n                <td>{{ event.ipAddress }}</td>\n                <td class=\"text-nowrap\">{{ event.username || '-' }}</td>\n                <td>\n                  <div class=\"btn-group\" role=\"group\">\n                    <button *ngIf=\"!event.isResolved && !event.isIgnored\" type=\"button\" class=\"btn btn-outline-primary btn-sm\"\n                            (click)=\"openResolveSecurityEventModal(event)\"\n                            title=\"Vyřešit\">\n                      <i class=\"bi bi-check-circle\"></i>\n                    </button>\n                    <button type=\"button\" class=\"btn btn-outline-danger btn-sm\"\n                            (click)=\"openBlockEventModal(event)\"\n                            title=\"Blokovat událost\">\n                      <i class=\"bi bi-slash-circle\"></i>\n                    </button>\n                  </div>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Sdílená komponenta pro řešení bezpečnostních událostí -->\n<app-resolve-alert-modal\n  [securityEvent]=\"selectedSecurityEvent\"\n  modalId=\"resolveSecurityEventModal\"\n  entityType=\"securityEvent\"\n  (resolve)=\"onResolveSecurityEvent($event)\"\n  (ignore)=\"onIgnoreSecurityEvent()\"\n  (close)=\"selectedSecurityEvent = null\">\n</app-resolve-alert-modal>\n\n<!-- Modal pro blokování události -->\n<div class=\"modal fade\" id=\"blockEventModal\" tabindex=\"-1\" aria-labelledby=\"blockEventModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header bg-warning text-dark\">\n        <h5 class=\"modal-title\" id=\"blockEventModalLabel\">Blokovat bezpečnostní událost</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"selectedEvent\">\n          <div class=\"alert alert-warning\">\n            <i class=\"bi bi-exclamation-triangle me-1\"></i>\n            <strong>Pozor!</strong> Vytvoříte filtr, který zablokuje podobné bezpečnostní události v budoucnu.\n          </div>\n\n          <h6>Náhled filtru:</h6>\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <p><strong>Typ události:</strong> {{ getEventTypeText(selectedEvent.eventType) }}</p>\n\n              <div class=\"mb-3\">\n                <label class=\"form-label\"><strong>Popis:</strong></label>\n                <div class=\"input-group\">\n                  <input type=\"text\" class=\"form-control\" [(ngModel)]=\"selectedEvent.description\" readonly>\n                  <select class=\"form-select\" style=\"max-width: 150px;\" [(ngModel)]=\"descriptionMatchType\">\n                    <option [value]=\"0\">Přesná shoda</option>\n                    <option [value]=\"1\">Obsahuje</option>\n                    <option [value]=\"2\">Začíná na</option>\n                    <option [value]=\"3\">Končí na</option>\n                  </select>\n                </div>\n                <small class=\"form-text text-muted\">Vyberte, jak má být popis porovnáván</small>\n              </div>\n\n              <div class=\"mb-3\">\n                <div class=\"form-check\">\n                  <input class=\"form-check-input\" type=\"checkbox\" id=\"applyToAllIps\" [(ngModel)]=\"applyToAllIps\" (change)=\"onApplyToAllIpsChange()\">\n                  <label class=\"form-check-label\" for=\"applyToAllIps\">\n                    <strong>Použít pro všechny IP adresy</strong>\n                  </label>\n                </div>\n                <div *ngIf=\"!applyToAllIps\" class=\"mt-2\">\n                  <label for=\"filterIpAddress\" class=\"form-label\"><strong>IP adresa:</strong></label>\n                  <div class=\"input-group\">\n                    <input type=\"text\" class=\"form-control\" id=\"filterIpAddress\" [(ngModel)]=\"filterIpAddress\" placeholder=\"Zadejte IP adresu\">\n                    <select class=\"form-select\" style=\"max-width: 150px;\" [(ngModel)]=\"ipAddressMatchType\">\n                      <option [value]=\"0\">Přesná shoda</option>\n                      <option [value]=\"1\">Obsahuje</option>\n                      <option [value]=\"2\">Začíná na</option>\n                      <option [value]=\"3\">Končí na</option>\n                    </select>\n                  </div>\n                  <small class=\"form-text text-muted\">Vyberte, jak má být IP adresa porovnávána</small>\n                </div>\n                <div *ngIf=\"applyToAllIps\" class=\"mt-2 text-muted\">\n                  <small>Filtr bude aplikován na všechny IP adresy</small>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"form-check mt-3\">\n            <input class=\"form-check-input\" type=\"checkbox\" id=\"deleteExistingEvents\" [(ngModel)]=\"deleteExistingEvents\">\n            <label class=\"form-check-label\" for=\"deleteExistingEvents\">\n              Smazat také existující události odpovídající tomuto vzoru\n            </label>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zrušit</button>\n        <button type=\"button\" class=\"btn btn-warning\" (click)=\"confirmBlockEvent()\">\n          <i class=\"bi bi-slash-circle me-1\"></i>Blokovat událost\n        </button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal pro správu filtrů -->\n<div class=\"modal fade\" id=\"manageFiltersModal\" tabindex=\"-1\" aria-labelledby=\"manageFiltersModalLabel\" aria-hidden=\"true\">\n  <div class=\"modal-dialog modal-xl\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\" id=\"manageFiltersModalLabel\">Spravovat filtry bezpečnostních událostí</h5>\n        <button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\" aria-label=\"Zavřít\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"loadingFilters\" class=\"text-center\">\n          <div class=\"spinner-border\" role=\"status\">\n            <span class=\"visually-hidden\">Načítání...</span>\n          </div>\n        </div>\n\n        <div *ngIf=\"!loadingFilters && securityEventFilters.length === 0\" class=\"alert alert-info\">\n          Žádné filtry nejsou nastaveny.\n        </div>\n\n        <div *ngIf=\"!loadingFilters && securityEventFilters.length > 0\" class=\"table-responsive\">\n          <table class=\"table table-hover\">\n            <thead>\n              <tr>\n                <th class=\"text-nowrap\">Typ události</th>\n                <th class=\"text-nowrap\">Popis</th>\n                <th class=\"text-nowrap\">Typ porovnávání</th>\n                <th class=\"text-nowrap\">IP adresa</th>\n                <th class=\"text-nowrap\">Typ porovnávání</th>\n                <th class=\"text-nowrap\">Vytvořeno</th>\n                <th class=\"text-nowrap\">Vytvořil</th>\n                <th class=\"text-nowrap\">Akce</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let filter of securityEventFilters\">\n                <td>{{ filter.eventTypeName }}</td>\n                <td>{{ filter.description || '-' }}</td>\n                <td><small class=\"text-muted\">{{ filter.descriptionMatchTypeName }}</small></td>\n                <td>{{ filter.ipAddress || 'Všechny IP' }}</td>\n                <td><small class=\"text-muted\">{{ filter.ipAddressMatchTypeName }}</small></td>\n                <td>{{ filter.createdAt | localDate:'dd.MM.yyyy HH:mm' }}</td>\n                <td>{{ filter.createdBy }}</td>\n                <td>\n                  <button type=\"button\" class=\"btn btn-outline-danger btn-sm\"\n                          (click)=\"confirmDeleteFilter(filter)\"\n                          title=\"Smazat filtr\">\n                    <i class=\"bi bi-trash\"></i>\n                  </button>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Zavřít</button>\n      </div>\n    </div>\n  </div>\n</div>\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}