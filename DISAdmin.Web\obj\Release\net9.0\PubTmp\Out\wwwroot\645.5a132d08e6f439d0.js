"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[645],{1645:(P,v,d)=>{d.r(v),d.d(v,{MonitoringModule:()=>w});var g=d(6895),m=d(6123),f=d(433),y=d(5861),p=d(3192),t=d(1571),b=d(4612),C=d(985),S=d(7556),z=d(9032),E=d(9991),x=d(9523),T=d(8374);const I=["certificateUsageChart"],k=["securityEventsChart"],Z=["instancesStatusChart"];function _(n,r){if(1&n){const o=t.EpF();t.TgZ(0,"div",19),t._uU(1),t.TgZ(2,"button",20),t.NdJ("click",function(){t.CHM(o);const i=t.oxw();return t.KtG(i.error=null)}),t.qZA()()}if(2&n){const o=t.oxw();t.xp6(1),t.hij(" ",o.error," ")}}function D(n,r){1&n&&(t.TgZ(0,"div",21)(1,"div",22)(2,"span",23),t._uU(3,"Na\u010d\xedt\xe1n\xed..."),t.qZA()(),t.TgZ(4,"p",24),t._uU(5,"Na\u010d\xedt\xe1n\xed metrik..."),t.qZA()())}function U(n,r){if(1&n){const o=t.EpF();t.TgZ(0,"div",27)(1,"div",6)(2,"div",28)(3,"h5",36),t._uU(4,"Vyu\u017eit\xed certifik\xe1t\u016f"),t.qZA(),t.TgZ(5,"button",30),t.NdJ("click",function(){t.CHM(o);const i=t.MAs(10),s=t.oxw(3);return t.KtG(s.openFullscreenChart(i,"Vyu\u017eit\xed certifik\xe1t\u016f"))}),t._UZ(6,"i",31),t.qZA()(),t.TgZ(7,"div",32)(8,"div",33),t._UZ(9,"canvas",null,37),t.qZA()()()()}}function A(n,r){if(1&n){const o=t.EpF();t.TgZ(0,"div",27)(1,"div",6)(2,"div",28)(3,"h5",38),t._uU(4,"Bezpe\u010dnostn\xed ud\xe1losti"),t.qZA(),t.TgZ(5,"button",30),t.NdJ("click",function(){t.CHM(o);const i=t.MAs(10),s=t.oxw(3);return t.KtG(s.openFullscreenChart(i,"Bezpe\u010dnostn\xed ud\xe1losti"))}),t._UZ(6,"i",31),t.qZA()(),t.TgZ(7,"div",32)(8,"div",33),t._UZ(9,"canvas",null,39),t.qZA()()()()}}function R(n,r){if(1&n&&(t.TgZ(0,"div",26),t.YNc(1,U,11,0,"div",35),t.YNc(2,A,11,0,"div",35),t.qZA()),2&n){const o=t.oxw(2);t.xp6(1),t.Q6J("ngIf","all"===o.filterData.metricType||"certificate"===o.filterData.metricType),t.xp6(1),t.Q6J("ngIf","all"===o.filterData.metricType||"security"===o.filterData.metricType)}}function M(n,r){if(1&n){const o=t.EpF();t.TgZ(0,"div"),t.YNc(1,R,3,2,"div",25),t.TgZ(2,"div",26)(3,"div",27)(4,"div",6)(5,"div",28)(6,"h5",29),t._uU(7,"Stav instanc\xed"),t.qZA(),t.TgZ(8,"button",30),t.NdJ("click",function(){t.CHM(o);const i=t.MAs(13),s=t.oxw();return t.KtG(s.openFullscreenChart(i,"Stav instanc\xed"))}),t._UZ(9,"i",31),t.qZA()(),t.TgZ(10,"div",32)(11,"div",33),t._UZ(12,"canvas",null,34),t.qZA()()()()()()}if(2&n){const o=t.oxw();t.xp6(1),t.Q6J("ngIf",o.isAdmin&&("all"===o.filterData.metricType||"certificate"===o.filterData.metricType||"security"===o.filterData.metricType))}}p.kL.register(...p.zX);let F=(()=>{const r=class{constructor(e,i,s,a,c,h){this.monitoringService=e,this.chartService=i,this.authService=s,this.signalRService=a,this.chartModalService=c,this.instanceService=h,this.loading=!0,this.error=null,this.isAdmin=!1,this.systemStatistics={},this.certificateUsageChart=null,this.securityEventsChart=null,this.instancesStatusChart=null,this.filterFields=[],this.customers=[],this.instances=[],this.filterData={dateRange:"7",metricType:"all"},this.filtersInitialized=!1,this.chartsInitialized=!1,this.updateSubscription=null,this.signalRSubscriptions=[],this.authService.currentUser.subscribe(l=>{this.currentUser=l,this.isAdmin=l?.isAdmin||!1})}ngOnInit(){this.loadCustomers(),this.loadSavedFilters(),this.loadSystemStatistics(),this.initSignalR()}loadSavedFilters(){try{const e=localStorage.getItem("last_filter_monitoring");if(e){const i=JSON.parse(e);this.filterData={dateRange:i.dateRange||"7",customerId:i.customerId||void 0,instanceId:i.instanceId||void 0,metricType:i.metricType||"all"},console.log("Na\u010dteny ulo\u017een\xe9 filtry z localStorage:",this.filterData)}else console.log("\u017d\xe1dn\xe9 ulo\u017een\xe9 filtry, pou\u017e\xedv\xe1m v\xfdchoz\xed hodnoty:",this.filterData)}catch(e){console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed ulo\u017een\xfdch filtr\u016f",e)}}loadCustomers(){this.monitoringService.getCustomers().subscribe({next:e=>{this.customers=e,this.initFilterFields(),this.filterData.customerId&&this.loadInstances(this.filterData.customerId)},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed z\xe1kazn\xedk\u016f",e)}})}loadInstances(e){this.instanceService.getInstancesByCustomerId(e).subscribe({next:i=>{this.instances=i,this.updateInstanceFilterField()},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed instanc\xed",i)}})}initFilterFields(){this.filterFields=[{name:"dateRange",label:"\u010casov\xe9 obdob\xed",type:"select",options:[{value:"1",label:"Posledn\xed 1 den"},{value:"7",label:"Posledn\xedch 7 dn\xed"},{value:"30",label:"Posledn\xedch 30 dn\xed"},{value:"90",label:"Posledn\xedch 90 dn\xed"}]},{name:"customerId",label:"Z\xe1kazn\xedk",type:"select",options:[{value:"",label:"V\u0161ichni z\xe1kazn\xedci"},...this.customers.map(e=>({value:e.id,label:e.name}))],onChange:e=>{e?this.loadInstances(e):(this.instances=[],this.updateInstanceFilterField())}},{name:"instanceId",label:"Instance",type:"select",options:[{value:"",label:"V\u0161echny instance"}]},{name:"metricType",label:"Typ metrik",type:"select",options:[{value:"all",label:"V\u0161echny metriky"},{value:"api",label:"API metriky"},{value:"certificate",label:"Certifik\xe1ty"},...this.isAdmin?[{value:"security",label:"Bezpe\u010dnost"}]:[]]}]}updateInstanceFilterField(){const e=this.filterFields.find(i=>"instanceId"===i.name);e&&(e.options=[{value:"",label:"V\u0161echny instance"},...this.instances.sort((i,s)=>{const a=i.customerAbbreviation.localeCompare(s.customerAbbreviation);return 0!==a?a:i.name.localeCompare(s.name)}).map(i=>({value:i.id,label:`${i.customerAbbreviation} - ${i.name}`}))])}onFilterChange(e){console.log("onFilterChange called with filters:",e),this.filterData={dateRange:e.dateRange||"7",customerId:e.customerId||void 0,instanceId:e.instanceId||void 0,metricType:e.metricType||"all"},console.log("Updated filterData:",this.filterData),e.customerId&&e.customerId!==this.filterData.customerId&&this.loadInstances(e.customerId),this.filtersInitialized||(this.filtersInitialized=!0,console.log("Filtry byly inicializov\xe1ny, na\u010d\xedt\xe1m grafy..."),this.chartsInitialized)?(this.refreshData(),setTimeout(()=>{this.initPopovers()},500)):this.initChartsWithCorrectFilters()}onExportData(e){console.log("Export dat s filtry:",e),this.monitoringService.exportMetricsData(e.instanceId,e.customerId,parseInt(e.dateRange||"7"),e.metricType||"all").subscribe({next:i=>{const s=new Blob([i],{type:"text/csv"}),a=window.URL.createObjectURL(s),c=document.createElement("a");c.href=a,c.download=`metriky_${(new Date).toISOString().slice(0,10)}.csv`,document.body.appendChild(c),c.click(),window.URL.revokeObjectURL(a),document.body.removeChild(c)},error:i=>{console.error("Chyba p\u0159i exportu dat",i)}})}ngOnDestroy(){this.updateSubscription&&this.updateSubscription.unsubscribe(),this.signalRSubscriptions.forEach(e=>e.unsubscribe()),this.signalRService.stopConnection(),this.destroyCharts()}openFullscreenChart(e,i){if(e instanceof HTMLCanvasElement){const s=p.kL.getChart(e);this.chartModalService.openChartModal(s||null,i)}else this.chartModalService.openChartModal(e,i)}loadSystemStatistics(){this.monitoringService.getSystemStatistics().subscribe({next:e=>{this.systemStatistics=e},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed syst\xe9mov\xfdch statistik",e),this.error="Nepoda\u0159ilo se na\u010d\xedst syst\xe9mov\xe9 statistiky"}})}refreshData(){this.loadSystemStatistics(),this.refreshCharts(),setTimeout(()=>{this.initPopovers()},500)}initCharts(){this.loading=!0,this.loadInstancesStatusChart(),this.isAdmin&&(this.loadCertificateUsageChart(),this.loadSecurityEventsChart()),this.loading=!1}initChartsWithCorrectFilters(){console.log("initChartsWithCorrectFilters called with filterData:",this.filterData),this.chartsInitialized=!0,this.loading=!0,this.loadInstancesStatusChart(),this.isAdmin&&(this.loadCertificateUsageChart(),this.loadSecurityEventsChart()),this.loading=!1}refreshCharts(){this.loadInstancesStatusChart(),this.isAdmin&&(this.loadCertificateUsageChart(),this.loadSecurityEventsChart())}destroyCharts(){this.certificateUsageChart&&(this.certificateUsageChart.destroy(),this.certificateUsageChart=null),this.securityEventsChart&&(this.securityEventsChart.destroy(),this.securityEventsChart=null),this.instancesStatusChart&&(this.instancesStatusChart.destroy(),this.instancesStatusChart=null)}loadCertificateUsageChart(){const e=this.getFilters();this.chartService.getCertificateUsageChartData(e.instanceId,e.customerId,parseInt(e.dateRange)).subscribe({next:i=>{this.certificateUsageChart?(this.certificateUsageChart.data.labels=i.labels,this.certificateUsageChart.data.datasets=i.datasets,this.certificateUsageChart.update()):this.certificateUsageChartRef&&(this.certificateUsageChart=new p.kL(this.certificateUsageChartRef.nativeElement,{type:"bar",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det operac\xed"}},x:{title:{display:!0,text:"Typ operace"}}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf vyu\u017eit\xed certifik\xe1t\u016f",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf vyu\u017eit\xed certifik\xe1t\u016f"}})}loadSecurityEventsChart(){const e=this.getFilters();this.chartService.getSecurityEventsChartData(parseInt(e.dateRange)).subscribe({next:i=>{this.securityEventsChart?(this.securityEventsChart.data.labels=i.labels,this.securityEventsChart.data.datasets=i.datasets,this.securityEventsChart.update()):this.securityEventsChartRef&&(this.securityEventsChart=new p.kL(this.securityEventsChartRef.nativeElement,{type:"line",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"top"},tooltip:{mode:"index",intersect:!1}},scales:{y:{beginAtZero:!0,title:{display:!0,text:"Po\u010det ud\xe1lost\xed"}},x:{title:{display:!0,text:"Datum"}}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf bezpe\u010dnostn\xedch ud\xe1lost\xed",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf bezpe\u010dnostn\xedch ud\xe1lost\xed"}})}loadInstancesStatusChart(){const e=this.getFilters();this.chartService.getInstancesStatusChartData(e.customerId).subscribe({next:i=>{this.instancesStatusChart?(this.instancesStatusChart.data.labels=i.labels,this.instancesStatusChart.data.datasets=i.datasets,this.instancesStatusChart.update()):this.instancesStatusChartRef&&(this.instancesStatusChart=new p.kL(this.instancesStatusChartRef.nativeElement,{type:"doughnut",data:{labels:i.labels,datasets:i.datasets},options:{responsive:!0,maintainAspectRatio:!1,plugins:{title:{display:!0,text:i.title,font:{size:16}},legend:{display:!0,position:"right"},tooltip:{mode:"index",intersect:!1}}}}))},error:i=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed dat pro graf stavu instanc\xed",i),this.error="Nepoda\u0159ilo se na\u010d\xedst data pro graf stavu instanc\xed"}})}getFilters(){return this.filterData}initSignalR(){var e=this;return(0,y.Z)(function*(){try{if(console.log("Initializing SignalR connection..."),yield e.signalRService.startConnection(),!(yield e.signalRService.testConnection()))return void console.warn("SignalR connection test failed. Falling back to interval-based updates.");console.log("SignalR connection test successful. Joining groups..."),yield e.signalRService.joinGroup("api-metrics"),yield e.signalRService.joinGroup("certificate-metrics"),e.isAdmin&&(yield e.signalRService.joinGroup("security-metrics"),yield e.signalRService.joinGroup("all-metrics")),e.signalRSubscriptions.push(e.signalRService.certificateMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.isAdmin&&e.updateCertificateChart(s)})),e.signalRSubscriptions.push(e.signalRService.securityMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.isAdmin&&e.updateSecurityChart(s)})),e.signalRSubscriptions.push(e.signalRService.allMetrics$.subscribe(s=>{Object.keys(s).length>0&&e.loadSystemStatistics()})),console.log("SignalR initialization completed successfully.")}catch(i){console.error("Error during SignalR initialization:",i),console.warn("Falling back to interval-based updates.")}})()}updateCertificateChart(e){if(this.certificateUsageChart&&e.certificateUsage){const i=Object.keys(e.certificateUsage);for(const s of i){const a=this.certificateUsageChart.data.labels,c=a.indexOf(s);if(-1!==c)for(let h=0;h<this.certificateUsageChart.data.datasets.length;h++){const l=this.certificateUsageChart.data.datasets[h],u=l.label;void 0!==e.certificateUsage[s][u]&&(l.data[c]=e.certificateUsage[s][u])}else{a.push(s);for(let h=0;h<this.certificateUsageChart.data.datasets.length;h++){const l=this.certificateUsageChart.data.datasets[h],u=l.label;l.data.push(void 0!==e.certificateUsage[s][u]?e.certificateUsage[s][u]:0)}}}this.certificateUsageChart.update("none")}}updateSecurityChart(e){if(this.securityEventsChart&&e.securityEvents){const s=new Date(e.securityEvents.timestamp).toLocaleTimeString();for(let a=0;a<this.securityEventsChart.data.datasets.length;a++){const c=this.securityEventsChart.data.datasets[a],h=c.label;if(e.securityEvents[h]){const l=this.securityEventsChart.data.labels,u=c.data;l.length>0&&l[l.length-1]===s?u[u.length-1]=e.securityEvents[h]:(0===a&&l.push(s),u.push(e.securityEvents[h]),l.length>20&&0===a?(l.shift(),u.shift()):u.length>l.length&&u.shift())}}this.securityEventsChart.update("none")}}ngAfterViewInit(){setTimeout(()=>{this.initPopovers()},500),setTimeout(()=>{this.chartsInitialized||(console.log("Filtry se nena\u010detly v\u010das, inicializuji grafy s v\xfdchoz\xedmi hodnotami"),this.initChartsWithCorrectFilters())},3e3)}initPopovers(){const e={"dis-response":"Graf zobrazuje pr\u016fm\u011brnou dobu odezvy metod v \u010dase. Data jsou z\xedsk\xe1v\xe1na z tabulky PerformanceMetrics za zvolen\xe9 obdob\xed.","dis-calls":"Tato statistika zobrazuje celkov\xfd po\u010det vol\xe1n\xed DIS metod za posledn\xedch 24 hodin. Hodnota je vypo\u010d\xedt\xe1na jako suma hodnoty TotalCount ze v\u0161ech z\xe1znam\u016f v tabulce PerformanceMetrics za posledn\xedch 24 hodin.","weighted-avg":"Tato hodnota p\u0159edstavuje v\xe1\u017een\xfd pr\u016fm\u011br doby odezvy metod za posledn\xedch 24 hodin. V\xe1\u017een\xfd pr\u016fm\u011br je vypo\u010d\xedt\xe1n s ohledem na po\u010det nenulov\xfdch vol\xe1n\xed ka\u017ed\xe9 metody (NonZeroCount), co\u017e poskytuje p\u0159esn\u011bj\u0161\xed obraz o skute\u010dn\xe9 dob\u011b odezvy ne\u017e prost\xfd pr\u016fm\u011br.","certificate-usage":"Graf zobrazuje vyu\u017eit\xed certifik\xe1t\u016f podle typu operace. Zahrnuje operace jako ov\u011b\u0159en\xed, podpis, \u0161ifrov\xe1n\xed a de\u0161ifrov\xe1n\xed. Data jsou z\xedsk\xe1v\xe1na z tabulky CertificateUsageLogs za zvolen\xe9 obdob\xed.","security-events":"Graf zobrazuje po\u010det bezpe\u010dnostn\xedch ud\xe1lost\xed v \u010dase. Zahrnuje ud\xe1losti jako ne\xfasp\u011b\u0161n\xe9 pokusy o p\u0159ihl\xe1\u0161en\xed, podez\u0159el\xe9 aktivity a poru\u0161en\xed bezpe\u010dnosti. Data jsou z\xedsk\xe1v\xe1na z tabulky SecurityLogs za zvolen\xe9 obdob\xed.","instances-status":"Graf zobrazuje aktu\xe1ln\xed stav v\u0161ech instanc\xed v syst\xe9mu. Ukazuje po\u010det aktivn\xedch, neaktivn\xedch a probl\xe9mov\xfdch instanc\xed.","active-instances":"Tato statistika zobrazuje po\u010det aktu\xe1ln\u011b aktivn\xedch instanc\xed z celkov\xe9ho po\u010dtu. Instance je pova\u017eov\xe1na za aktivn\xed, pokud se p\u0159ipojila v posledn\xedch 24 hodin\xe1ch.","expiring-certificates":"Tato statistika zobrazuje po\u010det certifik\xe1t\u016f, kter\xfdm vypr\u0161\xed platnost v p\u0159\xed\u0161t\xedch 30 dnech. V\u010dasn\xe1 obnova certifik\xe1t\u016f je d\u016fle\u017eit\xe1 pro zaji\u0161t\u011bn\xed nep\u0159eru\u0161en\xe9ho provozu.","security-events-24h":"Tato statistika zobrazuje po\u010det bezpe\u010dnostn\xedch ud\xe1lost\xed za posledn\xedch 24 hodin. Zahrnuje tak\xe9 po\u010det aktivn\xedch upozorn\u011bn\xed, kter\xe1 vy\u017eaduj\xed pozornost."};document.querySelectorAll('[data-bs-toggle="popover"]').forEach(i=>{const s=bootstrap.Popover.getInstance(i);s&&s.dispose()}),document.querySelectorAll('[data-bs-toggle="popover"]').forEach(i=>{const s=i.getAttribute("data-help-type");if(s&&s in e)try{new bootstrap.Popover(i,{content:e[s],html:!0,trigger:"hover",placement:"top",container:"body"})}catch(a){console.error("Error initializing popover:",a)}else s&&console.warn("Help content not found for type:",s)})}};let n=r;return r.\u0275fac=function(i){return new(i||r)(t.Y36(b.W),t.Y36(C.C),t.Y36(S.e),t.Y36(z.M),t.Y36(E.i),t.Y36(x._))},r.\u0275cmp=t.Xpm({type:r,selectors:[["app-monitoring"]],viewQuery:function(i,s){if(1&i&&(t.Gf(I,5),t.Gf(k,5),t.Gf(Z,5)),2&i){let a;t.iGM(a=t.CRH())&&(s.certificateUsageChartRef=a.first),t.iGM(a=t.CRH())&&(s.securityEventsChartRef=a.first),t.iGM(a=t.CRH())&&(s.instancesStatusChartRef=a.first)}},decls:48,vars:17,consts:[[1,"container"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],["class","alert alert-danger alert-dismissible fade show","role","alert",4,"ngIf"],[3,"entityType","fields","showExportButton","exportButtonText","filterChange","exportData"],[1,"row","mb-4"],[1,"col-md-3","mb-3"],[1,"card","h-100"],[1,"card-body","text-center"],["data-bs-toggle","popover","data-help-type","active-instances",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0"],[1,"text-muted"],["data-bs-toggle","popover","data-help-type","dis-calls",1,"card-title",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","weighted-avg",2,"cursor","help"],["data-bs-toggle","popover","data-help-type","expiring-certificates",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0","text-warning"],["data-bs-toggle","popover","data-help-type","security-events-24h",1,"card-title",2,"cursor","help"],[1,"display-4","mb-0","text-danger"],["class","text-center my-5",4,"ngIf"],[4,"ngIf"],["role","alert",1,"alert","alert-danger","alert-dismissible","fade","show"],["type","button","aria-label","Close",1,"btn-close",3,"click"],[1,"text-center","my-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2"],["class","row",4,"ngIf"],[1,"row"],[1,"col-lg-6","mb-4"],[1,"card-header","d-flex","justify-content-between","align-items-center"],["data-bs-toggle","popover","data-help-type","instances-status",1,"mb-0",2,"cursor","help"],["title","Zobrazit graf na celou obrazovku",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-arrows-fullscreen"],[1,"card-body"],[1,"chart-container",2,"position","relative","height","300px"],["instancesStatusChart",""],["class","col-lg-6 mb-4",4,"ngIf"],["data-bs-toggle","popover","data-help-type","certificate-usage",1,"mb-0",2,"cursor","help"],["certificateUsageChart",""],["data-bs-toggle","popover","data-help-type","security-events",1,"mb-0",2,"cursor","help"],["securityEventsChart",""]],template:function(i,s){1&i&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h2"),t._uU(3,"Monitoring a metriky"),t.qZA()(),t.YNc(4,_,3,1,"div",2),t.TgZ(5,"app-advanced-filter",3),t.NdJ("filterChange",function(c){return s.onFilterChange(c)})("exportData",function(c){return s.onExportData(c)}),t.qZA(),t.TgZ(6,"div",4)(7,"div",5)(8,"div",6)(9,"div",7)(10,"h5",8),t._uU(11,"Aktivn\xed instance"),t.qZA(),t.TgZ(12,"p",9),t._uU(13),t.qZA(),t.TgZ(14,"p",10),t._uU(15),t.qZA()()()(),t.TgZ(16,"div",5)(17,"div",6)(18,"div",7)(19,"h5",11),t._uU(20,"Po\u010det vol\xe1n\xed DIS metod"),t.qZA(),t.TgZ(21,"p",9),t._uU(22),t.qZA(),t.TgZ(23,"p",10)(24,"span",12),t._uU(25,"Pr\u016fm\u011brn\xe1 odezva"),t.qZA(),t._uU(26),t.ALo(27,"number"),t.qZA()()()(),t.TgZ(28,"div",5)(29,"div",6)(30,"div",7)(31,"h5",13),t._uU(32,"Expiruj\xedc\xed certifik\xe1ty"),t.qZA(),t.TgZ(33,"p",14),t._uU(34),t.qZA(),t.TgZ(35,"p",10),t._uU(36,"V p\u0159\xed\u0161t\xedch 30 dnech"),t.qZA()()()(),t.TgZ(37,"div",5)(38,"div",6)(39,"div",7)(40,"h5",15),t._uU(41,"Bezpe\u010dnostn\xed ud\xe1losti (24h)"),t.qZA(),t.TgZ(42,"p",16),t._uU(43),t.qZA(),t.TgZ(44,"p",10),t._uU(45),t.qZA()()()()(),t.YNc(46,D,6,0,"div",17),t.YNc(47,M,14,1,"div",18),t.qZA()),2&i&&(t.xp6(4),t.Q6J("ngIf",s.error),t.xp6(1),t.Q6J("entityType","monitoring")("fields",s.filterFields)("showExportButton",!0)("exportButtonText","Export dat"),t.xp6(8),t.Oqu(s.systemStatistics.ActiveInstancesCount||0),t.xp6(2),t.hij("z celkov\xfdch ",s.systemStatistics.InstancesCount||0,""),t.xp6(7),t.Oqu(s.systemStatistics.TotalMethodCalls||0),t.xp6(4),t.hij(": ",t.xi3(27,14,s.systemStatistics.WeightedAvgResponseTime||0,"1.0-2")," ms"),t.xp6(8),t.Oqu(s.systemStatistics.ExpiringCertificatesCount||0),t.xp6(9),t.Oqu(s.systemStatistics.SecurityEventsLast24h||0),t.xp6(2),t.hij("Aktivn\xed upozorn\u011bn\xed: ",s.systemStatistics.ActiveAlertsCount||0,""),t.xp6(1),t.Q6J("ngIf",s.loading),t.xp6(1),t.Q6J("ngIf",!s.loading))},dependencies:[g.O5,T.W,g.JJ],styles:[".chart-container[_ngcontent-%COMP%]{position:relative;height:300px;width:100%}.card[_ngcontent-%COMP%]{box-shadow:0 .125rem .25rem #00000013;border-radius:.5rem;border:1px solid rgba(0,0,0,.125)}.card-header[_ngcontent-%COMP%]{background-color:#00000008;border-bottom:1px solid rgba(0,0,0,.125)}.display-4[_ngcontent-%COMP%]{font-size:2.5rem;font-weight:300;line-height:1.2}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.text-muted[_ngcontent-%COMP%]{color:#6c757d!important}.form-select[_ngcontent-%COMP%]:focus, .form-control[_ngcontent-%COMP%]:focus{border-color:#86b7fe;box-shadow:0 0 0 .25rem #0d6efd40}.btn-primary[_ngcontent-%COMP%]{background-color:#0d6efd;border-color:#0d6efd}.btn-primary[_ngcontent-%COMP%]:hover{background-color:#0b5ed7;border-color:#0a58ca}@media (max-width: 768px){.display-4[_ngcontent-%COMP%]{font-size:2rem}.card-title[_ngcontent-%COMP%]{font-size:1rem}}"]}),n})();var j=d(4466);let w=(()=>{const r=class{};let n=r;return r.\u0275fac=function(i){return new(i||r)},r.\u0275mod=t.oAB({type:r}),r.\u0275inj=t.cJS({imports:[g.ez,f.u5,f.UX,j.m,m.Bz.forChild([{path:"",component:F}])]}),n})()}}]);