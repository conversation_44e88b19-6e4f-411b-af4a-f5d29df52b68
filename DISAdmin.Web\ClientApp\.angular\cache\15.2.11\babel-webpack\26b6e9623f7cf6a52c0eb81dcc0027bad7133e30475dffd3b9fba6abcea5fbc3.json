{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/communication-pattern.service\";\nimport * as i2 from \"../../services/title.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../shared/collapsible-block/collapsible-block.component\";\nfunction CommunicationPatternsComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_9_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.error = null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction CommunicationPatternsComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 16)(2, \"span\", 17);\n    i0.ɵɵtext(3, \"Na\\u010D\\u00EDt\\u00E1m...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 18);\n    i0.ɵɵtext(5, \"Na\\u010D\\u00EDt\\u00E1m komunika\\u010Dn\\u00ED vzorce...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Testovat detekci\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Testov\\u00E1n\\u00ED...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Aktualizovat vzorce\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Aktualizuji...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CommunicationPatternsComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20)(2, \"h5\", 21);\n    i0.ɵɵelement(3, \"i\", 22);\n    i0.ɵɵtext(4, \" Konfigurace detekce v\\u00FDpadk\\u016F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\")(6, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_11_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.testDetection());\n    });\n    i0.ɵɵelement(7, \"i\", 24);\n    i0.ɵɵtemplate(8, CommunicationPatternsComponent_div_11_span_8_Template, 2, 0, \"span\", 25);\n    i0.ɵɵtemplate(9, CommunicationPatternsComponent_div_11_span_9_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_11_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.refreshPatterns());\n    });\n    i0.ɵɵelement(11, \"i\", 27);\n    i0.ɵɵtemplate(12, CommunicationPatternsComponent_div_11_span_12_Template, 2, 0, \"span\", 25);\n    i0.ɵɵtemplate(13, CommunicationPatternsComponent_div_11_span_13_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 28)(15, \"div\", 1)(16, \"div\", 29)(17, \"strong\", 30);\n    i0.ɵɵtext(18, \"Okno anal\\u00FDzy:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 29)(21, \"strong\", 31);\n    i0.ɵɵtext(22, \"Tolerance:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 29)(25, \"strong\", 32);\n    i0.ɵɵtext(26, \"Min. frekvence:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 29)(29, \"strong\", 33);\n    i0.ɵɵtext(30, \"Vylou\\u010Dit v\\u00EDkendy: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 34);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(33, \"div\", 35)(34, \"div\", 29)(35, \"strong\", 36);\n    i0.ɵɵtext(36, \"Min. interval alert\\u016F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 29)(39, \"strong\", 37);\n    i0.ɵɵtext(40, \"Aktualizace vzorc\\u016F:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 29)(43, \"strong\", 38);\n    i0.ɵɵtext(44, \"Interval kontrol:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 29)(47, \"strong\", 39);\n    i0.ɵɵtext(48, \"Min. vol\\u00E1n\\u00ED pro anal\\u00FDzu:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"spinner-border\", ctx_r2.testing)(\"spinner-border-sm\", ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.testing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.refreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"spinner-border\", ctx_r2.refreshing)(\"spinner-border-sm\", ctx_r2.refreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.refreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.refreshing);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.analysisWindowDays, \" dn\\u00ED \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.toleranceMinutes, \" minut \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.minimumFrequencyPerHour, \"/hod \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r2.config.excludeWeekends ? \"bg-success\" : \"bg-secondary\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.excludeWeekends ? \"Ano\" : \"Ne\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.minimumAlertInterval, \" min \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.patternUpdateIntervalHours, \" hod \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.checkIntervalMinutes, \" min \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.config.minimumCallsForAnalysis, \" \");\n  }\n}\nfunction CommunicationPatternsComponent_app_collapsible_block_12_tr_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\", 44)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 48)(11, \"span\", 34);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 48)(14, \"small\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 49)(17, \"strong\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\", 50);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\", 50);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"td\", 51);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"td\", 44);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"td\", 51)(28, \"span\", 34);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.customerAbbreviation);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r15.getStatusBadgeClass(item_r17.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getStatusText(item_r17.status), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r15.getDetectionLogicTooltip(item_r17.pattern));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r15.getDetectionLogicBadgeClass(item_r17.pattern.detectionLogicType));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.getDetectionLogicShortText(item_r17.pattern.detectionLogicType), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"title\", ctx_r15.getActiveHoursTooltip(item_r17.pattern));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r15.formatActiveHours(item_r17.pattern));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r17.pattern.totalCallsAnalyzed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatNumber(item_r17.pattern.averageIntervalMinutes), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatNumber(item_r17.pattern.standardDeviation), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r15.formatNumber(item_r17.pattern.minIntervalMinutes), \" / \", ctx_r15.formatNumber(item_r17.pattern.maxIntervalMinutes), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r15.formatDate(item_r17.lastConnectionDate), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(item_r17.pattern.hasSufficientData ? \"bg-success\" : \"bg-warning\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", item_r17.pattern.hasSufficientData ? \"Ano\" : \"Ne\", \" \");\n  }\n}\nfunction CommunicationPatternsComponent_app_collapsible_block_12_tr_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 52);\n    i0.ɵɵelement(2, \"i\", 53);\n    i0.ɵɵelementStart(3, \"p\", 18);\n    i0.ɵɵtext(4, \"\\u017D\\u00E1dn\\u00E9 komunika\\u010Dn\\u00ED vzorce nebyly nalezeny\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CommunicationPatternsComponent_app_collapsible_block_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-collapsible-block\", 40)(1, \"div\", 41)(2, \"table\", 42)(3, \"thead\", 43)(4, \"tr\")(5, \"th\", 44);\n    i0.ɵɵtext(6, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\", 44);\n    i0.ɵɵtext(8, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\", 44);\n    i0.ɵɵtext(10, \"Stav\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"th\", 44);\n    i0.ɵɵtext(12, \"Typ detekce\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"th\", 44);\n    i0.ɵɵtext(14, \"Aktivn\\u00ED hodiny\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"th\", 45);\n    i0.ɵɵtext(16, \"Vol\\u00E1n\\u00ED za 30 dn\\u00ED\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"th\", 44);\n    i0.ɵɵtext(18, \"Pr\\u016Fm\\u011Br (min)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"th\", 44);\n    i0.ɵɵtext(20, \"Std. odchylka\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"th\", 44);\n    i0.ɵɵtext(22, \"Min/max (min)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"th\", 46);\n    i0.ɵɵtext(24, \"Posl. kontakt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"th\", 44);\n    i0.ɵɵtext(26, \"Dostatek dat\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"tbody\");\n    i0.ɵɵtemplate(28, CommunicationPatternsComponent_app_collapsible_block_12_tr_28_Template, 30, 20, \"tr\", 47);\n    i0.ɵɵtemplate(29, CommunicationPatternsComponent_app_collapsible_block_12_tr_29_Template, 5, 0, \"tr\", 25);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"showRecordCount\", true)(\"recordCount\", ctx_r3.patterns.length)(\"defaultExpanded\", true);\n    i0.ɵɵadvance(28);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.patterns);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.patterns.length === 0);\n  }\n}\nfunction CommunicationPatternsComponent_div_13_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"i\", 63);\n    i0.ɵɵelementStart(2, \"p\", 18);\n    i0.ɵɵtext(3, \"\\u017D\\u00E1dn\\u00E9 v\\u00FDpadky komunikace nebyly detekov\\u00E1ny\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CommunicationPatternsComponent_div_13_div_10_tr_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const outage_r21 = ctx.$implicit;\n    const ctx_r20 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(outage_r21.instanceName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(outage_r21.customerAbbreviation);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.formatTimeSpan(outage_r21.timeSinceLastContact));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.formatTimeSpan(outage_r21.expectedMaxInterval));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r20.formatDate(outage_r21.lastContactTime));\n  }\n}\nfunction CommunicationPatternsComponent_div_13_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 64);\n    i0.ɵɵelement(2, \"i\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"table\", 65)(6, \"thead\")(7, \"tr\")(8, \"th\");\n    i0.ɵɵtext(9, \"Instance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Z\\u00E1kazn\\u00EDk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"\\u010Cas od kontaktu\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"O\\u010Dek\\u00E1van\\u00FD max\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"th\");\n    i0.ɵɵtext(17, \"Posledn\\u00ED kontakt\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"tbody\");\n    i0.ɵɵtemplate(19, CommunicationPatternsComponent_div_13_div_10_tr_19_Template, 11, 5, \"tr\", 47);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Detekov\\u00E1no \", ctx_r19.testResults.length, \" v\\u00FDpadk\\u016F komunikace \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r19.testResults);\n  }\n}\nfunction CommunicationPatternsComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55)(2, \"div\", 56)(3, \"div\", 57)(4, \"h5\", 58);\n    i0.ɵɵelement(5, \"i\", 59);\n    i0.ɵɵtext(6, \" V\\u00FDsledky testov\\u00E1n\\u00ED detekce v\\u00FDpadk\\u016F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_13_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.closeTestResults());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 60);\n    i0.ɵɵtemplate(9, CommunicationPatternsComponent_div_13_div_9_Template, 4, 0, \"div\", 7);\n    i0.ɵɵtemplate(10, CommunicationPatternsComponent_div_13_div_10_Template, 20, 2, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 61)(12, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function CommunicationPatternsComponent_div_13_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.closeTestResults());\n    });\n    i0.ɵɵtext(13, \"Zav\\u0159\\u00EDt\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r4.showTestResults ? \"block\" : \"none\");\n    i0.ɵɵclassProp(\"show\", ctx_r4.showTestResults);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.testResults.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.testResults.length > 0);\n  }\n}\nfunction CommunicationPatternsComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 66);\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"show\", ctx_r5.showTestResults);\n  }\n}\nexport class CommunicationPatternsComponent {\n  constructor(communicationPatternService, titleService) {\n    this.communicationPatternService = communicationPatternService;\n    this.titleService = titleService;\n    this.patterns = [];\n    this.config = null;\n    this.testResults = [];\n    this.loading = false;\n    this.refreshing = false;\n    this.testing = false;\n    this.error = null;\n    this.showTestResults = false;\n  }\n  ngOnInit() {\n    this.titleService.setTitle('Komunikační vzorce');\n    this.loadData();\n  }\n  ngAfterViewInit() {\n    // Inicializace popoverů s zpožděním, aby se zajistilo, že DOM je plně načten\n    setTimeout(() => {\n      this.initPopovers();\n    }, 500);\n  }\n  ngOnDestroy() {\n    // Zničení všech popoverů\n    this.destroyPopovers();\n  }\n  loadData() {\n    this.loading = true;\n    this.error = null;\n    // Načtení vzorců\n    this.communicationPatternService.getAllPatterns().subscribe({\n      next: response => {\n        if (response.success) {\n          this.patterns = response.data;\n        } else {\n          this.error = response.message || 'Chyba při načítání vzorců';\n        }\n      },\n      error: error => {\n        this.error = 'Chyba při načítání vzorců: ' + (error.message || error);\n        console.error('Chyba při načítání vzorců:', error);\n      }\n    });\n    // Načtení konfigurace\n    this.communicationPatternService.getConfiguration().subscribe({\n      next: response => {\n        if (response.success) {\n          this.config = response.data;\n        }\n        this.loading = false;\n        // Reinicializace popoverů po načtení dat\n        setTimeout(() => {\n          this.initPopovers();\n        }, 100);\n      },\n      error: error => {\n        console.error('Chyba při načítání konfigurace:', error);\n        this.loading = false;\n      }\n    });\n  }\n  refreshPatterns() {\n    this.refreshing = true;\n    this.error = null;\n    this.communicationPatternService.refreshPatterns().subscribe({\n      next: response => {\n        if (response.success) {\n          // Po úspěšné aktualizaci znovu načti data\n          this.loadData();\n        } else {\n          this.error = response.message || 'Chyba při aktualizaci vzorců';\n        }\n        this.refreshing = false;\n      },\n      error: error => {\n        this.error = 'Chyba při aktualizaci vzorců: ' + (error.message || error);\n        this.refreshing = false;\n        console.error('Chyba při aktualizaci vzorců:', error);\n      }\n    });\n  }\n  testDetection() {\n    this.testing = true;\n    this.error = null;\n    this.testResults = [];\n    this.communicationPatternService.testDetection().subscribe({\n      next: response => {\n        if (response.success) {\n          this.testResults = response.data;\n          this.showTestResults = true;\n        } else {\n          this.error = response.message || 'Chyba při testování detekce';\n        }\n        this.testing = false;\n      },\n      error: error => {\n        this.error = 'Chyba při testování detekce: ' + (error.message || error);\n        this.testing = false;\n        console.error('Chyba při testování detekce:', error);\n      }\n    });\n  }\n  getStatusBadgeClass(status) {\n    switch (status.toLowerCase()) {\n      case 'active':\n        return 'badge bg-success';\n      case 'inactive':\n        return 'badge bg-secondary';\n      case 'blocked':\n        return 'badge bg-danger';\n      default:\n        return 'badge bg-secondary';\n    }\n  }\n  getStatusText(status) {\n    switch (status.toLowerCase()) {\n      case 'active':\n        return 'Aktivní';\n      case 'inactive':\n        return 'Neaktivní';\n      case 'blocked':\n        return 'Blokovaná';\n      default:\n        return status;\n    }\n  }\n  formatTimeSpan(timeSpan) {\n    // Převod TimeSpan stringu na čitelný formát\n    const parts = timeSpan.split(':');\n    if (parts.length >= 2) {\n      const hours = parseInt(parts[0]);\n      const minutes = parseInt(parts[1]);\n      if (hours > 0) {\n        return `${hours}h ${minutes}m`;\n      } else {\n        return `${minutes}m`;\n      }\n    }\n    return timeSpan;\n  }\n  formatDate(date) {\n    if (!date) return 'Nikdy';\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return d.toLocaleString('cs-CZ');\n  }\n  formatNumber(num) {\n    return num.toFixed(1);\n  }\n  formatActiveHours(pattern) {\n    if (!pattern.activeHours) {\n      return 'Neanalyzováno';\n    }\n    const ah = pattern.activeHours;\n    const confidence = (ah.confidence * 100).toFixed(0);\n    return `${ah.startHour}:00-${ah.endHour}:00 (${confidence}% spolehlivost)`;\n  }\n  getActiveHoursTooltip(pattern) {\n    if (!pattern.activeHours) {\n      return 'Aktivní hodiny nebyly analyzovány - nedostatek historických dat';\n    }\n    const ah = pattern.activeHours;\n    const days = ah.activeDays?.map(day => this.getDayName(day)).join(', ') || 'Žádné dny';\n    return `Aktivní dny: ${days}\\nSpolehlivost: ${(ah.confidence * 100).toFixed(1)}%`;\n  }\n  /**\r\n   * Převod čísla dne na český název\r\n   */\n  getDayName(dayNumber) {\n    const dayNames = ['Ne', 'Po', 'Út', 'St', 'Čt', 'Pá', 'So'];\n    return dayNames[dayNumber] || dayNumber.toString();\n  }\n  getDetectionLogicTooltip(pattern) {\n    return pattern.detectionLogicDescription || 'Informace o detekční logice nejsou k dispozici';\n  }\n  getDetectionLogicShortText(detectionType) {\n    switch (detectionType) {\n      case 'Nastavené pracovní hodiny':\n        return 'Nastavené';\n      case 'Historická analýza':\n        return 'Historická';\n      case 'Nepřetržitý monitoring':\n        return 'Nepřetržitý';\n      default:\n        return 'Neznámý';\n    }\n  }\n  getDetectionLogicBadgeClass(detectionType) {\n    switch (detectionType) {\n      case 'Nastavené pracovní hodiny':\n        return 'bg-success';\n      case 'Historická analýza':\n        return 'bg-info';\n      case 'Nepřetržitý monitoring':\n        return 'bg-warning';\n      default:\n        return 'bg-secondary';\n    }\n  }\n  closeTestResults() {\n    this.showTestResults = false;\n    this.testResults = [];\n  }\n  /**\r\n   * Inicializace popoverů pro nápovědu\r\n   */\n  initPopovers() {\n    // Definice obsahu nápověd\n    const helpContent = {\n      'analysis-window': 'Počet dní zpětně, ze kterých se analyzují komunikační vzorce instancí. ' + 'Delší období poskytuje přesnější analýzu, ale vyžaduje více historických dat.',\n      'tolerance': 'Tolerance v minutách pro detekci výpadků komunikace. ' + 'Pokud instance nekomunikuje déle než očekávaný interval + tolerance, je detekován výpadek.',\n      'min-frequency': 'Minimální frekvence komunikace za hodinu potřebná pro analýzu vzorce. ' + 'Instance s nižší frekvencí nebudou analyzovány.',\n      'exclude-weekends': 'Určuje, zda se mají víkendy vyloučit z analýzy komunikačních vzorců. ' + 'Užitečné pro instance, které nekomunikují o víkendech.',\n      'min-alert-interval': 'Minimální interval mezi alerty pro stejnou instanci v minutách. ' + 'Zabraňuje spamování alertů při opakovaných výpadcích.',\n      'pattern-update-interval': 'Interval v hodinách, jak často se aktualizují komunikační vzorce. ' + 'Kratší interval znamená aktuálnější vzorce, ale vyšší zátěž systému.',\n      'check-interval': 'Interval v minutách, jak často se kontrolují výpadky komunikace. ' + 'Kratší interval znamená rychlejší detekci výpadků.',\n      'min-calls-analysis': 'Minimální počet volání potřebný pro analýzu komunikačního vzorce instance. ' + 'Instance s menším počtem volání nebudou analyzovány.'\n    };\n    // Nejprve zrušíme všechny existující popovery\n    this.destroyPopovers();\n    // Inicializace popoverů pomocí Bootstrap API\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const helpType = el.getAttribute('data-help-type');\n      if (helpType && helpType in helpContent) {\n        try {\n          new bootstrap.Popover(el, {\n            content: helpContent[helpType],\n            html: true,\n            trigger: 'hover focus',\n            placement: 'top',\n            container: 'body',\n            sanitize: false\n          });\n        } catch (error) {\n          console.error('Chyba při inicializaci popoveru:', error);\n        }\n      } else if (helpType) {\n        console.warn('Nápověda nenalezena pro typ:', helpType);\n      }\n    });\n  }\n  /**\r\n   * Zničení všech popoverů\r\n   */\n  destroyPopovers() {\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popover = bootstrap.Popover.getInstance(el);\n      if (popover) {\n        popover.dispose();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function CommunicationPatternsComponent_Factory(t) {\n      return new (t || CommunicationPatternsComponent)(i0.ɵɵdirectiveInject(i1.CommunicationPatternService), i0.ɵɵdirectiveInject(i2.TitleService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CommunicationPatternsComponent,\n      selectors: [[\"app-communication-patterns\"]],\n      decls: 15,\n      vars: 6,\n      consts: [[1, \"container-fluid\"], [1, \"row\"], [1, \"col-12\"], [1, \"mb-4\"], [1, \"bi\", \"bi-diagram-3-fill\", \"me-2\"], [1, \"text-muted\", \"mb-0\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"card mb-4\", 4, \"ngIf\"], [\"title\", \"Komunika\\u010Dn\\u00ED vzorce\", \"icon\", \"bi-table\", \"storageKey\", \"collapsible_block_communication_patterns\", \"marginTop\", \"\", \"marginBottom\", \"mb-4\", 3, \"showRecordCount\", \"recordCount\", \"defaultExpanded\", 4, \"ngIf\"], [\"class\", \"modal fade\", \"tabindex\", \"-1\", \"role\", \"dialog\", 3, \"show\", \"display\", 4, \"ngIf\"], [\"class\", \"modal-backdrop fade\", 3, \"show\", 4, \"ngIf\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [1, \"bi\", \"bi-exclamation-triangle-fill\", \"me-2\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"bg-light\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"bi\", \"bi-gear-fill\", \"me-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-play-circle\"], [4, \"ngIf\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"disabled\", \"click\"], [1, \"bi\", \"bi-arrow-clockwise\"], [1, \"card-body\"], [1, \"col-md-3\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"analysis-window\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"tolerance\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"min-frequency\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"exclude-weekends\", 2, \"cursor\", \"help\"], [1, \"badge\"], [1, \"row\", \"mt-2\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"min-alert-interval\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"pattern-update-interval\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"check-interval\", 2, \"cursor\", \"help\"], [\"data-bs-toggle\", \"popover\", \"data-help-type\", \"min-calls-analysis\", 2, \"cursor\", \"help\"], [\"title\", \"Komunika\\u010Dn\\u00ED vzorce\", \"icon\", \"bi-table\", \"storageKey\", \"collapsible_block_communication_patterns\", \"marginTop\", \"\", \"marginBottom\", \"mb-4\", 3, \"showRecordCount\", \"recordCount\", \"defaultExpanded\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [1, \"table-light\"], [1, \"text-nowrap\"], [1, \"text-nowrap\", 2, \"width\", \"120px\"], [1, \"text-nowrap\", 2, \"width\", \"140px\"], [4, \"ngFor\", \"ngForOf\"], [3, \"title\"], [1, \"text-center\", \"text-nowrap\"], [1, \"text-end\"], [1, \"text-center\"], [\"colspan\", \"11\", 1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"bi\", \"bi-inbox\", \"fs-1\"], [\"tabindex\", \"-1\", \"role\", \"dialog\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [1, \"bi\", \"bi-bug-fill\", \"me-2\"], [1, \"modal-body\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"bi\", \"bi-check-circle-fill\", \"text-success\", \"fs-1\"], [1, \"alert\", \"alert-warning\"], [1, \"table\", \"table-sm\"], [1, \"modal-backdrop\", \"fade\"]],\n      template: function CommunicationPatternsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h2\");\n          i0.ɵɵelement(5, \"i\", 4);\n          i0.ɵɵtext(6, \" Komunika\\u010Dn\\u00ED vzorce instanc\\u00ED \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 5);\n          i0.ɵɵtext(8, \" Anal\\u00FDza historick\\u00E9ho chov\\u00E1n\\u00ED komunikace instanc\\u00ED pro inteligentn\\u00ED detekci v\\u00FDpadk\\u016F. Instance bez nastaven\\u00FDch pracovn\\u00EDch hodin pou\\u017E\\u00EDvaj\\u00ED automaticky analyzovan\\u00E9 aktivn\\u00ED hodiny. \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(9, CommunicationPatternsComponent_div_9_Template, 4, 1, \"div\", 6);\n          i0.ɵɵtemplate(10, CommunicationPatternsComponent_div_10_Template, 6, 0, \"div\", 7);\n          i0.ɵɵtemplate(11, CommunicationPatternsComponent_div_11_Template, 50, 24, \"div\", 8);\n          i0.ɵɵtemplate(12, CommunicationPatternsComponent_app_collapsible_block_12_Template, 30, 5, \"app-collapsible-block\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(13, CommunicationPatternsComponent_div_13_Template, 14, 6, \"div\", 10);\n          i0.ɵɵtemplate(14, CommunicationPatternsComponent_div_14_Template, 1, 2, \"div\", 11);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.config && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showTestResults);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showTestResults);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.CollapsibleBlockComponent],\n      styles: [\".card-header[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa !important;\\n  border-bottom: 1px solid #dee2e6;\\n}\\n\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  letter-spacing: 0.5px;\\n  color: #495057;\\n}\\n\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  vertical-align: middle;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n.spinner-border-sm[_ngcontent-%COMP%] {\\n  width: 1rem;\\n  height: 1rem;\\n}\\n\\n.modal-backdrop[_ngcontent-%COMP%] {\\n  background-color: rgba(0, 0, 0, 0.5);\\n}\\n\\n.text-end[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.text-center[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n\\n[data-bs-theme=\\\"dark\\\"][_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background-color: #343a40 !important;\\n  border-bottom: 1px solid #495057;\\n}\\n\\n[data-bs-theme=\\\"dark\\\"][_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  color: #adb5bd;\\n}\\n\\n[data-bs-theme=\\\"dark\\\"][_ngcontent-%COMP%]   .modal-backdrop[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYWRtaW4vY29tbXVuaWNhdGlvbi1wYXR0ZXJucy9jb21tdW5pY2F0aW9uLXBhdHRlcm5zLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSxvQ0FBb0M7RUFDcEMsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLG1CQUFtQjtFQUNuQixxQkFBcUI7RUFDckIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0FBQ2Q7O0FBRUE7RUFDRSxvQ0FBb0M7QUFDdEM7O0FBRUE7RUFDRSxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxrQkFBa0I7QUFDcEI7O0FBRUEsc0JBQXNCO0FBQ3RCO0VBQ0Usb0NBQW9DO0VBQ3BDLGdDQUFnQztBQUNsQzs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSwwQ0FBMEM7QUFDNUMiLCJzb3VyY2VzQ29udGVudCI6WyIuY2FyZC1oZWFkZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhICFpbXBvcnRhbnQ7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZGVlMmU2O1xufVxuXG4udGFibGUgdGgge1xuICBmb250LXdlaWdodDogNjAwO1xuICBmb250LXNpemU6IDAuODc1cmVtO1xuICBsZXR0ZXItc3BhY2luZzogMC41cHg7XG4gIGNvbG9yOiAjNDk1MDU3O1xufVxuXG4udGFibGUgdGQge1xuICB2ZXJ0aWNhbC1hbGlnbjogbWlkZGxlO1xufVxuXG4uYmFkZ2Uge1xuICBmb250LXNpemU6IDAuNzVyZW07XG59XG5cbi5zcGlubmVyLWJvcmRlci1zbSB7XG4gIHdpZHRoOiAxcmVtO1xuICBoZWlnaHQ6IDFyZW07XG59XG5cbi5tb2RhbC1iYWNrZHJvcCB7XG4gIGJhY2tncm91bmQtY29sb3I6IHJnYmEoMCwgMCwgMCwgMC41KTtcbn1cblxuLnRleHQtZW5kIHtcbiAgdGV4dC1hbGlnbjogcmlnaHQ7XG59XG5cbi50ZXh0LWNlbnRlciB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLyogRGFyayBtb2RlIHN1cHBvcnQgKi9cbltkYXRhLWJzLXRoZW1lPVwiZGFya1wiXSAuY2FyZC1oZWFkZXIge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiAjMzQzYTQwICFpbXBvcnRhbnQ7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjNDk1MDU3O1xufVxuXG5bZGF0YS1icy10aGVtZT1cImRhcmtcIl0gLnRhYmxlIHRoIHtcbiAgY29sb3I6ICNhZGI1YmQ7XG59XG5cbltkYXRhLWJzLXRoZW1lPVwiZGFya1wiXSAubW9kYWwtYmFja2Ryb3Age1xuICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "mappings": ";;;;;;;;IAeMA,+BAAuF;IACrFA,wBAAoD;IACpDA,YACA;IAAAA,kCAA+D;IAAvBA;MAAAA;MAAA;MAAA,qCAAiB,IAAI;IAAA,EAAC;IAACA,iBAAS;;;;IADxEA,eACA;IADAA,6CACA;;;;;IAIFA,+BAA8C;IAEZA,yCAAU;IAAAA,iBAAO;IAEjDA,6BAAgB;IAAAA,sEAA6B;IAAAA,iBAAI;;;;;IAe3CA,4BAAuB;IAACA,iCAAgB;IAAAA,iBAAO;;;;;IAC/CA,4BAAsB;IAACA,uCAAY;IAAAA,iBAAO;;;;;IAM1CA,4BAA0B;IAACA,oCAAmB;IAAAA,iBAAO;;;;;IACrDA,4BAAyB;IAACA,+BAAc;IAAAA,iBAAO;;;;;;IAnBvDA,+BAAkD;IAG5CA,wBAAoC;IACpCA,uDACF;IAAAA,iBAAK;IACLA,2BAAK;IAEKA;MAAAA;MAAA;MAAA,OAASA,sCAAe;IAAA,EAAC;IAE/BA,wBAAsG;IACtGA,yFAA+C;IAC/CA,yFAA0C;IAC5CA,iBAAS;IACTA,mCAEgC;IADxBA;MAAAA;MAAA;MAAA,OAASA,wCAAiB;IAAA,EAAC;IAEjCA,yBAAgH;IAChHA,2FAAqD;IACrDA,2FAA+C;IACjDA,iBAAS;IAGbA,gCAAuB;IAGuEA,mCAAa;IAAAA,iBAAS;IAACA,aACjH;IAAAA,iBAAM;IACNA,gCAAsB;IAC8DA,2BAAU;IAAAA,iBAAS;IAACA,aACxG;IAAAA,iBAAM;IACNA,gCAAsB;IACkEA,gCAAe;IAAAA,iBAAS;IAACA,aACjH;IAAAA,iBAAM;IACNA,gCAAsB;IACqEA,6CAAkB;IAAAA,iBAAS;IACpHA,iCAAqF;IACnFA,aACF;IAAAA,iBAAO;IAGXA,gCAAsB;IAEyEA,2CAAqB;IAAAA,iBAAS;IAACA,aAC5H;IAAAA,iBAAM;IACNA,gCAAsB;IAC4EA,yCAAmB;IAAAA,iBAAS;IAACA,aAC/H;IAAAA,iBAAM;IACNA,gCAAsB;IACmEA,kCAAiB;IAAAA,iBAAS;IAACA,aACpH;IAAAA,iBAAM;IACNA,gCAAsB;IACuEA,wDAAwB;IAAAA,iBAAS;IAACA,aAC/H;IAAAA,iBAAM;;;;IA5CEA,eAAoB;IAApBA,yCAAoB;IACGA,eAAgC;IAAhCA,gDAAgC;IACtDA,eAAc;IAAdA,sCAAc;IACdA,eAAa;IAAbA,qCAAa;IAIdA,eAAuB;IAAvBA,4CAAuB;IACIA,eAAmC;IAAnCA,mDAAmC;IAC7DA,eAAiB;IAAjBA,yCAAiB;IACjBA,eAAgB;IAAhBA,wCAAgB;IAOwFA,eACjH;IADiHA,0EACjH;IAEwGA,eACxG;IADwGA,qEACxG;IAEiHA,eACjH;IADiHA,0EACjH;IAGsBA,eAAgE;IAAhEA,4EAAgE;IAClFA,eACF;IADEA,6EACF;IAK0HA,eAC5H;IAD4HA,uEAC5H;IAE+HA,eAC/H;IAD+HA,6EAC/H;IAEoHA,eACpH;IADoHA,uEACpH;IAE+HA,eAC/H;IAD+HA,sEAC/H;;;;;IAiCEA,0BAAkC;IAEtBA,YAA+B;IAAAA,iBAAS;IAElDA,8BAAwB;IACdA,YAAuB;IAAAA,iBAAS;IAE1CA,0BAAI;IAEAA,YACF;IAAAA,iBAAO;IAETA,+BAAqD;IAEjDA,aACF;IAAAA,iBAAO;IAETA,+BAAkD;IACzCA,aAAqC;IAAAA,iBAAQ;IAEtDA,+BAAoC;IAC1BA,aAAqC;IAAAA,iBAAS;IAExDA,+BAAqB;IACnBA,aACF;IAAAA,iBAAK;IACLA,+BAAqB;IACnBA,aACF;IAAAA,iBAAK;IACLA,+BAAwB;IACtBA,aAEF;IAAAA,iBAAK;IACLA,+BAAwB;IACtBA,aACF;IAAAA,iBAAK;IACLA,+BAAwB;IAEpBA,aACF;IAAAA,iBAAO;;;;;IArCCA,eAA+B;IAA/BA,mDAA+B;IAG/BA,eAAuB;IAAvBA,2CAAuB;IAGzBA,eAA0C;IAA1CA,2DAA0C;IAC9CA,eACF;IADEA,uEACF;IAEEA,eAAgD;IAAhDA,0EAAgD;IAC9BA,eAAsE;IAAtEA,uFAAsE;IACxFA,eACF;IADEA,wGACF;IAEEA,eAA6C;IAA7CA,uEAA6C;IACxCA,eAAqC;IAArCA,iEAAqC;IAGpCA,eAAqC;IAArCA,yDAAqC;IAG7CA,eACF;IADEA,8FACF;IAEEA,eACF;IADEA,yFACF;IAEEA,eAEF;IAFEA,4JAEF;IAEEA,eACF;IADEA,gFACF;IAEsBA,eAAsE;IAAtEA,+EAAsE;IACxFA,eACF;IADEA,kFACF;;;;;IAGJA,0BAAkC;IAE9BA,wBAAgC;IAChCA,6BAAgB;IAAAA,iFAAwC;IAAAA,iBAAI;;;;;IAxExEA,iDAQ2C;IAKTA,kCAAQ;IAAAA,iBAAK;IACrCA,8BAAwB;IAAAA,wBAAQ;IAAAA,iBAAK;IACrCA,8BAAwB;IAAAA,qBAAI;IAAAA,iBAAK;IACjCA,+BAAwB;IAAAA,4BAAW;IAAAA,iBAAK;IACxCA,+BAAwB;IAAAA,oCAAc;IAAAA,iBAAK;IAC3CA,+BAA8C;IAAAA,gDAAgB;IAAAA,iBAAK;IACnEA,+BAAwB;IAAAA,uCAAY;IAAAA,iBAAK;IACzCA,+BAAwB;IAAAA,8BAAa;IAAAA,iBAAK;IAC1CA,+BAAwB;IAAAA,8BAAa;IAAAA,iBAAK;IAC1CA,+BAA8C;IAAAA,8BAAa;IAAAA,iBAAK;IAChEA,+BAAwB;IAAAA,6BAAY;IAAAA,iBAAK;IAG7CA,8BAAO;IACLA,2GAyCK;IACLA,yGAKK;IACPA,iBAAQ;;;;IAxESA,sCAAwB;IAwBlBA,gBAAW;IAAXA,yCAAW;IA0C3BA,eAA2B;IAA3BA,mDAA2B;;;;;IA2BtCA,+BAA+D;IAC7DA,wBAAyD;IACzDA,6BAAgB;IAAAA,mFAA0C;IAAAA,iBAAI;;;;;IAmBxDA,0BAAuC;IACjCA,YAAyB;IAAAA,iBAAK;IAClCA,0BAAI;IAAAA,YAAiC;IAAAA,iBAAK;IAC1CA,0BAAI;IAAAA,YAAiD;IAAAA,iBAAK;IAC1DA,0BAAI;IAAAA,YAAgD;IAAAA,iBAAK;IACzDA,0BAAI;IAAAA,aAAwC;IAAAA,iBAAK;;;;;IAJ7CA,eAAyB;IAAzBA,6CAAyB;IACzBA,eAAiC;IAAjCA,qDAAiC;IACjCA,eAAiD;IAAjDA,6EAAiD;IACjDA,eAAgD;IAAhDA,4EAAgD;IAChDA,eAAwC;IAAxCA,oEAAwC;;;;;IAtBtDA,2BAAoC;IAEhCA,wBAAoD;IACpDA,YACF;IAAAA,iBAAM;IACNA,+BAA8B;IAIlBA,wBAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,mCAAQ;IAAAA,iBAAK;IACjBA,2BAAI;IAAAA,qCAAe;IAAAA,iBAAK;IACxBA,2BAAI;IAAAA,6CAAa;IAAAA,iBAAK;IACtBA,2BAAI;IAAAA,sCAAgB;IAAAA,iBAAK;IAG7BA,8BAAO;IACLA,+FAMK;IACPA,iBAAQ;;;;IArBVA,eACF;IADEA,wGACF;IAa6BA,gBAAc;IAAdA,6CAAc;;;;;;IAjCrDA,+BACyD;IAK/CA,wBAAmC;IACnCA,6EACF;IAAAA,iBAAK;IACLA,kCAAqE;IAA7BA;MAAAA;MAAA;MAAA,OAASA,yCAAkB;IAAA,EAAC;IAACA,iBAAS;IAEhFA,+BAAwB;IACtBA,sFAGM;IACNA,0FA2BM;IACRA,iBAAM;IACNA,gCAA0B;IACwBA;MAAAA;MAAA;MAAA,OAASA,yCAAkB;IAAA,EAAC;IAACA,iCAAM;IAAAA,iBAAS;;;;IA9C7CA,oEAAoD;IAAnFA,8CAA8B;IAYxCA,eAA8B;IAA9BA,sDAA8B;IAI9BA,eAA4B;IAA5BA,oDAA4B;;;;;IAmC1CA,0BAA8F;;;;IAA7DA,8CAA8B;;;ACpN/D,OAAM,MAAOC,8BAA8B;EAUzCC,YACUC,2BAAwD,EACxDC,YAA0B;IAD1B,gCAA2B,GAA3BD,2BAA2B;IAC3B,iBAAY,GAAZC,YAAY;IAXtB,aAAQ,GAA+B,EAAE;IACzC,WAAM,GAAqC,IAAI;IAC/C,gBAAW,GAA0B,EAAE;IACvC,YAAO,GAAG,KAAK;IACf,eAAU,GAAG,KAAK;IAClB,YAAO,GAAG,KAAK;IACf,UAAK,GAAkB,IAAI;IAC3B,oBAAe,GAAG,KAAK;EAKnB;EAEJC,QAAQ;IACN,IAAI,CAACD,YAAY,CAACE,QAAQ,CAAC,oBAAoB,CAAC;IAChD,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,eAAe;IACb;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAW;IACT;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQL,QAAQ;IACd,IAAI,CAACM,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,IAAI;IAEjB;IACA,IAAI,CAACX,2BAA2B,CAACY,cAAc,EAAE,CAACC,SAAS,CAAC;MAC1DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACC,QAAQ,GAAGF,QAAQ,CAACG,IAAI;SAC9B,MAAM;UACL,IAAI,CAACP,KAAK,GAAGI,QAAQ,CAACI,OAAO,IAAI,2BAA2B;;MAEhE,CAAC;MACDR,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,6BAA6B,IAAIA,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAAC;QACrES,OAAO,CAACT,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACpD;KACD,CAAC;IAEF;IACA,IAAI,CAACX,2BAA2B,CAACqB,gBAAgB,EAAE,CAACR,SAAS,CAAC;MAC5DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACM,MAAM,GAAGP,QAAQ,CAACG,IAAI;;QAE7B,IAAI,CAACR,OAAO,GAAG,KAAK;QAEpB;QACAJ,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,YAAY,EAAE;QACrB,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDI,KAAK,EAAGA,KAAK,IAAI;QACfS,OAAO,CAACT,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,IAAI,CAACD,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEAa,eAAe;IACb,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACb,KAAK,GAAG,IAAI;IAEjB,IAAI,CAACX,2BAA2B,CAACuB,eAAe,EAAE,CAACV,SAAS,CAAC;MAC3DC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB;UACA,IAAI,CAACZ,QAAQ,EAAE;SAChB,MAAM;UACL,IAAI,CAACO,KAAK,GAAGI,QAAQ,CAACI,OAAO,IAAI,8BAA8B;;QAEjE,IAAI,CAACK,UAAU,GAAG,KAAK;MACzB,CAAC;MACDb,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,gCAAgC,IAAIA,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAAC;QACxE,IAAI,CAACa,UAAU,GAAG,KAAK;QACvBJ,OAAO,CAACT,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;KACD,CAAC;EACJ;EAEAc,aAAa;IACX,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACf,KAAK,GAAG,IAAI;IACjB,IAAI,CAACgB,WAAW,GAAG,EAAE;IAErB,IAAI,CAAC3B,2BAA2B,CAACyB,aAAa,EAAE,CAACZ,SAAS,CAAC;MACzDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;UACpB,IAAI,CAACW,WAAW,GAAGZ,QAAQ,CAACG,IAAI;UAChC,IAAI,CAACU,eAAe,GAAG,IAAI;SAC5B,MAAM;UACL,IAAI,CAACjB,KAAK,GAAGI,QAAQ,CAACI,OAAO,IAAI,6BAA6B;;QAEhE,IAAI,CAACO,OAAO,GAAG,KAAK;MACtB,CAAC;MACDf,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,+BAA+B,IAAIA,KAAK,CAACQ,OAAO,IAAIR,KAAK,CAAC;QACvE,IAAI,CAACe,OAAO,GAAG,KAAK;QACpBN,OAAO,CAACT,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;KACD,CAAC;EACJ;EAIAkB,mBAAmB,CAACC,MAAc;IAChC,QAAQA,MAAM,CAACC,WAAW,EAAE;MAC1B,KAAK,QAAQ;QACX,OAAO,kBAAkB;MAC3B,KAAK,UAAU;QACb,OAAO,oBAAoB;MAC7B,KAAK,SAAS;QACZ,OAAO,iBAAiB;MAC1B;QACE,OAAO,oBAAoB;IAAC;EAElC;EAEAC,aAAa,CAACF,MAAc;IAC1B,QAAQA,MAAM,CAACC,WAAW,EAAE;MAC1B,KAAK,QAAQ;QACX,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,WAAW;MACpB,KAAK,SAAS;QACZ,OAAO,WAAW;MACpB;QACE,OAAOD,MAAM;IAAC;EAEpB;EAEAG,cAAc,CAACC,QAAgB;IAC7B;IACA,MAAMC,KAAK,GAAGD,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC;IACjC,IAAID,KAAK,CAACE,MAAM,IAAI,CAAC,EAAE;MACrB,MAAMC,KAAK,GAAGC,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;MAChC,MAAMK,OAAO,GAAGD,QAAQ,CAACJ,KAAK,CAAC,CAAC,CAAC,CAAC;MAElC,IAAIG,KAAK,GAAG,CAAC,EAAE;QACb,OAAO,GAAGA,KAAK,KAAKE,OAAO,GAAG;OAC/B,MAAM;QACL,OAAO,GAAGA,OAAO,GAAG;;;IAGxB,OAAON,QAAQ;EACjB;EAEAO,UAAU,CAACC,IAA+B;IACxC,IAAI,CAACA,IAAI,EAAE,OAAO,OAAO;IAEzB,MAAMC,CAAC,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG,IAAIE,IAAI,CAACF,IAAI,CAAC,GAAGA,IAAI;IAC1D,OAAOC,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;EAClC;EAEAC,YAAY,CAACC,GAAW;IACtB,OAAOA,GAAG,CAACC,OAAO,CAAC,CAAC,CAAC;EACvB;EAEAC,iBAAiB,CAACC,OAAY;IAC5B,IAAI,CAACA,OAAO,CAACC,WAAW,EAAE;MACxB,OAAO,eAAe;;IAGxB,MAAMC,EAAE,GAAGF,OAAO,CAACC,WAAW;IAC9B,MAAME,UAAU,GAAG,CAACD,EAAE,CAACC,UAAU,GAAG,GAAG,EAAEL,OAAO,CAAC,CAAC,CAAC;IAEnD,OAAO,GAAGI,EAAE,CAACE,SAAS,OAAOF,EAAE,CAACG,OAAO,QAAQF,UAAU,iBAAiB;EAC5E;EAEAG,qBAAqB,CAACN,OAAY;IAChC,IAAI,CAACA,OAAO,CAACC,WAAW,EAAE;MACxB,OAAO,iEAAiE;;IAG1E,MAAMC,EAAE,GAAGF,OAAO,CAACC,WAAW;IAC9B,MAAMM,IAAI,GAAGL,EAAE,CAACM,UAAU,EAAEC,GAAG,CAAEC,GAAW,IAAK,IAAI,CAACC,UAAU,CAACD,GAAG,CAAC,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,WAAW;IAChG,OAAO,gBAAgBL,IAAI,mBAAmB,CAACL,EAAE,CAACC,UAAU,GAAG,GAAG,EAAEL,OAAO,CAAC,CAAC,CAAC,GAAG;EACnF;EAEA;;;EAGQa,UAAU,CAACE,SAAiB;IAClC,MAAMC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3D,OAAOA,QAAQ,CAACD,SAAS,CAAC,IAAIA,SAAS,CAACE,QAAQ,EAAE;EACpD;EAEAC,wBAAwB,CAAChB,OAAY;IACnC,OAAOA,OAAO,CAACiB,yBAAyB,IAAI,gDAAgD;EAC9F;EAEAC,0BAA0B,CAACC,aAAqB;IAC9C,QAAQA,aAAa;MACnB,KAAK,2BAA2B;QAC9B,OAAO,WAAW;MACpB,KAAK,oBAAoB;QACvB,OAAO,YAAY;MACrB,KAAK,wBAAwB;QAC3B,OAAO,aAAa;MACtB;QACE,OAAO,SAAS;IAAC;EAEvB;EAEAC,2BAA2B,CAACD,aAAqB;IAC/C,QAAQA,aAAa;MACnB,KAAK,2BAA2B;QAC9B,OAAO,YAAY;MACrB,KAAK,oBAAoB;QACvB,OAAO,SAAS;MAClB,KAAK,wBAAwB;QAC3B,OAAO,YAAY;MACrB;QACE,OAAO,cAAc;IAAC;EAE5B;EAEAE,gBAAgB;IACd,IAAI,CAAC3C,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACD,WAAW,GAAG,EAAE;EACvB;EAIA;;;EAGQpB,YAAY;IAClB;IACA,MAAMiE,WAAW,GAA2B;MAC1C,iBAAiB,EAAE,yEAAyE,GAC1F,+EAA+E;MACjF,WAAW,EAAE,uDAAuD,GAClE,4FAA4F;MAC9F,eAAe,EAAE,wEAAwE,GACvF,iDAAiD;MACnD,kBAAkB,EAAE,uEAAuE,GACzF,wDAAwD;MAC1D,oBAAoB,EAAE,kEAAkE,GACtF,uDAAuD;MACzD,yBAAyB,EAAE,oEAAoE,GAC7F,sEAAsE;MACxE,gBAAgB,EAAE,mEAAmE,GACnF,oDAAoD;MACtD,oBAAoB,EAAE,6EAA6E,GACjG;KACH;IAED;IACA,IAAI,CAAC/D,eAAe,EAAE;IAEtB;IACAgE,QAAQ,CAACC,gBAAgB,CAAC,4BAA4B,CAAC,CAACC,OAAO,CAACC,EAAE,IAAG;MACnE,MAAMC,QAAQ,GAAGD,EAAE,CAACE,YAAY,CAAC,gBAAgB,CAAC;MAElD,IAAID,QAAQ,IAAIA,QAAQ,IAAIL,WAAW,EAAE;QACvC,IAAI;UACF,IAAIO,SAAS,CAACC,OAAO,CAACJ,EAAE,EAAE;YACxBK,OAAO,EAAET,WAAW,CAACK,QAAoC,CAAC;YAC1DK,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,aAAa;YACtBC,SAAS,EAAE,KAAK;YAChBC,SAAS,EAAE,MAAM;YACjBC,QAAQ,EAAE;WACX,CAAC;SACH,CAAC,OAAO3E,KAAK,EAAE;UACdS,OAAO,CAACT,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;OAE3D,MAAM,IAAIkE,QAAQ,EAAE;QACnBzD,OAAO,CAACmE,IAAI,CAAC,8BAA8B,EAAEV,QAAQ,CAAC;;IAE1D,CAAC,CAAC;EACJ;EAEA;;;EAGQpE,eAAe;IACrBgE,QAAQ,CAACC,gBAAgB,CAAC,4BAA4B,CAAC,CAACC,OAAO,CAACC,EAAE,IAAG;MACnE,MAAMY,OAAO,GAAGT,SAAS,CAACC,OAAO,CAACS,WAAW,CAACb,EAAE,CAAC;MACjD,IAAIY,OAAO,EAAE;QACXA,OAAO,CAACE,OAAO,EAAE;;IAErB,CAAC,CAAC;EACJ;;;uBAzSW5F,8BAA8B;IAAA;EAAA;;;YAA9BA,8BAA8B;MAAA6F;MAAAC;MAAAC;MAAAC;MAAAC;QAAA;UDZ3ClG,8BAA6B;UAKnBA,uBAAyC;UACzCA,4DACF;UAAAA,iBAAK;UACLA,4BAA2B;UACzBA,2QAEF;UAAAA,iBAAI;UAINA,+EAIM;UAGNA,iFAKM;UAGNA,mFAwDM;UAGNA,sHA8EwB;UAC1BA,iBAAM;UAKVA,mFAkDM;UACNA,kFAA8F;;;UAjNlFA,eAAW;UAAXA,gCAAW;UAOXA,eAAa;UAAbA,kCAAa;UAQbA,eAAwB;UAAxBA,iDAAwB;UA2DNA,eAAc;UAAdA,mCAAc;UAqFVA,eAAqB;UAArBA,0CAAqB;UAkDUA,eAAqB;UAArBA,0CAAqB", "names": ["i0", "CommunicationPatternsComponent", "constructor", "communicationPatternService", "titleService", "ngOnInit", "setTitle", "loadData", "ngAfterViewInit", "setTimeout", "initPopovers", "ngOnDestroy", "destroyPopovers", "loading", "error", "getAllPatterns", "subscribe", "next", "response", "success", "patterns", "data", "message", "console", "getConfiguration", "config", "refreshPatterns", "refreshing", "testDetection", "testing", "testResults", "showTestResults", "getStatusBadgeClass", "status", "toLowerCase", "getStatusText", "formatTimeSpan", "timeSpan", "parts", "split", "length", "hours", "parseInt", "minutes", "formatDate", "date", "d", "Date", "toLocaleString", "formatNumber", "num", "toFixed", "formatActiveHours", "pattern", "activeHours", "ah", "confidence", "startHour", "endHour", "getActiveHoursTooltip", "days", "activeDays", "map", "day", "getDayName", "join", "dayNumber", "dayNames", "toString", "getDetectionLogicTooltip", "detectionLogicDescription", "getDetectionLogicShortText", "detectionType", "getDetectionLogicBadgeClass", "closeTestResults", "helpContent", "document", "querySelectorAll", "for<PERSON>ach", "el", "helpType", "getAttribute", "bootstrap", "Popover", "content", "html", "trigger", "placement", "container", "sanitize", "warn", "popover", "getInstance", "dispose", "selectors", "decls", "vars", "consts", "template"], "sourceRoot": "", "sources": ["C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\admin\\communication-patterns\\communication-patterns.component.html", "C:\\Users\\<USER>\\Documents\\VSCodeProjects\\DISAdminAugment\\DISAdmin.Web\\ClientApp\\src\\app\\admin\\communication-patterns\\communication-patterns.component.ts"], "sourcesContent": ["<div class=\"container-fluid\">\n  <div class=\"row\">\n    <div class=\"col-12\">\n      <div class=\"mb-4\">\n        <h2>\n          <i class=\"bi bi-diagram-3-fill me-2\"></i>\n          Komunikační vzorce instancí\n        </h2>\n        <p class=\"text-muted mb-0\">\n          Analýza historického chování komunikace instancí pro inteligentní detekci výpadků.\n          Instance bez nastavených pracovních hodin používají automaticky analyzované aktivní hodiny.\n        </p>\n      </div>\n\n      <!-- Error <PERSON> -->\n      <div *ngIf=\"error\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n        <i class=\"bi bi-exclamation-triangle-fill me-2\"></i>\n        {{ error }}\n        <button type=\"button\" class=\"btn-close\" (click)=\"error = null\"></button>\n      </div>\n\n      <!-- Loading -->\n      <div *ngIf=\"loading\" class=\"text-center py-4\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Načítám...</span>\n        </div>\n        <p class=\"mt-2\">Načítám komunikační vzorce...</p>\n      </div>\n\n      <!-- Configuration Card -->\n      <div *ngIf=\"config && !loading\" class=\"card mb-4\">\n        <div class=\"card-header bg-light d-flex justify-content-between align-items-center\">\n          <h5 class=\"card-title mb-0\">\n            <i class=\"bi bi-gear-fill me-2\"></i>\n            Konfigurace detekce výpadků\n          </h5>\n          <div>\n            <button class=\"btn btn-outline-primary btn-sm me-2\"\n                    (click)=\"testDetection()\"\n                    [disabled]=\"testing\">\n              <i class=\"bi bi-play-circle\" [class.spinner-border]=\"testing\" [class.spinner-border-sm]=\"testing\"></i>\n              <span *ngIf=\"!testing\"> Testovat detekci</span>\n              <span *ngIf=\"testing\"> Testování...</span>\n            </button>\n            <button class=\"btn btn-primary btn-sm\"\n                    (click)=\"refreshPatterns()\"\n                    [disabled]=\"refreshing\">\n              <i class=\"bi bi-arrow-clockwise\" [class.spinner-border]=\"refreshing\" [class.spinner-border-sm]=\"refreshing\"></i>\n              <span *ngIf=\"!refreshing\"> Aktualizovat vzorce</span>\n              <span *ngIf=\"refreshing\"> Aktualizuji...</span>\n            </button>\n          </div>\n        </div>\n        <div class=\"card-body\">\n          <div class=\"row\">\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"analysis-window\" style=\"cursor: help;\">Okno analýzy:</strong> {{ config.analysisWindowDays }} dní\n            </div>\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"tolerance\" style=\"cursor: help;\">Tolerance:</strong> {{ config.toleranceMinutes }} minut\n            </div>\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"min-frequency\" style=\"cursor: help;\">Min. frekvence:</strong> {{ config.minimumFrequencyPerHour }}/hod\n            </div>\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"exclude-weekends\" style=\"cursor: help;\">Vyloučit víkendy: </strong>\n              <span class=\"badge\" [class]=\"config.excludeWeekends ? 'bg-success' : 'bg-secondary'\">\n                {{ config.excludeWeekends ? 'Ano' : 'Ne' }}\n              </span>\n            </div>\n          </div>\n          <div class=\"row mt-2\">\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"min-alert-interval\" style=\"cursor: help;\">Min. interval alertů:</strong> {{ config.minimumAlertInterval }} min\n            </div>\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"pattern-update-interval\" style=\"cursor: help;\">Aktualizace vzorců:</strong> {{ config.patternUpdateIntervalHours }} hod\n            </div>\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"check-interval\" style=\"cursor: help;\">Interval kontrol:</strong> {{ config.checkIntervalMinutes }} min\n            </div>\n            <div class=\"col-md-3\">\n              <strong data-bs-toggle=\"popover\" data-help-type=\"min-calls-analysis\" style=\"cursor: help;\">Min. volání pro analýzu:</strong> {{ config.minimumCallsForAnalysis }}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Patterns Table -->\n      <app-collapsible-block *ngIf=\"!loading\"\n                             title=\"Komunikační vzorce\"\n                             icon=\"bi-table\"\n                             [showRecordCount]=\"true\"\n                             [recordCount]=\"patterns.length\"\n                             storageKey=\"collapsible_block_communication_patterns\"\n                             [defaultExpanded]=\"true\"\n                             marginTop=\"\"\n                             marginBottom=\"mb-4\">\n        <div class=\"table-responsive\">\n          <table class=\"table table-hover mb-0\">\n            <thead class=\"table-light\">\n              <tr>\n                <th class=\"text-nowrap\">Zákazník</th>\n                <th class=\"text-nowrap\">Instance</th>\n                <th class=\"text-nowrap\">Stav</th>\n                <th class=\"text-nowrap\">Typ detekce</th>\n                <th class=\"text-nowrap\">Aktivní hodiny</th>\n                <th class=\"text-nowrap\" style=\"width: 120px;\">Volání za 30 dní</th>\n                <th class=\"text-nowrap\">Průměr (min)</th>\n                <th class=\"text-nowrap\">Std. odchylka</th>\n                <th class=\"text-nowrap\">Min/max (min)</th>\n                <th class=\"text-nowrap\" style=\"width: 140px;\">Posl. kontakt</th>\n                <th class=\"text-nowrap\">Dostatek dat</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let item of patterns\">\n                <td>\n                  <strong>{{ item.customerAbbreviation }}</strong>\n                </td>\n                <td class=\"text-nowrap\">\n                  <strong>{{ item.instanceName }}</strong>\n                </td>\n                <td>\n                  <span [class]=\"getStatusBadgeClass(item.status)\">\n                    {{ getStatusText(item.status) }}\n                  </span>\n                </td>\n                <td [title]=\"getDetectionLogicTooltip(item.pattern)\">\n                  <span class=\"badge\" [class]=\"getDetectionLogicBadgeClass(item.pattern.detectionLogicType)\">\n                    {{ getDetectionLogicShortText(item.pattern.detectionLogicType) }}\n                  </span>\n                </td>\n                <td [title]=\"getActiveHoursTooltip(item.pattern)\">\n                  <small>{{ formatActiveHours(item.pattern) }}</small>\n                </td>\n                <td class=\"text-center text-nowrap\">\n                  <strong>{{ item.pattern.totalCallsAnalyzed }}</strong>\n                </td>\n                <td class=\"text-end\">\n                  {{ formatNumber(item.pattern.averageIntervalMinutes) }}\n                </td>\n                <td class=\"text-end\">\n                  {{ formatNumber(item.pattern.standardDeviation) }}\n                </td>\n                <td class=\"text-center\">\n                  {{ formatNumber(item.pattern.minIntervalMinutes) }} /\n                  {{ formatNumber(item.pattern.maxIntervalMinutes) }}\n                </td>\n                <td class=\"text-nowrap\">\n                  {{ formatDate(item.lastConnectionDate) }}\n                </td>\n                <td class=\"text-center\">\n                  <span class=\"badge\" [class]=\"item.pattern.hasSufficientData ? 'bg-success' : 'bg-warning'\">\n                    {{ item.pattern.hasSufficientData ? 'Ano' : 'Ne' }}\n                  </span>\n                </td>\n              </tr>\n              <tr *ngIf=\"patterns.length === 0\">\n                <td colspan=\"11\" class=\"text-center py-4 text-muted\">\n                  <i class=\"bi bi-inbox fs-1\"></i>\n                  <p class=\"mt-2\">Žádné komunikační vzorce nebyly nalezeny</p>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </app-collapsible-block>\n    </div>\n  </div>\n</div>\n\n<!-- Test Results Modal -->\n<div class=\"modal fade\" [class.show]=\"showTestResults\" [style.display]=\"showTestResults ? 'block' : 'none'\" \n     tabindex=\"-1\" role=\"dialog\" *ngIf=\"showTestResults\">\n  <div class=\"modal-dialog modal-lg\" role=\"document\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\">\n          <i class=\"bi bi-bug-fill me-2\"></i>\n          Výsledky testování detekce výpadků\n        </h5>\n        <button type=\"button\" class=\"btn-close\" (click)=\"closeTestResults()\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <div *ngIf=\"testResults.length === 0\" class=\"text-center py-4\">\n          <i class=\"bi bi-check-circle-fill text-success fs-1\"></i>\n          <p class=\"mt-2\">Žádné výpadky komunikace nebyly detekovány</p>\n        </div>\n        <div *ngIf=\"testResults.length > 0\">\n          <div class=\"alert alert-warning\">\n            <i class=\"bi bi-exclamation-triangle-fill me-2\"></i>\n            Detekováno {{ testResults.length }} výpadků komunikace\n          </div>\n          <div class=\"table-responsive\">\n            <table class=\"table table-sm\">\n              <thead>\n                <tr>\n                  <th>Instance</th>\n                  <th>Zákazník</th>\n                  <th>Čas od kontaktu</th>\n                  <th>Očekávaný max</th>\n                  <th>Poslední kontakt</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr *ngFor=\"let outage of testResults\">\n                  <td>{{ outage.instanceName }}</td>\n                  <td>{{ outage.customerAbbreviation }}</td>\n                  <td>{{ formatTimeSpan(outage.timeSinceLastContact) }}</td>\n                  <td>{{ formatTimeSpan(outage.expectedMaxInterval) }}</td>\n                  <td>{{ formatDate(outage.lastContactTime) }}</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </div>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closeTestResults()\">Zavřít</button>\n      </div>\n    </div>\n  </div>\n</div>\n<div class=\"modal-backdrop fade\" [class.show]=\"showTestResults\" *ngIf=\"showTestResults\"></div>", "import { Component, OnInit, AfterViewInit, OnD<PERSON>roy } from '@angular/core';\nimport { CommunicationPatternService } from '../../services/communication-pattern.service';\nimport { CommunicationPatternInfo, CommunicationOutageConfig, CommunicationOutage } from '../../models/communication-pattern.model';\nimport { TitleService } from '../../services/title.service';\n\ndeclare var bootstrap: any;\n\n@Component({\n  selector: 'app-communication-patterns',\n  templateUrl: './communication-patterns.component.html',\n  styleUrls: ['./communication-patterns.component.css']\n})\nexport class CommunicationPatternsComponent implements OnInit, AfterViewInit, OnDestroy {\n  patterns: CommunicationPatternInfo[] = [];\n  config: CommunicationOutageConfig | null = null;\n  testResults: CommunicationOutage[] = [];\n  loading = false;\n  refreshing = false;\n  testing = false;\n  error: string | null = null;\n  showTestResults = false;\n\n  constructor(\n    private communicationPatternService: CommunicationPatternService,\n    private titleService: TitleService\n  ) { }\n\n  ngOnInit(): void {\n    this.titleService.setTitle('Komunikační vzorce');\n    this.loadData();\n  }\n\n  ngAfterViewInit(): void {\n    // Inicializace popoverů s zpožděním, aby se zajistilo, že DOM je plně načten\n    setTimeout(() => {\n      this.initPopovers();\n    }, 500);\n  }\n\n  ngOnDestroy(): void {\n    // Zničení všech popoverů\n    this.destroyPopovers();\n  }\n\n  private loadData(): void {\n    this.loading = true;\n    this.error = null;\n\n    // Načtení vzorců\n    this.communicationPatternService.getAllPatterns().subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.patterns = response.data;\n        } else {\n          this.error = response.message || 'Chyba při načítání vzorců';\n        }\n      },\n      error: (error) => {\n        this.error = 'Chyba při načítání vzorců: ' + (error.message || error);\n        console.error('Chyba při načítání vzorců:', error);\n      }\n    });\n\n    // Načtení konfigurace\n    this.communicationPatternService.getConfiguration().subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.config = response.data;\n        }\n        this.loading = false;\n\n        // Reinicializace popoverů po načtení dat\n        setTimeout(() => {\n          this.initPopovers();\n        }, 100);\n      },\n      error: (error) => {\n        console.error('Chyba při načítání konfigurace:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  refreshPatterns(): void {\n    this.refreshing = true;\n    this.error = null;\n\n    this.communicationPatternService.refreshPatterns().subscribe({\n      next: (response) => {\n        if (response.success) {\n          // Po úspěšné aktualizaci znovu načti data\n          this.loadData();\n        } else {\n          this.error = response.message || 'Chyba při aktualizaci vzorců';\n        }\n        this.refreshing = false;\n      },\n      error: (error) => {\n        this.error = 'Chyba při aktualizaci vzorců: ' + (error.message || error);\n        this.refreshing = false;\n        console.error('Chyba při aktualizaci vzorců:', error);\n      }\n    });\n  }\n\n  testDetection(): void {\n    this.testing = true;\n    this.error = null;\n    this.testResults = [];\n\n    this.communicationPatternService.testDetection().subscribe({\n      next: (response) => {\n        if (response.success) {\n          this.testResults = response.data;\n          this.showTestResults = true;\n        } else {\n          this.error = response.message || 'Chyba při testování detekce';\n        }\n        this.testing = false;\n      },\n      error: (error) => {\n        this.error = 'Chyba při testování detekce: ' + (error.message || error);\n        this.testing = false;\n        console.error('Chyba při testování detekce:', error);\n      }\n    });\n  }\n\n\n\n  getStatusBadgeClass(status: string): string {\n    switch (status.toLowerCase()) {\n      case 'active':\n        return 'badge bg-success';\n      case 'inactive':\n        return 'badge bg-secondary';\n      case 'blocked':\n        return 'badge bg-danger';\n      default:\n        return 'badge bg-secondary';\n    }\n  }\n\n  getStatusText(status: string): string {\n    switch (status.toLowerCase()) {\n      case 'active':\n        return 'Aktivní';\n      case 'inactive':\n        return 'Neaktivní';\n      case 'blocked':\n        return 'Blokovaná';\n      default:\n        return status;\n    }\n  }\n\n  formatTimeSpan(timeSpan: string): string {\n    // Převod TimeSpan stringu na čitelný formát\n    const parts = timeSpan.split(':');\n    if (parts.length >= 2) {\n      const hours = parseInt(parts[0]);\n      const minutes = parseInt(parts[1]);\n      \n      if (hours > 0) {\n        return `${hours}h ${minutes}m`;\n      } else {\n        return `${minutes}m`;\n      }\n    }\n    return timeSpan;\n  }\n\n  formatDate(date: Date | string | undefined): string {\n    if (!date) return 'Nikdy';\n    \n    const d = typeof date === 'string' ? new Date(date) : date;\n    return d.toLocaleString('cs-CZ');\n  }\n\n  formatNumber(num: number): string {\n    return num.toFixed(1);\n  }\n\n  formatActiveHours(pattern: any): string {\n    if (!pattern.activeHours) {\n      return 'Neanalyzováno';\n    }\n\n    const ah = pattern.activeHours;\n    const confidence = (ah.confidence * 100).toFixed(0);\n\n    return `${ah.startHour}:00-${ah.endHour}:00 (${confidence}% spolehlivost)`;\n  }\n\n  getActiveHoursTooltip(pattern: any): string {\n    if (!pattern.activeHours) {\n      return 'Aktivní hodiny nebyly analyzovány - nedostatek historických dat';\n    }\n\n    const ah = pattern.activeHours;\n    const days = ah.activeDays?.map((day: number) => this.getDayName(day)).join(', ') || 'Žádné dny';\n    return `Aktivní dny: ${days}\\nSpolehlivost: ${(ah.confidence * 100).toFixed(1)}%`;\n  }\n\n  /**\n   * Převod čísla dne na český název\n   */\n  private getDayName(dayNumber: number): string {\n    const dayNames = ['Ne', 'Po', 'Út', 'St', 'Čt', 'Pá', 'So'];\n    return dayNames[dayNumber] || dayNumber.toString();\n  }\n\n  getDetectionLogicTooltip(pattern: any): string {\n    return pattern.detectionLogicDescription || 'Informace o detekční logice nejsou k dispozici';\n  }\n\n  getDetectionLogicShortText(detectionType: string): string {\n    switch (detectionType) {\n      case 'Nastavené pracovní hodiny':\n        return 'Nastavené';\n      case 'Historická analýza':\n        return 'Historická';\n      case 'Nepřetržitý monitoring':\n        return 'Nepřetržitý';\n      default:\n        return 'Neznámý';\n    }\n  }\n\n  getDetectionLogicBadgeClass(detectionType: string): string {\n    switch (detectionType) {\n      case 'Nastavené pracovní hodiny':\n        return 'bg-success';\n      case 'Historická analýza':\n        return 'bg-info';\n      case 'Nepřetržitý monitoring':\n        return 'bg-warning';\n      default:\n        return 'bg-secondary';\n    }\n  }\n\n  closeTestResults(): void {\n    this.showTestResults = false;\n    this.testResults = [];\n  }\n\n\n\n  /**\n   * Inicializace popoverů pro nápovědu\n   */\n  private initPopovers(): void {\n    // Definice obsahu nápověd\n    const helpContent: Record<string, string> = {\n      'analysis-window': 'Počet dní zpětně, ze kterých se analyzují komunikační vzorce instancí. ' +\n        'Delší období poskytuje přesnější analýzu, ale vyžaduje více historických dat.',\n      'tolerance': 'Tolerance v minutách pro detekci výpadků komunikace. ' +\n        'Pokud instance nekomunikuje déle než očekávaný interval + tolerance, je detekován výpadek.',\n      'min-frequency': 'Minimální frekvence komunikace za hodinu potřebná pro analýzu vzorce. ' +\n        'Instance s nižší frekvencí nebudou analyzovány.',\n      'exclude-weekends': 'Určuje, zda se mají víkendy vyloučit z analýzy komunikačních vzorců. ' +\n        'Užitečné pro instance, které nekomunikují o víkendech.',\n      'min-alert-interval': 'Minimální interval mezi alerty pro stejnou instanci v minutách. ' +\n        'Zabraňuje spamování alertů při opakovaných výpadcích.',\n      'pattern-update-interval': 'Interval v hodinách, jak často se aktualizují komunikační vzorce. ' +\n        'Kratší interval znamená aktuálnější vzorce, ale vyšší zátěž systému.',\n      'check-interval': 'Interval v minutách, jak často se kontrolují výpadky komunikace. ' +\n        'Kratší interval znamená rychlejší detekci výpadků.',\n      'min-calls-analysis': 'Minimální počet volání potřebný pro analýzu komunikačního vzorce instance. ' +\n        'Instance s menším počtem volání nebudou analyzovány.'\n    };\n\n    // Nejprve zrušíme všechny existující popovery\n    this.destroyPopovers();\n\n    // Inicializace popoverů pomocí Bootstrap API\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const helpType = el.getAttribute('data-help-type');\n\n      if (helpType && helpType in helpContent) {\n        try {\n          new bootstrap.Popover(el, {\n            content: helpContent[helpType as keyof typeof helpContent],\n            html: true,\n            trigger: 'hover focus',\n            placement: 'top',\n            container: 'body',\n            sanitize: false\n          });\n        } catch (error) {\n          console.error('Chyba při inicializaci popoveru:', error);\n        }\n      } else if (helpType) {\n        console.warn('Nápověda nenalezena pro typ:', helpType);\n      }\n    });\n  }\n\n  /**\n   * Zničení všech popoverů\n   */\n  private destroyPopovers(): void {\n    document.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach(el => {\n      const popover = bootstrap.Popover.getInstance(el);\n      if (popover) {\n        popover.dispose();\n      }\n    });\n  }\n}\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}