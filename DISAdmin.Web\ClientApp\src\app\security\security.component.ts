import { Component, OnInit } from '@angular/core';
import { SecurityService } from '../services/security.service';
import {
  SecurityEventResponse,
  FailedConnectionStatsResponse,
  SecurityEventFilterResponse,
  CreateSecurityEventFilterRequest,
  MatchType
} from '../models/security.model';

import { ModalService } from '../services/modal.service';
import { ToastrService } from 'ngx-toastr';
import * as bootstrap from 'bootstrap';
import { LocalDatePipe } from '../shared/pipes/local-date.pipe';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../shared/shared.module';

@Component({
  selector: 'app-security',
  templateUrl: './security.component.html',
  styleUrls: ['./security.component.css'],
  imports: [LocalDatePipe, CommonModule, FormsModule, ReactiveFormsModule, SharedModule],
  standalone: true
})
export class SecurityComponent implements OnInit {
  loading: boolean = false;
  error: string | null = null;

  securityEvents: SecurityEventResponse[] = [];
  failedConnectionStats: FailedConnectionStatsResponse[] = [];

  selectedSecurityEvent: SecurityEventResponse | null = null;

  // Security Event Filters
  securityEventFilters: SecurityEventFilterResponse[] = [];
  selectedEvent: SecurityEventResponse | null = null;
  deleteExistingEvents: boolean = false;
  loadingFilters: boolean = false;
  applyToAllIps: boolean = true; // Výchozí stav - použít pro všechny IP
  filterIpAddress: string = '';
  descriptionMatchType: MatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro popis
  ipAddressMatchType: MatchType = MatchType.ExactMatch; // Výchozí typ porovnávání pro IP adresu

  constructor(
    private securityService: SecurityService,
    private modalService: ModalService,
    private toastr: ToastrService
  ) {
  }

  ngOnInit(): void {
    this.loadSecurityDashboard();
  }

  loadSecurityDashboard(): void {
    this.loading = true;
    this.error = null;

    this.securityService.getSecurityDashboard().subscribe({
      next: (response) => {
        // Převod časů na lokální čas
        this.securityEvents = response.recentSecurityEvents.map(event => ({
          ...event,
          timestamp: new Date(event.timestamp)
        }));

        this.failedConnectionStats = response.failedConnectionStats.map(stat => ({
          ...stat,
          lastFailedCertificateValidation: stat.lastFailedCertificateValidation ? new Date(stat.lastFailedCertificateValidation) : undefined,
          lastFailedApiKeyValidation: stat.lastFailedApiKeyValidation ? new Date(stat.lastFailedApiKeyValidation) : undefined
        }));

        this.loading = false;
      },
      error: (err) => {
        console.error('Error loading security dashboard', err);
        this.error = 'Chyba při načítání bezpečnostního dashboardu';
        this.loading = false;
      }
    });
  }

  openResolveSecurityEventModal(securityEvent: SecurityEventResponse): void {
    this.selectedSecurityEvent = securityEvent;
    this.modalService.open('resolveSecurityEventModal');
  }

  onResolveSecurityEvent(resolution: string): void {
    if (!this.selectedSecurityEvent) {
      return;
    }

    this.securityService.resolveSecurityEvent(this.selectedSecurityEvent.id, resolution).subscribe({
      next: () => {
        this.toastr.success('Bezpečnostní událost byla úspěšně vyřešena', 'Úspěch');
        this.loadSecurityDashboard();
      },
      error: (err) => {
        console.error('Error resolving security event', err);
        this.error = 'Chyba při řešení bezpečnostní události';
        this.toastr.error('Chyba při řešení bezpečnostní události', 'Chyba');
      }
    });
  }

  onIgnoreSecurityEvent(): void {
    if (!this.selectedSecurityEvent) {
      return;
    }

    this.securityService.ignoreSecurityEvent(this.selectedSecurityEvent.id).subscribe({
      next: () => {
        this.toastr.success('Bezpečnostní událost byla ignorována', 'Úspěch');
        this.loadSecurityDashboard();
      },
      error: (err) => {
        console.error('Error ignoring security event', err);
        this.error = 'Chyba při ignorování bezpečnostní události';
        this.toastr.error('Chyba při ignorování bezpečnostní události', 'Chyba');
      }
    });
  }

  getSeverityClass(severity: number): string {
    switch (severity) {
      case 5:
        return 'text-danger fw-bold';
      case 4:
        return 'text-danger';
      case 3:
        return 'text-warning';
      case 2:
        return 'text-info';
      case 1:
        return 'text-success';
      default:
        return 'text-muted';
    }
  }

  getSeverityIcon(severity: number): string {
    switch (severity) {
      case 5:
      case 4:
        return 'bi-exclamation-triangle-fill';
      case 3:
        return 'bi-exclamation-circle-fill';
      case 2:
        return 'bi-info-circle-fill';
      default:
        return 'bi-check-circle-fill';
    }
  }

  getSeverityText(severity: number): string {
    switch (severity) {
      case 5:
        return 'Kritická';
      case 4:
        return 'Vysoká';
      case 3:
        return 'Střední';
      case 2:
        return 'Nízká';
      case 1:
        return 'Informační';
      default:
        return 'Neznámá';
    }
  }

  getEventTypeClass(eventType: string): string {
    switch (eventType) {
      case 'SuspiciousActivity':
        return 'text-danger';
      case 'CertificateValidationFailure':
        return 'text-warning';
      case 'IpBlocked':
        return 'text-danger';
      case 'FailedAccessAttempt':
        return 'text-warning';
      default:
        return 'text-info';
    }
  }

  getEventTypeIcon(eventType: string): string {
    switch (eventType) {
      case 'SuspiciousActivity':
        return 'bi-exclamation-triangle-fill';
      case 'CertificateValidationFailure':
        return 'bi-shield-exclamation';
      case 'IpBlocked':
        return 'bi-slash-circle-fill';
      case 'FailedAccessAttempt':
        return 'bi-x-circle-fill';
      case 'ApiKeyMisuse':
        return 'bi-key-fill';
      case 'UnauthorizedAccess':
        return 'bi-shield-x';
      case 'Other':
        return 'bi-question-circle-fill';
      default:
        return 'bi-info-circle-fill';
    }
  }

  getEventTypeText(eventType: string): string {
    switch (eventType) {
      case 'FailedAccessAttempt':
        return 'Neúspěšný pokus o přístup';
      case 'SuspiciousActivity':
        return 'Podezřelá aktivita';
      case 'IpBlocked':
        return 'Blokovaná IP adresa';
      case 'CertificateValidationFailure':
        return 'Selhání validace certifikátu';
      case 'ApiKeyMisuse':
        return 'Nesprávné použití API klíče';
      case 'UnauthorizedAccess':
        return 'Neautorizovaný přístup';
      case 'Other':
        return 'Ostatní';
      default:
        return eventType;
    }
  }

  // Security Event Filter methods
  openBlockEventModal(event: SecurityEventResponse): void {
    this.selectedEvent = event;
    this.deleteExistingEvents = false;
    this.applyToAllIps = true; // Výchozí stav
    this.filterIpAddress = event.ipAddress || '';
    const modal = new bootstrap.Modal(document.getElementById('blockEventModal')!);
    modal.show();
  }

  onApplyToAllIpsChange(): void {
    if (!this.applyToAllIps && this.selectedEvent) {
      // Pokud se vypne "pro všechny IP", nastav IP adresu z události
      this.filterIpAddress = this.selectedEvent.ipAddress || '';
    }
  }

  confirmBlockEvent(): void {
    if (!this.selectedEvent) return;

    const eventTypeNumber = this.getEventTypeNumber(this.selectedEvent.eventType);

    const request: CreateSecurityEventFilterRequest = {
      eventType: eventTypeNumber,
      description: this.selectedEvent.description,
      descriptionMatchType: this.descriptionMatchType,
      ipAddress: this.applyToAllIps ? undefined : this.filterIpAddress,
      ipAddressMatchType: this.ipAddressMatchType,
      deleteExistingEvents: this.deleteExistingEvents
    };

    this.securityService.createSecurityEventFilter(request).subscribe({
      next: (response) => {
        if (response.success) {
          this.toastr.success('Filtr byl úspěšně vytvořen', 'Úspěch');
          this.loadSecurityDashboard(); // Refresh data
          const modal = bootstrap.Modal.getInstance(document.getElementById('blockEventModal')!);
          modal?.hide();
        } else {
          this.toastr.error(response.message || 'Chyba při vytváření filtru', 'Chyba');
        }
      },
      error: (error) => {
        console.error('Error creating filter:', error);
        this.toastr.error('Chyba při vytváření filtru', 'Chyba');
      }
    });
  }

  openManageFiltersModal(): void {
    this.loadSecurityEventFilters();
    const modal = new bootstrap.Modal(document.getElementById('manageFiltersModal')!);
    modal.show();
  }

  loadSecurityEventFilters(): void {
    this.loadingFilters = true;
    this.securityService.getSecurityEventFilters().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.securityEventFilters = response.data.map(filter => ({
            ...filter,
            createdAt: new Date(filter.createdAt)
          }));
        } else {
          this.securityEventFilters = [];
        }
        this.loadingFilters = false;
      },
      error: (error) => {
        console.error('Error loading filters:', error);
        this.toastr.error('Chyba při načítání filtrů', 'Chyba');
        this.loadingFilters = false;
      }
    });
  }

  confirmDeleteFilter(filter: SecurityEventFilterResponse): void {
    if (confirm(`Opravdu chcete smazat filtr pro "${filter.eventTypeName}"?`)) {
      this.securityService.deleteSecurityEventFilter(filter.id).subscribe({
        next: (response) => {
          if (response.success) {
            this.toastr.success('Filtr byl úspěšně smazán', 'Úspěch');
            this.loadSecurityEventFilters(); // Refresh filters
          } else {
            this.toastr.error(response.message || 'Chyba při mazání filtru', 'Chyba');
          }
        },
        error: (error) => {
          console.error('Error deleting filter:', error);
          this.toastr.error('Chyba při mazání filtru', 'Chyba');
        }
      });
    }
  }

  private getEventTypeNumber(eventType: string): number {
    switch (eventType) {
      case 'FailedAccessAttempt':
        return 0;
      case 'SuspiciousActivity':
        return 1;
      case 'IpBlocked':
        return 2;
      case 'CertificateValidationFailure':
        return 3;
      case 'ApiKeyMisuse':
        return 4;
      case 'UnauthorizedAccess':
        return 5;
      case 'Other':
        return 6;
      default:
        return 6; // Other
    }
  }
}
