"use strict";(self.webpackChunkDISAdmin_Web=self.webpackChunkDISAdmin_Web||[]).push([[650],{2650:(Xt,T,_)=>{_.r(T),_.d(T,{LogsModule:()=>Wt});var p=_(6895),y=_(6123),u=_(433),t=_(1571),F=_(3119),d=(()=>{return(o=d||(d={}))[o.Undefined=0]="Undefined",o[o.DISAdmin=1]="DISAdmin",o[o.DISApi=2]="DISApi",d;var o})(),m=(()=>{return(o=m||(m={}))[o.Trace=0]="Trace",o[o.Debug=1]="Debug",o[o.Information=2]="Information",o[o.Warning=3]="Warning",o[o.Error=4]="Error",o[o.Critical=5]="Critical",m;var o})();class f{static getLocalizedName(a){switch(a){case"Login":return"P\u0159ihl\xe1\u0161en\xed";case"Logout":return"Odhl\xe1\u0161en\xed";case"Create":return"Vytvo\u0159en\xed";case"Update":return"\xdaprava";case"Delete":return"Smaz\xe1n\xed";case"Export":return"Export";case"Import":return"Import";case"PasswordChange":return"Zm\u011bna hesla";case"ApiAccess":return"P\u0159\xedstup k API";case"Other":return"Ostatn\xed";default:return a}}static getActivityTypes(){return[{value:"Login",label:"P\u0159ihl\xe1\u0161en\xed"},{value:"Logout",label:"Odhl\xe1\u0161en\xed"},{value:"Create",label:"Vytvo\u0159en\xed"},{value:"Update",label:"\xdaprava"},{value:"Delete",label:"Smaz\xe1n\xed"},{value:"Export",label:"Export"},{value:"Import",label:"Import"},{value:"PasswordChange",label:"Zm\u011bna hesla"},{value:"ApiAccess",label:"P\u0159\xedstup k API"},{value:"Other",label:"Ostatn\xed"}]}}class C{static getLocalizedName(a){switch(a){case d.DISAdmin:return"DISAdmin";case d.DISApi:return"DIS API";case d.Undefined:return"Neur\u010deno";default:return"Nezn\xe1m\xfd"}}static getLogSources(){return[{value:d.DISAdmin,label:"DISAdmin"},{value:d.DISApi,label:"DIS API"},{value:d.Undefined,label:"Neur\u010deno"}]}}class x{static getLocalizedName(a){switch(a){case"Trace":return"Trasov\xe1n\xed";case"Debug":return"Lad\u011bn\xed";case"Information":return"Informace";case"Warning":return"Varov\xe1n\xed";case"Error":return"Chyba";case"Critical":return"Kritick\xe1";default:return a}}static getLogLevels(){return[{value:m.Trace,label:"Trasov\xe1n\xed"},{value:m.Debug,label:"Lad\u011bn\xed"},{value:m.Information,label:"Informace"},{value:m.Warning,label:"Varov\xe1n\xed"},{value:m.Error,label:"Chyba"},{value:m.Critical,label:"Kritick\xe1"}]}static getLogLevelClass(a){switch(a){case"Trace":return"bg-secondary";case"Debug":return"bg-info";case"Information":return"bg-primary";case"Warning":return"bg-warning";case"Error":return"bg-danger";case"Critical":return"bg-dark";default:return"bg-light"}}}var b=_(529),D=_(2340);let h=(()=>{const a=class{constructor(e){this.http=e,this.baseUrl=`${D.N.apiUrl}/logs`,this.filtersUrl=`${D.N.apiUrl}/filters`}getActivityLogs(e){let i=new b.LE;return e.userId&&(i=i.set("userId",e.userId.toString())),e.fromDate&&(i=i.set("fromDate",e.fromDate.toISOString())),e.toDate&&(i=i.set("toDate",e.toDate.toISOString())),e.maxResults&&(i=i.set("maxResults",e.maxResults.toString())),e.entityName&&(i=i.set("entityName",e.entityName)),e.entityId&&(i=i.set("entityId",e.entityId.toString())),e.activityType&&(i=i.set("activityType",e.activityType)),null!=e.source&&(i=i.set("source",e.source.toString())),this.http.get(`${this.baseUrl}/activity`,{params:i})}getErrorLogs(e){let i=new b.LE;return e.userId&&(i=i.set("userId",e.userId.toString())),e.fromDate&&(i=i.set("fromDate",e.fromDate.toISOString())),e.toDate&&(i=i.set("toDate",e.toDate.toISOString())),e.maxResults&&(i=i.set("maxResults",e.maxResults.toString())),null!=e.source&&(i=i.set("source",e.source.toString())),this.http.get(`${this.baseUrl}/errors`,{params:i})}getEntityActivityLogs(e,i,r=100){let s=(new b.LE).set("maxResults",r.toString());return this.http.get(`${this.baseUrl}/activity/entity/${e}/${i}`,{params:s})}getSavedLogFilters(e){return this.http.get(`${this.filtersUrl}/logs-${e}`)}saveLogFilter(e){const i={id:0,name:e.name,entityType:`logs-${e.logType}`,filter:JSON.parse(e.filterData),isDefault:!1,isShared:!1};return this.http.post(this.filtersUrl,i)}deleteLogFilter(e){return this.http.delete(`${this.filtersUrl}/${e}`)}};let o=a;return a.\u0275fac=function(i){return new(i||a)(t.LFG(b.eN))},a.\u0275prov=t.Yz7({token:a,factory:a.\u0275fac,providedIn:"root"}),o})();var v=_(1609),q=_(5861),M=_(3071),I=_(7185);function U(o,a){if(1&o&&(t.TgZ(0,"span",29),t._uU(1),t.qZA()),2&o){const n=t.oxw();t.xp6(1),t.Oqu(n.activeFilterCount)}}function P(o,a){if(1&o&&(t.TgZ(0,"option",47),t._uU(1),t.qZA()),2&o){const n=a.$implicit;t.Q6J("value",n.id),t.xp6(1),t.Oqu(n.username)}}function S(o,a){if(1&o&&(t.TgZ(0,"option",35),t._uU(1),t.qZA()),2&o){const n=a.$implicit;t.Q6J("ngValue",n.value),t.xp6(1),t.Oqu(n.label)}}function k(o,a){if(1&o&&(t.TgZ(0,"option",47),t._uU(1),t.qZA()),2&o){const n=a.$implicit;t.Q6J("value",n.value),t.xp6(1),t.Oqu(n.label)}}function N(o,a){if(1&o&&(t.ynx(0),t.TgZ(1,"div",32)(2,"label",48),t._uU(3,"Typ aktivity"),t.qZA(),t.TgZ(4,"select",49)(5,"option",35),t._uU(6,"V\u0161echny typy"),t.qZA(),t.YNc(7,k,2,2,"option",36),t.qZA()(),t.TgZ(8,"div",32)(9,"label",50),t._uU(10,"Entita"),t.qZA(),t._UZ(11,"input",51),t.qZA(),t.TgZ(12,"div",32)(13,"label",52),t._uU(14,"ID entity"),t.qZA(),t._UZ(15,"input",53),t.qZA(),t.BQk()),2&o){const n=t.oxw(2);t.xp6(5),t.Q6J("ngValue",""),t.xp6(2),t.Q6J("ngForOf",n.activityTypes)}}function J(o,a){if(1&o&&(t.TgZ(0,"option",47),t._uU(1),t.qZA()),2&o){const n=a.$implicit;t.Q6J("value",n.value),t.xp6(1),t.Oqu(n.label)}}function E(o,a){if(1&o&&(t.TgZ(0,"div",32)(1,"label",54),t._uU(2,"\xdarove\u0148 logov\xe1n\xed"),t.qZA(),t.TgZ(3,"select",55)(4,"option",35),t._uU(5,"V\u0161echny \xfarovn\u011b"),t.qZA(),t.YNc(6,J,2,2,"option",36),t.qZA()()),2&o){const n=t.oxw(3);t.xp6(4),t.Q6J("ngValue",null),t.xp6(2),t.Q6J("ngForOf",n.logLevels)}}function Q(o,a){1&o&&(t.TgZ(0,"div",32)(1,"label",56),t._uU(2,"Kategorie"),t.qZA(),t._UZ(3,"input",57),t.qZA())}function Y(o,a){if(1&o&&(t.ynx(0),t.YNc(1,E,7,2,"div",45),t.YNc(2,Q,4,0,"div",45),t.BQk()),2&o){const n=t.oxw(2);t.xp6(1),t.Q6J("ngIf",n.showLogLevelFilter),t.xp6(1),t.Q6J("ngIf",n.showCategoryFilter)}}function H(o,a){if(1&o&&(t.TgZ(0,"option",47),t._uU(1),t.qZA()),2&o){const n=a.$implicit;t.Q6J("value",n.value),t.xp6(1),t.Oqu(n.label)}}function $(o,a){1&o&&(t.TgZ(0,"div",61),t._uU(1," Zadejte po\u010d\xe1te\u010dn\xed datum "),t.qZA())}const A=function(o){return{"is-invalid":o}};function z(o,a){if(1&o&&(t.TgZ(0,"div",32)(1,"label",58),t._uU(2,"Od"),t.qZA(),t._UZ(3,"input",59),t.YNc(4,$,2,0,"div",60),t.qZA()),2&o){const n=t.oxw(2);let e,i;t.xp6(3),t.Q6J("ngClass",t.VKq(2,A,(null==(e=n.filterForm.get("fromDate"))?null:e.invalid)&&(null==(e=n.filterForm.get("fromDate"))?null:e.touched))),t.xp6(1),t.Q6J("ngIf",null==(i=n.filterForm.get("fromDate"))||null==i.errors?null:i.errors.required)}}function V(o,a){1&o&&(t.TgZ(0,"div",61),t._uU(1," Zadejte koncov\xe9 datum "),t.qZA())}function G(o,a){if(1&o&&(t.TgZ(0,"div",32)(1,"label",62),t._uU(2,"Do"),t.qZA(),t._UZ(3,"input",63),t.YNc(4,V,2,0,"div",60),t.qZA()),2&o){const n=t.oxw(2);let e,i;t.xp6(3),t.Q6J("ngClass",t.VKq(2,A,(null==(e=n.filterForm.get("toDate"))?null:e.invalid)&&(null==(e=n.filterForm.get("toDate"))?null:e.touched))),t.xp6(1),t.Q6J("ngIf",null==(i=n.filterForm.get("toDate"))||null==i.errors?null:i.errors.required)}}function K(o,a){if(1&o){const n=t.EpF();t.TgZ(0,"div",67),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw(3);return t.KtG(s.applySavedFilter(r))}),t.TgZ(1,"span",68),t._uU(2),t.qZA(),t.TgZ(3,"button",69),t.NdJ("click",function(i){const s=t.CHM(n).$implicit,l=t.oxw(3);return t.KtG(l.deleteSavedFilter(s,i))}),t._UZ(4,"i",70),t.qZA()()}if(2&o){const n=a.$implicit;t.xp6(2),t.Oqu(n.name)}}function j(o,a){if(1&o&&(t.TgZ(0,"div",64)(1,"h6"),t._uU(2,"Ulo\u017een\xe9 filtry"),t.qZA(),t.TgZ(3,"div",65),t.YNc(4,K,5,1,"div",66),t.qZA()()),2&o){const n=t.oxw(2);t.xp6(4),t.Q6J("ngForOf",n.savedFilters)}}function R(o,a){if(1&o&&(t.TgZ(0,"div",30)(1,"form",20)(2,"div",31)(3,"div",32)(4,"label",33),t._uU(5,"U\u017eivatel"),t.qZA(),t.TgZ(6,"select",34)(7,"option",35),t._uU(8,"V\u0161ichni u\u017eivatel\xe9"),t.qZA(),t.YNc(9,P,2,2,"option",36),t.qZA()(),t.TgZ(10,"div",32)(11,"label",37),t._uU(12,"Zdroj"),t.qZA(),t.TgZ(13,"select",38)(14,"option",35),t._uU(15,"V\u0161echny zdroje"),t.qZA(),t.YNc(16,S,2,2,"option",39),t.qZA()(),t.YNc(17,N,16,2,"ng-container",40),t.YNc(18,Y,3,2,"ng-container",40),t.TgZ(19,"div",32)(20,"label",41),t._uU(21,"Po\u010det z\xe1znam\u016f"),t.qZA(),t._UZ(22,"input",42),t.qZA(),t.TgZ(23,"div",32)(24,"label",43),t._uU(25,"\u010casov\xe9 obdob\xed"),t.qZA(),t.TgZ(26,"select",44),t.YNc(27,H,2,2,"option",36),t.qZA()(),t.YNc(28,z,5,4,"div",45),t.YNc(29,G,5,4,"div",45),t.qZA()(),t.YNc(30,j,5,1,"div",46),t.qZA()),2&o){const n=t.oxw();let e,i;t.xp6(1),t.Q6J("formGroup",n.filterForm),t.xp6(6),t.Q6J("ngValue",null),t.xp6(2),t.Q6J("ngForOf",n.users),t.xp6(5),t.Q6J("ngValue",null),t.xp6(2),t.Q6J("ngForOf",n.logSources),t.xp6(1),t.Q6J("ngIf","activity"===n.logType||"api"===n.logType),t.xp6(1),t.Q6J("ngIf","error"===n.logType),t.xp6(9),t.Q6J("ngForOf",n.periodOptions),t.xp6(1),t.Q6J("ngIf","custom"===(null==(e=n.filterForm.get("period"))?null:e.value)),t.xp6(1),t.Q6J("ngIf","custom"===(null==(i=n.filterForm.get("period"))?null:i.value)),t.xp6(1),t.Q6J("ngIf",n.savedFilters.length>0)}}function W(o,a){if(1&o&&(t.TgZ(0,"div",71),t._uU(1),t.qZA()),2&o){const n=t.oxw();t.xp6(1),t.Oqu(n.error)}}function B(o,a){1&o&&t._UZ(0,"span",72)}const X=function(o,a){return{"bi-funnel":o,"bi-funnel-fill text-primary":a}},tt=function(o,a){return{"bi-chevron-up":o,"bi-chevron-down":a}};let Z=(()=>{const a=class{constructor(e,i,r,s,l){this.fb=e,this.logsService=i,this.userService=r,this.modalService=s,this.toastr=l,this.logType="activity",this.showLogLevelFilter=!1,this.showCategoryFilter=!1,this.filterChange=new t.vpe,this.savedFilters=[],this.users=[],this.activityTypes=f.getActivityTypes(),this.logSources=C.getLogSources(),this.logLevels=x.getLogLevels(),this.isLoading=!1,this.error=null,this.isFilterVisible=!1,this.hasActiveFilters=!1,this.activeFilterCount=0,this.periodOptions=[{value:1,label:"1 den"},{value:7,label:"7 dn\xed"},{value:30,label:"30 dn\xed"},{value:90,label:"90 dn\xed"},{value:"custom",label:"Vlastn\xed obdob\xed"}],this.filterForm=this.createFilterForm(),this.saveFilterForm=this.fb.group({name:[""]})}ngOnInit(){this.loadUsers(),this.loadSavedFilters(),this.loadFilterVisibilityState(),this.loadCurrentFilterState(),this.filterForm.valueChanges.subscribe(()=>{this.updateActiveFiltersIndicator(),this.applyFilter()}),this.filterForm.get("period")?.valueChanges.subscribe(e=>{this.updateDateFieldsValidation(e)})}updateDateFieldsValidation(e){const i=this.filterForm.get("fromDate"),r=this.filterForm.get("toDate");"custom"===e?(i?.setValidators([u.kI.required]),r?.setValidators([u.kI.required])):(i?.clearValidators(),r?.clearValidators()),i?.updateValueAndValidity(),r?.updateValueAndValidity()}loadCurrentFilterState(){try{const e=`current_filter_${this.logType}`,i=localStorage.getItem(e);if(i){const r=JSON.parse(i);if(r.fromDate&&r.toDate&&!r.period){const s=new Date(r.fromDate),l=new Date(r.toDate),c=Math.round((l.getTime()-s.getTime())/864e5);r.period=c<=1?1:c<=7?7:c<=30?30:c<=90?90:"custom"}if(r.period&&"custom"!==r.period){const s=new Date,l=new Date(s);l.setHours(23,59,59,999);const c=new Date(s);c.setDate(s.getDate()-Number(r.period)),c.setHours(0,0,0,0),r.fromDate=this.formatDateForInput(c),r.toDate=this.formatDateForInput(l)}else{if(r.fromDate){const s=new Date(r.fromDate);r.fromDate=this.formatDateForInput(s)}if(r.toDate){const s=new Date(r.toDate);r.toDate=this.formatDateForInput(s)}}this.filterForm.patchValue(r),this.updateDateFieldsValidation(r.period),this.updateActiveFiltersIndicator()}}catch(e){console.error(`Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu filtru pro ${this.logType}`,e)}}toggleFilterVisibility(){this.isFilterVisible=!this.isFilterVisible;try{const e=`filter_visibility_logs_${this.logType}`;localStorage.setItem(e,this.isFilterVisible.toString())}catch(e){console.error("Chyba p\u0159i ukl\xe1d\xe1n\xed stavu viditelnosti filtru do localStorage",e)}}loadFilterVisibilityState(){try{const e=`filter_visibility_logs_${this.logType}`,i=localStorage.getItem(e);null!==i&&(this.isFilterVisible="true"===i)}catch(e){console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu viditelnosti filtru z localStorage",e)}}updateActiveFiltersIndicator(){const e=this.filterForm.value,i=new Date,r=new Date(i);r.setHours(23,59,59,999);const s=new Date(i);s.setDate(i.getDate()-7),s.setHours(0,0,0,0);const l=this.formatDateForInput(s),c=this.formatDateForInput(r),w=Object.keys(e).filter(g=>!("maxResults"===g&&100===e[g]||"source"===g&&("activity"===this.logType&&e[g]===d.DISAdmin||"api"===this.logType&&e[g]===d.DISApi)||"period"===g&&7===e[g]||"custom"!==e.period&&("fromDate"===g||"toDate"===g)||"custom"===e.period&&("fromDate"===g&&e[g]===l||"toDate"===g&&e[g]===c))&&null!==e[g]&&""!==e[g]&&void 0!==e[g]).reduce((g,O)=>(g[O]=e[O],g),{});this.activeFilterCount=Object.keys(w).length,this.hasActiveFilters=this.activeFilterCount>0,this.hasActiveFilters&&console.log("Aktivn\xed filtry:",w)}createFilterForm(){const e=new Date,i=new Date(e);i.setHours(23,59,59,999);const r=new Date(e);r.setDate(e.getDate()-7),r.setHours(0,0,0,0);const c={period:[7],fromDate:[this.formatDateForInput(r)],toDate:[this.formatDateForInput(i)],userId:[null],maxResults:[100],source:[null]};return"activity"===this.logType?c.source=[d.DISAdmin]:"api"===this.logType&&(c.source=[d.DISApi]),("activity"===this.logType||"api"===this.logType)&&(c.entityName=[""],c.entityId=[null],c.activityType=[""]),"error"===this.logType&&(c.logLevel=[null],c.category=[""]),this.fb.group(c)}loadUsers(){this.userService.getUsers().subscribe({next:e=>{this.users=e},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed u\u017eivatel\u016f",e)}})}loadSavedFilters(){this.logsService.getSavedLogFilters(this.logType).subscribe({next:e=>{this.savedFilters=e},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed ulo\u017een\xfdch filtr\u016f",e)}})}applyFilter(){const e=this.filterForm.value,i={...e};if("custom"!==e.period){const r=new Date,s=new Date(r);s.setHours(23,59,59,999);const l=new Date(r);l.setDate(r.getDate()-Number(e.period)),l.setHours(0,0,0,0),i.fromDate=l,i.toDate=s}else e.fromDate&&(i.fromDate=new Date(e.fromDate)),e.toDate&&(i.toDate=new Date(e.toDate));this.saveFilterState(i),this.filterChange.emit(i)}refreshData(){this.applyFilter()}saveFilterState(e){try{const i=`current_filter_${this.logType}`;localStorage.setItem(i,JSON.stringify(e))}catch(i){console.error(`Chyba p\u0159i ukl\xe1d\xe1n\xed stavu filtru pro ${this.logType}`,i)}}resetFilter(){const e=new Date,i=new Date(e);i.setHours(23,59,59,999);const r=new Date(e);r.setDate(e.getDate()-7),r.setHours(0,0,0,0);const c={period:7,fromDate:this.formatDateForInput(r),toDate:this.formatDateForInput(i),maxResults:100};"activity"===this.logType?c.source=d.DISAdmin:"api"===this.logType&&(c.source=d.DISApi),this.filterForm.reset(c),this.updateActiveFiltersIndicator(),this.applyFilter(),this.toastr.info("Filtr byl resetov\xe1n","Reset")}formatDateForInput(e){return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}T${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`}openSaveFilterModal(){this.saveFilterForm.reset(),this.modalService.open("saveFilterModal")}saveFilter(){if(this.saveFilterForm.invalid)return;const e=this.saveFilterForm.get("name")?.value,r={name:e,logType:this.logType,filterData:JSON.stringify(this.filterForm.value)};this.isLoading=!0,this.logsService.saveLogFilter(r).subscribe({next:s=>{this.savedFilters.push(s),this.modalService.close("saveFilterModal"),this.isLoading=!1},error:s=>{console.error("Chyba p\u0159i ukl\xe1d\xe1n\xed filtru",s),this.error="Nepoda\u0159ilo se ulo\u017eit filtr",this.isLoading=!1}})}applySavedFilter(e){try{const i=JSON.parse(e.filterData),r={...i};if(i.fromDate&&i.toDate&&!i.period){const s=new Date(i.fromDate),l=new Date(i.toDate),c=Math.round((l.getTime()-s.getTime())/864e5);r.period=c<=1?1:c<=7?7:c<=30?30:c<=90?90:"custom"}if(r.period&&"custom"!==r.period){const s=new Date,l=new Date(s);l.setHours(23,59,59,999);const c=new Date(s);c.setDate(s.getDate()-Number(r.period)),c.setHours(0,0,0,0),r.fromDate=this.formatDateForInput(c),r.toDate=this.formatDateForInput(l)}else{if(i.fromDate){const s=new Date(i.fromDate);r.fromDate=this.formatDateForInput(s)}if(i.toDate){const s=new Date(i.toDate);r.toDate=this.formatDateForInput(s)}}this.filterForm.reset({maxResults:100}),this.filterForm.patchValue(r),"activity"===this.logType?this.filterForm.patchValue({source:d.DISAdmin}):"api"===this.logType&&this.filterForm.patchValue({source:d.DISApi}),this.updateDateFieldsValidation(r.period),this.updateActiveFiltersIndicator(),this.applyFilter()}catch(i){console.error("Chyba p\u0159i aplikov\xe1n\xed ulo\u017een\xe9ho filtru",i)}}deleteSavedFilter(e,i){var r=this;return(0,q.Z)(function*(){i.stopPropagation(),(yield r.modalService.confirm(`Opravdu chcete smazat filtr "${e.name}"?`,"Odstranit filtr","OK","Zru\u0161it","btn-danger","btn-secondary"))&&r.logsService.deleteLogFilter(e.id).subscribe({next:()=>{r.savedFilters=r.savedFilters.filter(l=>l.id!==e.id)},error:l=>{console.error("Chyba p\u0159i maz\xe1n\xed filtru",l)}})})()}};let o=a;return a.\u0275fac=function(i){return new(i||a)(t.Y36(u.qu),t.Y36(h),t.Y36(M.K),t.Y36(v.Z),t.Y36(I._W))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-log-filter"]],inputs:{logType:"logType",showLogLevelFilter:"showLogLevelFilter",showCategoryFilter:"showCategoryFilter"},outputs:{filterChange:"filterChange"},decls:39,vars:14,consts:[[1,"card","mb-4"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0","filter-title",3,"click"],[1,"bi","fs-5","me-2",3,"ngClass"],["class","badge bg-danger ms-2 badge-smaller",4,"ngIf"],[1,"bi","ms-2",3,"ngClass"],[1,"btn","btn-sm","btn-outline-primary","me-4",3,"click"],[1,"bi","bi-save","me-1"],[1,"btn","btn-sm","btn-outline-secondary","me-2",3,"click"],[1,"bi","bi-arrow-counterclockwise","me-1"],[1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"bi","bi-arrow-repeat","me-1"],["class","card-body",4,"ngIf"],["id","saveFilterModal","tabindex","-1","aria-labelledby","saveFilterModalLabel","aria-hidden","true",1,"modal","fade"],[1,"modal-dialog"],[1,"modal-content"],[1,"modal-header"],["id","saveFilterModalLabel",1,"modal-title"],["type","button","data-bs-dismiss","modal","aria-label","Zav\u0159\xedt",1,"btn-close"],[1,"modal-body"],[3,"formGroup"],[1,"mb-3"],["for","filterName",1,"form-label"],["type","text","id","filterName","formControlName","name","required","",1,"form-control"],["class","alert alert-danger",4,"ngIf"],[1,"modal-footer"],["type","button","data-bs-dismiss","modal",1,"btn","btn-secondary"],["type","button",1,"btn","btn-primary",3,"disabled","click"],["class","spinner-border spinner-border-sm me-1","role","status","aria-hidden","true",4,"ngIf"],[1,"badge","bg-danger","ms-2","badge-smaller"],[1,"card-body"],[1,"row"],[1,"col-md-6","col-lg-3","mb-3"],["for","userId",1,"form-label"],["id","userId","formControlName","userId",1,"form-select"],[3,"ngValue"],[3,"value",4,"ngFor","ngForOf"],["for","source",1,"form-label"],["id","source","formControlName","source",1,"form-select"],[3,"ngValue",4,"ngFor","ngForOf"],[4,"ngIf"],["for","maxResults",1,"form-label"],["type","number","id","maxResults","formControlName","maxResults","min","1","max","1000",1,"form-control"],["for","period",1,"form-label"],["id","period","formControlName","period",1,"form-select"],["class","col-md-6 col-lg-3 mb-3",4,"ngIf"],["class","mt-3",4,"ngIf"],[3,"value"],["for","activityType",1,"form-label"],["id","activityType","formControlName","activityType",1,"form-select"],["for","entityName",1,"form-label"],["type","text","id","entityName","formControlName","entityName","placeholder","Nap\u0159. customers, versions...",1,"form-control"],["for","entityId",1,"form-label"],["type","number","id","entityId","formControlName","entityId",1,"form-control"],["for","logLevel",1,"form-label"],["id","logLevel","formControlName","logLevel",1,"form-select"],["for","category",1,"form-label"],["type","text","id","category","formControlName","category","placeholder","Nap\u0159. DISAdmin.Api.Controllers...",1,"form-control"],["for","fromDate",1,"form-label"],["type","datetime-local","id","fromDate","formControlName","fromDate",1,"form-control",3,"ngClass"],["class","invalid-feedback",4,"ngIf"],[1,"invalid-feedback"],["for","toDate",1,"form-label"],["type","datetime-local","id","toDate","formControlName","toDate",1,"form-control",3,"ngClass"],[1,"mt-3"],[1,"saved-filters"],["class","saved-filter",3,"click",4,"ngFor","ngForOf"],[1,"saved-filter",3,"click"],[1,"filter-name"],[1,"btn","btn-sm","btn-link","text-danger",3,"click"],[1,"bi","bi-trash"],[1,"alert","alert-danger"],["role","status","aria-hidden","true",1,"spinner-border","spinner-border-sm","me-1"]],template:function(i,r){1&i&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h5",2),t.NdJ("click",function(){return r.toggleFilterVisibility()}),t._UZ(3,"i",3),t.TgZ(4,"span"),t._uU(5,"Filtr"),t.qZA(),t.YNc(6,U,2,1,"span",4),t._UZ(7,"i",5),t.qZA(),t.TgZ(8,"div")(9,"button",6),t.NdJ("click",function(){return r.openSaveFilterModal()}),t._UZ(10,"i",7),t._uU(11,"Ulo\u017eit filtr "),t.qZA(),t.TgZ(12,"button",8),t.NdJ("click",function(){return r.resetFilter()}),t._UZ(13,"i",9),t._uU(14,"Reset "),t.qZA(),t.TgZ(15,"button",10),t.NdJ("click",function(){return r.refreshData()}),t._UZ(16,"i",11),t._uU(17,"Na\u010d\xedst data "),t.qZA()()(),t.YNc(18,R,31,11,"div",12),t.qZA(),t.TgZ(19,"div",13)(20,"div",14)(21,"div",15)(22,"div",16)(23,"h5",17),t._uU(24,"Ulo\u017eit filtr"),t.qZA(),t._UZ(25,"button",18),t.qZA(),t.TgZ(26,"div",19)(27,"form",20)(28,"div",21)(29,"label",22),t._uU(30,"N\xe1zev filtru"),t.qZA(),t._UZ(31,"input",23),t.qZA(),t.YNc(32,W,2,1,"div",24),t.qZA()(),t.TgZ(33,"div",25)(34,"button",26),t._uU(35,"Zru\u0161it"),t.qZA(),t.TgZ(36,"button",27),t.NdJ("click",function(){return r.saveFilter()}),t.YNc(37,B,1,0,"span",28),t._uU(38," Ulo\u017eit "),t.qZA()()()()()),2&i&&(t.xp6(3),t.Q6J("ngClass",t.WLB(8,X,!r.hasActiveFilters,r.hasActiveFilters)),t.xp6(3),t.Q6J("ngIf",r.hasActiveFilters),t.xp6(1),t.Q6J("ngClass",t.WLB(11,tt,r.isFilterVisible,!r.isFilterVisible)),t.xp6(11),t.Q6J("ngIf",r.isFilterVisible),t.xp6(9),t.Q6J("formGroup",r.saveFilterForm),t.xp6(5),t.Q6J("ngIf",r.error),t.xp6(4),t.Q6J("disabled",r.saveFilterForm.invalid||r.isLoading),t.xp6(1),t.Q6J("ngIf",r.isLoading))},dependencies:[p.mk,p.sg,p.O5,u._Y,u.YN,u.Kr,u.Fj,u.wV,u.EJ,u.JJ,u.JL,u.Q7,u.qQ,u.Fd,u.sg,u.u],styles:[".filter-title[_ngcontent-%COMP%]{cursor:pointer;-webkit-user-select:none;user-select:none;display:flex;align-items:center}.filter-title[_ngcontent-%COMP%]:hover{color:var(--bs-primary)}.badge-smaller[_ngcontent-%COMP%]{font-size:.7rem;padding:.25em .45em}.filter-title[_ngcontent-%COMP%]   i.bi-chevron-down[_ngcontent-%COMP%], .filter-title[_ngcontent-%COMP%]   i.bi-chevron-up[_ngcontent-%COMP%]{transition:transform .2s ease-in-out}.saved-filters[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:.5rem;margin-top:.5rem}.saved-filter[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:rgba(var(--bs-primary-rgb),.1);border:1px solid rgba(var(--bs-primary-rgb),.2);border-radius:4px;padding:.25rem .75rem;cursor:pointer;transition:all .2s}.saved-filter[_ngcontent-%COMP%]:hover{background-color:rgba(var(--bs-primary-rgb),.2)}.filter-name[_ngcontent-%COMP%]{margin-right:.5rem}.btn-link[_ngcontent-%COMP%]{padding:0;font-size:.875rem}@media (prefers-color-scheme: dark){.saved-filter[_ngcontent-%COMP%]{background-color:rgba(var(--bs-primary-rgb),.2);border-color:rgba(var(--bs-primary-rgb),.3)}.saved-filter[_ngcontent-%COMP%]:hover{background-color:rgba(var(--bs-primary-rgb),.3)}}"]}),o})();function et(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"th"),t._uU(2,"U\u017eivatel:"),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA()()),2&o){const n=t.oxw(2);t.xp6(4),t.Oqu(n.log.username||"Syst\xe9m")}}function ot(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"th"),t._uU(2,"Instance:"),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA()()),2&o){const n=t.oxw(2);t.xp6(4),t.Oqu(n.log.username)}}function rt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw(3);t.xp6(1),t.hij("#",n.log.entityId,"")}}function nt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.YNc(2,rt,2,1,"span",18),t.qZA()),2&o){const n=t.oxw(2);t.xp6(1),t.hij(" ",n.log.entityName," "),t.xp6(1),t.Q6J("ngIf",n.log.entityId)}}function it(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function at(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"th"),t._uU(2,"HTTP metoda:"),t.qZA(),t.TgZ(3,"td")(4,"span"),t._uU(5),t.qZA()()()),2&o){const n=t.oxw(3);t.xp6(4),t.Gre("badge rounded-pill ",n.getHttpMethodClass(n.log.method),""),t.xp6(1),t.hij(" ",n.log.method," ")}}function st(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"th"),t._uU(2,"URL cesta:"),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA()()),2&o){const n=t.oxw(3);t.xp6(4),t.Oqu(n.log.endpoint)}}function lt(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"th"),t._uU(2,"Stavov\xfd k\xf3d:"),t.qZA(),t.TgZ(3,"td")(4,"span"),t._uU(5),t.qZA()()()),2&o){const n=t.oxw(3);t.xp6(4),t.Gre("badge rounded-pill ",n.getStatusCodeClass(n.log.statusCode),""),t.xp6(1),t.hij(" ",n.log.statusCode," ")}}function ct(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"th"),t._uU(2,"Doba odezvy:"),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA()()),2&o){const n=t.oxw(3);t.xp6(4),t.hij("",n.log.responseTimeMs," ms")}}function dt(o,a){if(1&o&&(t.TgZ(0,"div",11)(1,"h6",12),t._UZ(2,"i",20),t._uU(3,"Detaily API po\u017eadavku "),t.qZA(),t.TgZ(4,"div",14)(5,"div",15)(6,"div",16)(7,"table",17),t.YNc(8,at,6,4,"tr",18),t.YNc(9,st,5,1,"tr",18),t.qZA()(),t.TgZ(10,"div",16)(11,"table",17),t.YNc(12,lt,6,4,"tr",18),t.YNc(13,ct,5,1,"tr",18),t.qZA()()()()()),2&o){const n=t.oxw(2);t.xp6(8),t.Q6J("ngIf",n.log.method),t.xp6(1),t.Q6J("ngIf",n.log.endpoint),t.xp6(3),t.Q6J("ngIf",n.log.statusCode),t.xp6(1),t.Q6J("ngIf",n.log.responseTimeMs)}}function gt(o,a){if(1&o&&(t.TgZ(0,"div",11)(1,"h6",12),t._UZ(2,"i",21),t._uU(3,"Popis "),t.qZA(),t.TgZ(4,"div",14)(5,"p",22),t._uU(6),t.qZA()()()),2&o){const n=t.oxw(2);t.xp6(6),t.Oqu(n.log.description)}}function ut(o,a){if(1&o&&(t.TgZ(0,"div",11)(1,"h6",12),t._UZ(2,"i",23),t._uU(3,"Dodate\u010dn\xe9 informace "),t.qZA(),t.TgZ(4,"div",14)(5,"pre"),t._uU(6),t.qZA()()()),2&o){const n=t.oxw(2);t.xp6(6),t.Oqu(n.log.additionalInfo)}}function pt(o,a){if(1&o&&(t.TgZ(0,"div",10)(1,"div",11)(2,"h6",12),t._UZ(3,"i",13),t._uU(4,"Z\xe1kladn\xed informace "),t.qZA(),t.TgZ(5,"div",14)(6,"div",15)(7,"div",16)(8,"table",17)(9,"tr")(10,"th"),t._uU(11,"ID:"),t.qZA(),t.TgZ(12,"td"),t._uU(13),t.qZA()(),t.TgZ(14,"tr")(15,"th"),t._uU(16,"\u010cas:"),t.qZA(),t.TgZ(17,"td"),t._uU(18),t.ALo(19,"date"),t.qZA()(),t.YNc(20,et,5,1,"tr",18),t.YNc(21,ot,5,1,"tr",18),t.TgZ(22,"tr")(23,"th"),t._uU(24,"IP adresa:"),t.qZA(),t.TgZ(25,"td"),t._uU(26),t.qZA()()()(),t.TgZ(27,"div",16)(28,"table",17)(29,"tr")(30,"th"),t._uU(31,"Typ aktivity:"),t.qZA(),t.TgZ(32,"td")(33,"span"),t._uU(34),t.qZA()()(),t.TgZ(35,"tr")(36,"th"),t._uU(37,"Zdroj:"),t.qZA(),t.TgZ(38,"td")(39,"span"),t._uU(40),t.qZA()()(),t.TgZ(41,"tr")(42,"th"),t._uU(43,"Entita:"),t.qZA(),t.TgZ(44,"td"),t.YNc(45,nt,3,2,"span",18),t.YNc(46,it,2,0,"span",18),t.qZA()()()()()()(),t.YNc(47,dt,14,4,"div",19),t.YNc(48,gt,7,1,"div",19),t.YNc(49,ut,7,1,"div",19),t.qZA()),2&o){const n=t.oxw();t.xp6(13),t.Oqu(n.log.id),t.xp6(5),t.Oqu(t.xi3(19,18,n.log.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(2),t.Q6J("ngIf",n.isDISAdminLog()),t.xp6(1),t.Q6J("ngIf",n.isDISApiLog()),t.xp6(5),t.Oqu(n.log.ipAddress),t.xp6(7),t.Gre("badge rounded-pill ",n.getActivityTypeClass(n.log.activityType),""),t.xp6(1),t.hij(" ",n.getLocalizedActivityType(n.log.activityType)," "),t.xp6(5),t.Gre("badge rounded-pill ",n.getSourceClass(n.log.source),""),t.xp6(1),t.hij(" ",n.getLocalizedSource(n.log.source)," "),t.xp6(5),t.Q6J("ngIf",n.log.entityName),t.xp6(1),t.Q6J("ngIf",!n.log.entityName),t.xp6(1),t.Q6J("ngIf",n.log.method||n.log.endpoint||n.log.statusCode||n.log.responseTimeMs),t.xp6(1),t.Q6J("ngIf",n.log.description),t.xp6(1),t.Q6J("ngIf",n.log.additionalInfo)}}let L=(()=>{const a=class{constructor(){this.log=null,this.modalId="logDetailModal",this.title="Detail logu",this.close=new t.vpe}closeDetail(){this.close.emit()}getLocalizedActivityType(e){return f.getLocalizedName(e)}getLocalizedSource(e){return C.getLocalizedName(e)}getActivityTypeClass(e){switch(e){case"Login":return"bg-success";case"Logout":default:return"bg-secondary";case"Create":case"ApiAccess":return"bg-primary";case"Update":case"PasswordChange":return"bg-info";case"Delete":return"bg-danger";case"Export":case"Import":return"bg-warning"}}getSourceClass(e){switch(e){case d.DISAdmin:return"bg-primary";case d.DISApi:return"bg-info";default:return"bg-secondary"}}getHttpMethodClass(e){switch(e?.toUpperCase()){case"GET":return"bg-success";case"POST":return"bg-primary";case"PUT":case"PATCH":return"bg-info";case"DELETE":return"bg-danger";default:return"bg-secondary"}}getStatusCodeClass(e){return e?e>=200&&e<300?"bg-success":e>=300&&e<400?"bg-info":e>=400&&e<500?"bg-warning":e>=500?"bg-danger":"bg-secondary":"bg-secondary"}isDISAdminLog(){return this.log?.source===d.DISAdmin}isDISApiLog(){return this.log?.source===d.DISApi}};let o=a;return a.\u0275fac=function(i){return new(i||a)},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-log-detail"]],inputs:{log:"log",modalId:"modalId",title:"title"},outputs:{close:"close"},decls:12,vars:5,consts:[["tabindex","-1","aria-hidden","true",1,"modal","fade",3,"id"],[1,"modal-dialog","modal-lg"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title",3,"id"],["type","button","data-bs-dismiss","modal","aria-label","Zav\u0159\xedt",1,"btn-close",3,"click"],["class","modal-body",4,"ngIf"],[1,"modal-footer"],["type","button","data-bs-dismiss","modal",1,"btn","btn-secondary",3,"click"],[1,"bi","bi-x-circle","me-1"],[1,"modal-body"],[1,"section-card"],[1,"section-header"],[1,"bi","bi-info-circle","me-2"],[1,"section-body"],[1,"row"],[1,"col-md-6"],[1,"table","table-sm","mb-0"],[4,"ngIf"],["class","section-card",4,"ngIf"],[1,"bi","bi-hdd-network","me-2"],[1,"bi","bi-card-text","me-2"],[1,"mb-0"],[1,"bi","bi-file-earmark-text","me-2"]],template:function(i,r){1&i&&(t.TgZ(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h5",4),t._uU(5),t.qZA(),t.TgZ(6,"button",5),t.NdJ("click",function(){return r.closeDetail()}),t.qZA()(),t.YNc(7,pt,50,21,"div",6),t.TgZ(8,"div",7)(9,"button",8),t.NdJ("click",function(){return r.closeDetail()}),t._UZ(10,"i",9),t._uU(11,"Zav\u0159\xedt "),t.qZA()()()()()),2&i&&(t.Q6J("id",r.modalId),t.uIk("aria-labelledby",r.modalId+"Label"),t.xp6(4),t.Q6J("id",r.modalId+"Label"),t.xp6(1),t.Oqu(r.title),t.xp6(2),t.Q6J("ngIf",r.log))},dependencies:[p.O5,p.uU],styles:[".section-card[_ngcontent-%COMP%]{border-radius:.375rem;margin-bottom:1rem;box-shadow:0 2px 4px #0000000d;border:1px solid rgba(0,0,0,.125)}.section-header[_ngcontent-%COMP%]{padding:.75rem 1rem;background-color:#00000008;border-bottom:1px solid rgba(0,0,0,.125);font-weight:500;font-size:1rem;margin-bottom:0;border-radius:.375rem .375rem 0 0}.section-body[_ngcontent-%COMP%]{padding:1rem}.table-sm[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{width:40%;font-weight:500}.table-sm[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{width:60%}.badge[_ngcontent-%COMP%]{font-weight:500;padding:.35em .65em}@media (prefers-color-scheme: dark){.section-card[_ngcontent-%COMP%]{border-color:#ffffff20;background-color:#1a2530}.section-header[_ngcontent-%COMP%]{background-color:#111921;border-color:#ffffff20;color:#fff}}pre[_ngcontent-%COMP%]{white-space:pre-wrap;font-size:.85rem;max-height:300px;overflow-y:auto;margin-bottom:0}.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.bg-primary[_ngcontent-%COMP%]{background-color:#007bff!important}.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}"]}),o})();function _t(o,a){1&o&&(t.TgZ(0,"tr")(1,"td",8)(2,"div",9)(3,"span",10),t._uU(4,"Na\u010d\xedt\xe1n\xed..."),t.qZA()()()())}function mt(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"td",11),t._uU(2),t.qZA()()),2&o){const n=t.oxw();t.xp6(2),t.hij(" ",n.error," ")}}function ft(o,a){1&o&&(t.TgZ(0,"tr")(1,"td",8),t._uU(2," Nebyly nalezeny \u017e\xe1dn\xe9 auditn\xed logy "),t.qZA()())}function bt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw(2).$implicit;t.xp6(1),t.hij("#",n.entityId,"")}}function ht(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.YNc(2,bt,2,1,"span",5),t.qZA()),2&o){const n=t.oxw().$implicit;t.xp6(1),t.hij(" ",n.entityName," "),t.xp6(1),t.Q6J("ngIf",n.entityId)}}function vt(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function yt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw().$implicit,e=t.oxw();t.Gre("badge rounded-pill ",e.getHttpMethodClass(n.method),""),t.xp6(1),t.hij(" ",n.method," ")}}function Ct(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function xt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw().$implicit,e=t.oxw();t.Gre("badge rounded-pill ",e.getStatusCodeClass(n.statusCode),""),t.xp6(1),t.hij(" ",n.statusCode," ")}}function Zt(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function Tt(o,a){if(1&o){const n=t.EpF();t.TgZ(0,"tr")(1,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(2),t.ALo(3,"date"),t.qZA(),t.TgZ(4,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(5),t.qZA(),t.TgZ(6,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.TgZ(7,"span"),t._uU(8),t.qZA()(),t.TgZ(9,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.YNc(10,ht,3,2,"span",5),t.YNc(11,vt,2,0,"span",5),t.qZA(),t.TgZ(12,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.YNc(13,yt,2,4,"span",13),t.YNc(14,Ct,2,0,"span",5),t.qZA(),t.TgZ(15,"td",14),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(16),t.qZA(),t.TgZ(17,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.YNc(18,xt,2,4,"span",13),t.YNc(19,Zt,2,0,"span",5),t.qZA(),t.TgZ(20,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(21),t.qZA(),t.TgZ(22,"td")(23,"button",15),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._UZ(24,"i",16),t.qZA()()()}if(2&o){const n=a.$implicit,e=t.oxw();t.xp6(2),t.Oqu(t.xi3(3,14,n.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(3),t.Oqu(n.username||"Syst\xe9m"),t.xp6(2),t.Gre("badge rounded-pill ",e.getActivityTypeClass(n.activityType),""),t.xp6(1),t.hij(" ",e.getLocalizedActivityType(n.activityType)," "),t.xp6(2),t.Q6J("ngIf",n.entityName),t.xp6(1),t.Q6J("ngIf",!n.entityName),t.xp6(2),t.Q6J("ngIf",n.method),t.xp6(1),t.Q6J("ngIf",!n.method),t.xp6(2),t.hij(" ",n.endpoint||"-"," "),t.xp6(2),t.Q6J("ngIf",n.statusCode),t.xp6(1),t.Q6J("ngIf",!n.statusCode),t.xp6(2),t.hij(" ",n.responseTimeMs?n.responseTimeMs+" ms":"-"," ")}}let Dt=(()=>{const a=class{constructor(e,i){this.logsService=e,this.modalService=i,this.logs=[],this.currentFilter={maxResults:100,source:d.DISAdmin},this.isLoading=!1,this.error=null,this.selectedLog=null}ngOnInit(){this.loadCurrentFilterState()}ngAfterViewInit(){setTimeout(()=>{this.loadLogs()},0)}loadCurrentFilterState(){try{const i=localStorage.getItem("current_filter_activity");if(i){const r=JSON.parse(i);if(r.period&&"custom"!==r.period){const s=new Date,l=new Date(s);l.setHours(23,59,59,999);const c=new Date(s);c.setDate(s.getDate()-Number(r.period)),c.setHours(0,0,0,0),r.fromDate=c,r.toDate=l}else r.fromDate&&(r.fromDate=new Date(r.fromDate)),r.toDate&&(r.toDate=new Date(r.toDate));this.currentFilter={...this.currentFilter,...r},console.log("Na\u010dten filtr pro auditn\xed logy:",this.currentFilter)}}catch(e){console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu filtru pro auditn\xed logy",e)}}loadLogs(){this.isLoading=!0,this.error=null,this.logsService.getActivityLogs(this.currentFilter).subscribe({next:e=>{this.logs=e,this.isLoading=!1},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed auditn\xedch log\u016f",e),this.error="Nepoda\u0159ilo se na\u010d\xedst auditn\xed logy",this.isLoading=!1}})}onFilterChange(e){this.currentFilter=e,this.loadLogs()}showLogDetail(e){this.selectedLog=e,this.modalService.open("logDetailModal")}closeLogDetail(){this.selectedLog=null,this.modalService.close("logDetailModal")}getLocalizedActivityType(e){return f.getLocalizedName(e)}getLocalizedSource(e){return C.getLocalizedName(e)}getActivityTypeClass(e){switch(e){case"Login":return"bg-success";case"Logout":default:return"bg-secondary";case"Create":case"ApiAccess":return"bg-primary";case"Update":case"PasswordChange":return"bg-info";case"Delete":return"bg-danger";case"Export":case"Import":return"bg-warning"}}getSourceClass(e){switch(e){case d.DISAdmin:return"bg-primary";case d.DISApi:return"bg-info";default:return"bg-secondary"}}getHttpMethodClass(e){switch(e.toUpperCase()){case"GET":return"bg-success";case"POST":return"bg-primary";case"PUT":case"PATCH":return"bg-info";case"DELETE":return"bg-danger";default:return"bg-secondary"}}getStatusCodeClass(e){return e?e>=200&&e<300?"bg-success":e>=300&&e<400?"bg-info":e>=400&&e<500?"bg-warning":e>=500?"bg-danger":"bg-secondary":"bg-secondary"}};let o=a;return a.\u0275fac=function(i){return new(i||a)(t.Y36(h),t.Y36(v.Z))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-activity-logs"]],decls:28,vars:7,consts:[["logType","activity",3,"filterChange"],[1,"table-responsive"],[1,"table","table-hover","mb-0"],[1,"table-header-override","dark-header"],[1,"dark-header-row"],[4,"ngIf"],[4,"ngFor","ngForOf"],[3,"log","modalId","title","close"],["colspan","9",1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],["colspan","9",1,"text-center","text-danger","py-4"],[1,"cursor-pointer",3,"click"],[3,"class",4,"ngIf"],[1,"text-truncate","cursor-pointer",2,"max-width","200px",3,"click"],["title","Zobrazit detail",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-info-circle"]],template:function(i,r){1&i&&(t.TgZ(0,"app-log-filter",0),t.NdJ("filterChange",function(l){return r.onFilterChange(l)}),t.qZA(),t.TgZ(1,"div",1)(2,"table",2)(3,"thead",3)(4,"tr",4)(5,"th"),t._uU(6,"\u010cas"),t.qZA(),t.TgZ(7,"th"),t._uU(8,"U\u017eivatel"),t.qZA(),t.TgZ(9,"th"),t._uU(10,"Typ"),t.qZA(),t.TgZ(11,"th"),t._uU(12,"Entita"),t.qZA(),t.TgZ(13,"th"),t._uU(14,"Metoda"),t.qZA(),t.TgZ(15,"th"),t._uU(16,"Cesta"),t.qZA(),t.TgZ(17,"th"),t._uU(18,"K\xf3d"),t.qZA(),t.TgZ(19,"th"),t._uU(20,"Trv\xe1n\xed"),t.qZA(),t._UZ(21,"th"),t.qZA()(),t.TgZ(22,"tbody"),t.YNc(23,_t,5,0,"tr",5),t.YNc(24,mt,3,1,"tr",5),t.YNc(25,ft,3,0,"tr",5),t.YNc(26,Tt,25,17,"tr",6),t.qZA()()(),t.TgZ(27,"app-log-detail",7),t.NdJ("close",function(){return r.closeLogDetail()}),t.qZA()),2&i&&(t.xp6(23),t.Q6J("ngIf",r.isLoading),t.xp6(1),t.Q6J("ngIf",!r.isLoading&&r.error),t.xp6(1),t.Q6J("ngIf",!r.isLoading&&!r.error&&0===r.logs.length),t.xp6(1),t.Q6J("ngForOf",r.logs),t.xp6(1),t.Q6J("log",r.selectedLog)("modalId","logDetailModal")("title","Detail auditn\xedho logu"))},dependencies:[p.sg,p.O5,Z,L,p.uU],styles:[".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:500}.table-responsive[_ngcontent-%COMP%]{border-radius:6px!important;overflow:hidden!important;box-shadow:0 2px 4px #0000001a!important}.table[_ngcontent-%COMP%]{margin:0!important;border-collapse:collapse!important}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{padding-left:1rem!important}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{padding-left:1rem!important;border-left:none!important}.badge[_ngcontent-%COMP%]{font-weight:500;padding:.35em .65em}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-left:none!important}.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#007bff13!important}body.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#1a2530!important}.stack-trace[_ngcontent-%COMP%]{white-space:pre-wrap;font-size:.85rem;max-height:300px;overflow-y:auto}.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.bg-primary[_ngcontent-%COMP%]{background-color:#007bff!important}.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}@media (prefers-color-scheme: dark){.table[_ngcontent-%COMP%]{color:var(--bs-light)}.bg-warning[_ngcontent-%COMP%]{color:#212529}pre[_ngcontent-%COMP%]{color:var(--bs-light)}}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:first-child{border-top-left-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:last-child{border-top-right-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{background-color:var(--bs-table-bg, #111921)!important;color:#fff!important;border-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table{min-width:100%}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td>.badge{padding:.35em .65em;font-size:.875rem}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td:nth-child(5), [_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:nth-child(5){max-width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{padding:.5rem;font-size:1rem}"]}),o})();function At(o,a){1&o&&(t.TgZ(0,"tr")(1,"td",16)(2,"div",17)(3,"span",18),t._uU(4,"Na\u010d\xedt\xe1n\xed..."),t.qZA()()()())}function Lt(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"td",19),t._uU(2),t.qZA()()),2&o){const n=t.oxw();t.xp6(2),t.hij(" ",n.error," ")}}function wt(o,a){1&o&&(t.TgZ(0,"tr")(1,"td",16),t._uU(2," Nebyly nalezeny \u017e\xe1dn\xe9 logy aplikace "),t.qZA()())}function Ot(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw().$implicit,e=t.oxw();t.Gre("badge rounded-pill ",e.getStatusCodeClass(n.statusCode),""),t.xp6(1),t.hij(" ",n.statusCode," ")}}function Ft(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function qt(o,a){if(1&o){const n=t.EpF();t.TgZ(0,"tr")(1,"td",20),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(2),t.ALo(3,"date"),t.qZA(),t.TgZ(4,"td",20),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.TgZ(5,"span"),t._uU(6),t.qZA()(),t.TgZ(7,"td",21),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(8),t.qZA(),t.TgZ(9,"td",22),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(10),t.qZA(),t.TgZ(11,"td",20),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.TgZ(12,"span"),t._uU(13),t.qZA()(),t.TgZ(14,"td",22),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(15),t.qZA(),t.TgZ(16,"td",20),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(17),t.qZA(),t.TgZ(18,"td",20),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.YNc(19,Ot,2,4,"span",23),t.YNc(20,Ft,2,0,"span",5),t.qZA(),t.TgZ(21,"td",20),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(22),t.qZA(),t.TgZ(23,"td")(24,"button",24),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._UZ(25,"i",25),t.qZA()()()}if(2&o){const n=a.$implicit,e=t.oxw();t.xp6(2),t.Oqu(t.xi3(3,16,n.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(3),t.Gre("badge rounded-pill ",e.getLogLevelClass(n.logLevel),""),t.xp6(1),t.hij(" ",e.getLocalizedLogLevel(n.logLevel)," "),t.xp6(2),t.Oqu(n.message),t.xp6(2),t.Oqu(n.category||"-"),t.xp6(2),t.Gre("badge rounded-pill ",e.getSourceClass(n.source),""),t.xp6(1),t.hij(" ",e.getLocalizedSource(n.source)," "),t.xp6(2),t.Oqu(n.requestPath||"-"),t.xp6(2),t.Oqu(n.requestMethod||"-"),t.xp6(2),t.Q6J("ngIf",n.statusCode),t.xp6(1),t.Q6J("ngIf",!n.statusCode),t.xp6(2),t.Oqu(n.username||"Syst\xe9m")}}function Mt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw(2);t.Gre("badge rounded-pill ",n.getStatusCodeClass(n.selectedLog.statusCode),""),t.xp6(1),t.hij(" ",n.selectedLog.statusCode," ")}}function It(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function Ut(o,a){if(1&o&&(t.TgZ(0,"div",27)(1,"div",30)(2,"h6"),t._uU(3,"Stack Trace"),t.qZA(),t.TgZ(4,"div",31)(5,"div",32)(6,"pre",35),t._uU(7),t.qZA()()()()()),2&o){const n=t.oxw(2);t.xp6(7),t.Oqu(n.selectedLog.stackTrace)}}function Pt(o,a){if(1&o&&(t.TgZ(0,"div",27)(1,"div",36)(2,"h6"),t._uU(3,"Dodate\u010dn\xe9 informace"),t.qZA(),t.TgZ(4,"div",31)(5,"div",32)(6,"pre",33),t._uU(7),t.qZA()()()()()),2&o){const n=t.oxw(2);t.xp6(7),t.Oqu(n.selectedLog.additionalInfo)}}function St(o,a){if(1&o&&(t.TgZ(0,"div",26)(1,"div",27)(2,"div",28)(3,"h6"),t._uU(4,"Z\xe1kladn\xed informace"),t.qZA(),t.TgZ(5,"table",29)(6,"tr")(7,"th"),t._uU(8,"ID:"),t.qZA(),t.TgZ(9,"td"),t._uU(10),t.qZA()(),t.TgZ(11,"tr")(12,"th"),t._uU(13,"\u010cas:"),t.qZA(),t.TgZ(14,"td"),t._uU(15),t.ALo(16,"date"),t.qZA()(),t.TgZ(17,"tr")(18,"th"),t._uU(19,"U\u017eivatel:"),t.qZA(),t.TgZ(20,"td"),t._uU(21),t.qZA()(),t.TgZ(22,"tr")(23,"th"),t._uU(24,"IP adresa:"),t.qZA(),t.TgZ(25,"td"),t._uU(26),t.qZA()(),t.TgZ(27,"tr")(28,"th"),t._uU(29,"Zdroj:"),t.qZA(),t.TgZ(30,"td")(31,"span"),t._uU(32),t.qZA()()()()(),t.TgZ(33,"div",28)(34,"h6"),t._uU(35,"Detaily po\u017eadavku"),t.qZA(),t.TgZ(36,"table",29)(37,"tr")(38,"th"),t._uU(39,"Cesta:"),t.qZA(),t.TgZ(40,"td"),t._uU(41),t.qZA()(),t.TgZ(42,"tr")(43,"th"),t._uU(44,"Metoda:"),t.qZA(),t.TgZ(45,"td"),t._uU(46),t.qZA()(),t.TgZ(47,"tr")(48,"th"),t._uU(49,"Stavov\xfd k\xf3d:"),t.qZA(),t.TgZ(50,"td"),t.YNc(51,Mt,2,4,"span",23),t.YNc(52,It,2,0,"span",5),t.qZA()()()()(),t.TgZ(53,"div",27)(54,"div",30)(55,"h6"),t._uU(56,"Zpr\xe1va chyby"),t.qZA(),t.TgZ(57,"div",31)(58,"div",32)(59,"p",33),t._uU(60),t.qZA()()()()(),t.YNc(61,Ut,8,1,"div",34),t.YNc(62,Pt,8,1,"div",34),t.qZA()),2&o){const n=t.oxw();t.xp6(10),t.Oqu(n.selectedLog.id),t.xp6(5),t.Oqu(t.xi3(16,15,n.selectedLog.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(6),t.Oqu(n.selectedLog.username||"Syst\xe9m"),t.xp6(5),t.Oqu(n.selectedLog.ipAddress),t.xp6(5),t.Gre("badge rounded-pill ",n.getSourceClass(n.selectedLog.source),""),t.xp6(1),t.hij(" ",n.getLocalizedSource(n.selectedLog.source)," "),t.xp6(9),t.Oqu(n.selectedLog.requestPath||"-"),t.xp6(5),t.Oqu(n.selectedLog.requestMethod||"-"),t.xp6(5),t.Q6J("ngIf",n.selectedLog.statusCode),t.xp6(1),t.Q6J("ngIf",!n.selectedLog.statusCode),t.xp6(8),t.Oqu(n.selectedLog.message),t.xp6(1),t.Q6J("ngIf",n.selectedLog.stackTrace),t.xp6(1),t.Q6J("ngIf",n.selectedLog.additionalInfo)}}let kt=(()=>{const a=class{constructor(e,i){this.logsService=e,this.modalService=i,this.logs=[],this.currentFilter={maxResults:100},this.isLoading=!1,this.error=null,this.selectedLog=null}ngOnInit(){this.loadCurrentFilterState()}ngAfterViewInit(){setTimeout(()=>{this.loadLogs()},0)}loadCurrentFilterState(){try{const i=localStorage.getItem("current_filter_error");if(i){const r=JSON.parse(i);if(r.period&&"custom"!==r.period){const s=new Date,l=new Date(s);l.setHours(23,59,59,999);const c=new Date(s);c.setDate(s.getDate()-Number(r.period)),c.setHours(0,0,0,0),r.fromDate=c,r.toDate=l}else r.fromDate&&(r.fromDate=new Date(r.fromDate)),r.toDate&&(r.toDate=new Date(r.toDate));this.currentFilter={...this.currentFilter,...r},console.log("Na\u010dten filtr pro logy chyb:",this.currentFilter)}}catch(e){console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu filtru pro logy chyb",e)}}loadLogs(){this.isLoading=!0,this.error=null,this.logsService.getErrorLogs(this.currentFilter).subscribe({next:e=>{this.logs=e,this.isLoading=!1},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed log\u016f chyb",e),this.error="Nepoda\u0159ilo se na\u010d\xedst logy chyb",this.isLoading=!1}})}onFilterChange(e){this.currentFilter=e,this.loadLogs()}showLogDetail(e){this.selectedLog=e,this.modalService.open("errorLogDetailModal")}closeLogDetail(){this.selectedLog=null,this.modalService.close("errorLogDetailModal")}getLocalizedSource(e){switch(e){case"DISAdmin":return"DISAdmin";case"DISApi":return"DIS API";default:return e}}getSourceClass(e){switch(e){case"DISAdmin":return"bg-primary";case"DISApi":return"bg-info";default:return"bg-secondary"}}getStatusCodeClass(e){return e?e>=500?"bg-danger":e>=400?"bg-warning":e>=300?"bg-info":e>=200?"bg-success":"bg-secondary":"bg-secondary"}getLocalizedLogLevel(e){return x.getLocalizedName(e)}getLogLevelClass(e){return x.getLogLevelClass(e)}};let o=a;return a.\u0275fac=function(i){return new(i||a)(t.Y36(h),t.Y36(v.Z))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-error-logs"]],decls:40,vars:7,consts:[["logType","error",3,"showLogLevelFilter","showCategoryFilter","filterChange"],[1,"table-responsive"],[1,"table","table-hover","mb-0"],[1,"table-header-override","dark-header"],[1,"dark-header-row"],[4,"ngIf"],[4,"ngFor","ngForOf"],["id","errorLogDetailModal","tabindex","-1","aria-labelledby","errorLogDetailModalLabel","aria-hidden","true",1,"modal","fade"],[1,"modal-dialog","modal-lg"],[1,"modal-content"],[1,"modal-header"],["id","errorLogDetailModalLabel",1,"modal-title"],["type","button","data-bs-dismiss","modal","aria-label","Zav\u0159\xedt",1,"btn-close",3,"click"],["class","modal-body",4,"ngIf"],[1,"modal-footer"],["type","button","data-bs-dismiss","modal",1,"btn","btn-secondary",3,"click"],["colspan","10",1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],["colspan","10",1,"text-center","text-danger","py-4"],[1,"cursor-pointer",3,"click"],[1,"text-truncate","cursor-pointer",2,"max-width","250px",3,"click"],[1,"text-truncate","cursor-pointer",2,"max-width","150px",3,"click"],[3,"class",4,"ngIf"],["title","Zobrazit detail",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-info-circle"],[1,"modal-body"],[1,"row"],[1,"col-md-6","mb-3"],[1,"table","table-sm"],[1,"col-12","mb-3"],[1,"card"],[1,"card-body"],[1,"mb-0"],["class","row",4,"ngIf"],[1,"mb-0","stack-trace"],[1,"col-12"]],template:function(i,r){1&i&&(t.TgZ(0,"app-log-filter",0),t.NdJ("filterChange",function(l){return r.onFilterChange(l)}),t.qZA(),t.TgZ(1,"div",1)(2,"table",2)(3,"thead",3)(4,"tr",4)(5,"th"),t._uU(6,"\u010cas"),t.qZA(),t.TgZ(7,"th"),t._uU(8,"\xdarove\u0148"),t.qZA(),t.TgZ(9,"th"),t._uU(10,"Zpr\xe1va"),t.qZA(),t.TgZ(11,"th"),t._uU(12,"Kategorie"),t.qZA(),t.TgZ(13,"th"),t._uU(14,"Zdroj"),t.qZA(),t.TgZ(15,"th"),t._uU(16,"Cesta"),t.qZA(),t.TgZ(17,"th"),t._uU(18,"Metoda"),t.qZA(),t.TgZ(19,"th"),t._uU(20,"K\xf3d"),t.qZA(),t.TgZ(21,"th"),t._uU(22,"U\u017eivatel"),t.qZA(),t._UZ(23,"th"),t.qZA()(),t.TgZ(24,"tbody"),t.YNc(25,At,5,0,"tr",5),t.YNc(26,Lt,3,1,"tr",5),t.YNc(27,wt,3,0,"tr",5),t.YNc(28,qt,26,19,"tr",6),t.qZA()()(),t.TgZ(29,"div",7)(30,"div",8)(31,"div",9)(32,"div",10)(33,"h5",11),t._uU(34,"Detail logu chyby"),t.qZA(),t.TgZ(35,"button",12),t.NdJ("click",function(){return r.closeLogDetail()}),t.qZA()(),t.YNc(36,St,63,18,"div",13),t.TgZ(37,"div",14)(38,"button",15),t.NdJ("click",function(){return r.closeLogDetail()}),t._uU(39,"Zav\u0159\xedt"),t.qZA()()()()()),2&i&&(t.Q6J("showLogLevelFilter",!0)("showCategoryFilter",!0),t.xp6(25),t.Q6J("ngIf",r.isLoading),t.xp6(1),t.Q6J("ngIf",!r.isLoading&&r.error),t.xp6(1),t.Q6J("ngIf",!r.isLoading&&!r.error&&0===r.logs.length),t.xp6(1),t.Q6J("ngForOf",r.logs),t.xp6(8),t.Q6J("ngIf",r.selectedLog))},dependencies:[p.sg,p.O5,Z,p.uU],styles:[".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:500}.table-responsive[_ngcontent-%COMP%]{border-radius:6px!important;overflow:hidden!important;box-shadow:0 2px 4px #0000001a!important}.table[_ngcontent-%COMP%]{margin:0!important;border-collapse:collapse!important}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{padding-left:1rem!important}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{padding-left:1rem!important;border-left:none!important}.badge[_ngcontent-%COMP%]{font-weight:500;padding:.35em .65em}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-left:none!important}.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#007bff13!important}body.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#1a2530!important}.stack-trace[_ngcontent-%COMP%]{white-space:pre-wrap;font-size:.85rem;max-height:300px;overflow-y:auto}.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.bg-primary[_ngcontent-%COMP%]{background-color:#007bff!important}.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}@media (prefers-color-scheme: dark){.table[_ngcontent-%COMP%]{color:var(--bs-light)}.bg-warning[_ngcontent-%COMP%]{color:#212529}pre[_ngcontent-%COMP%]{color:var(--bs-light)}}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:first-child{border-top-left-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:last-child{border-top-right-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{background-color:var(--bs-table-bg, #111921)!important;color:#fff!important;border-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table{min-width:100%}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td>.badge{padding:.35em .65em;font-size:.875rem}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td:nth-child(5), [_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:nth-child(5){max-width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{padding:.5rem;font-size:1rem}"]}),o})();function Nt(o,a){1&o&&(t.TgZ(0,"tr")(1,"td",8)(2,"div",9)(3,"span",10),t._uU(4,"Na\u010d\xedt\xe1n\xed..."),t.qZA()()()())}function Jt(o,a){if(1&o&&(t.TgZ(0,"tr")(1,"td",11),t._uU(2),t.qZA()()),2&o){const n=t.oxw();t.xp6(2),t.hij(" ",n.error," ")}}function Et(o,a){1&o&&(t.TgZ(0,"tr")(1,"td",8),t._uU(2," Nebyly nalezeny \u017e\xe1dn\xe9 logy DIS API "),t.qZA()())}function Qt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&o){const n=t.oxw(2).$implicit;t.xp6(1),t.hij("#",n.entityId,"")}}function Yt(o,a){if(1&o&&(t.TgZ(0,"span"),t._uU(1),t.YNc(2,Qt,2,1,"span",5),t.qZA()),2&o){const n=t.oxw().$implicit;t.xp6(1),t.hij(" ",n.entityName," "),t.xp6(1),t.Q6J("ngIf",n.entityId)}}function Ht(o,a){1&o&&(t.TgZ(0,"span"),t._uU(1,"-"),t.qZA())}function $t(o,a){if(1&o){const n=t.EpF();t.TgZ(0,"tr")(1,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(2),t.ALo(3,"date"),t.qZA(),t.TgZ(4,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(5),t.qZA(),t.TgZ(6,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.YNc(7,Yt,3,2,"span",5),t.YNc(8,Ht,2,0,"span",5),t.qZA(),t.TgZ(9,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.TgZ(10,"span"),t._uU(11),t.qZA()(),t.TgZ(12,"td",13),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(13),t.qZA(),t.TgZ(14,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t.TgZ(15,"span"),t._uU(16),t.qZA()(),t.TgZ(17,"td",12),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._uU(18),t.qZA(),t.TgZ(19,"td")(20,"button",14),t.NdJ("click",function(){const r=t.CHM(n).$implicit,s=t.oxw();return t.KtG(s.showLogDetail(r))}),t._UZ(21,"i",15),t.qZA()()()}if(2&o){const n=a.$implicit,e=t.oxw();t.xp6(2),t.Oqu(t.xi3(3,14,n.timestamp,"dd.MM.yyyy HH:mm:ss")),t.xp6(3),t.Oqu(n.username),t.xp6(2),t.Q6J("ngIf",n.entityName),t.xp6(1),t.Q6J("ngIf",!n.entityName),t.xp6(2),t.Gre("badge rounded-pill ",e.getHttpMethodClass(n.method||""),""),t.xp6(1),t.hij(" ",n.method||"-"," "),t.xp6(2),t.Oqu(n.endpoint||"-"),t.xp6(2),t.Gre("badge rounded-pill ",e.getStatusCodeClass(n.statusCode),""),t.xp6(1),t.hij(" ",n.statusCode||"-"," "),t.xp6(2),t.hij(" ",n.responseTimeMs?n.responseTimeMs+" ms":"-"," ")}}let zt=(()=>{const a=class{constructor(e,i){this.logsService=e,this.modalService=i,this.logs=[],this.currentFilter={maxResults:100,source:d.DISApi},this.isLoading=!1,this.error=null,this.selectedLog=null}ngOnInit(){this.loadCurrentFilterState()}ngAfterViewInit(){setTimeout(()=>{this.loadLogs()},0)}loadCurrentFilterState(){try{const i=localStorage.getItem("current_filter_api");if(i){const r=JSON.parse(i);if(r.period&&"custom"!==r.period){const s=new Date,l=new Date(s);l.setHours(23,59,59,999);const c=new Date(s);c.setDate(s.getDate()-Number(r.period)),c.setHours(0,0,0,0),r.fromDate=c,r.toDate=l}else r.fromDate&&(r.fromDate=new Date(r.fromDate)),r.toDate&&(r.toDate=new Date(r.toDate));this.currentFilter={...this.currentFilter,...r},console.log("Na\u010dten filtr pro logy DIS API:",this.currentFilter)}}catch(e){console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu filtru pro logy DIS API",e)}}loadLogs(){this.isLoading=!0,this.error=null,this.logsService.getActivityLogs(this.currentFilter).subscribe({next:e=>{this.logs=e,this.isLoading=!1},error:e=>{console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed log\u016f DIS API",e),this.error="Nepoda\u0159ilo se na\u010d\xedst logy DIS API",this.isLoading=!1}})}onFilterChange(e){this.currentFilter=e,this.loadLogs()}showLogDetail(e){this.selectedLog=e,this.modalService.open("apiLogDetailModal")}closeLogDetail(){this.selectedLog=null,this.modalService.close("apiLogDetailModal")}getLocalizedActivityType(e){return f.getLocalizedName(e)}getActivityTypeClass(e){switch(e){case"Login":return"bg-success";case"Logout":default:return"bg-secondary";case"Create":case"ApiAccess":return"bg-primary";case"Update":case"PasswordChange":return"bg-info";case"Delete":return"bg-danger";case"Export":case"Import":return"bg-warning"}}getHttpMethodClass(e){switch(e.toUpperCase()){case"GET":return"bg-success";case"POST":return"bg-primary";case"PUT":return"bg-info";case"DELETE":return"bg-danger";case"PATCH":return"bg-warning";default:return"bg-secondary"}}getStatusCodeClass(e){return null==e?"bg-secondary":e>=500?"bg-danger":e>=400?"bg-warning":e>=300?"bg-info":e>=200?"bg-success":"bg-secondary"}};let o=a;return a.\u0275fac=function(i){return new(i||a)(t.Y36(h),t.Y36(v.Z))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-api-logs"]],decls:26,vars:7,consts:[["logType","api",3,"filterChange"],[1,"table-responsive"],[1,"table","table-hover","mb-0"],[1,"table-header-override","dark-header"],[1,"dark-header-row"],[4,"ngIf"],[4,"ngFor","ngForOf"],[3,"log","modalId","title","close"],["colspan","8",1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],["colspan","8",1,"text-center","text-danger","py-4"],[1,"cursor-pointer",3,"click"],[1,"text-truncate","cursor-pointer",2,"max-width","200px",3,"click"],["title","Zobrazit detail",1,"btn","btn-sm","btn-outline-info",3,"click"],[1,"bi","bi-info-circle"]],template:function(i,r){1&i&&(t.TgZ(0,"app-log-filter",0),t.NdJ("filterChange",function(l){return r.onFilterChange(l)}),t.qZA(),t.TgZ(1,"div",1)(2,"table",2)(3,"thead",3)(4,"tr",4)(5,"th"),t._uU(6,"\u010cas"),t.qZA(),t.TgZ(7,"th"),t._uU(8,"Instance"),t.qZA(),t.TgZ(9,"th"),t._uU(10,"Entita"),t.qZA(),t.TgZ(11,"th"),t._uU(12,"Metoda"),t.qZA(),t.TgZ(13,"th"),t._uU(14,"Cesta"),t.qZA(),t.TgZ(15,"th"),t._uU(16,"K\xf3d"),t.qZA(),t.TgZ(17,"th"),t._uU(18,"Trv\xe1n\xed"),t.qZA(),t._UZ(19,"th"),t.qZA()(),t.TgZ(20,"tbody"),t.YNc(21,Nt,5,0,"tr",5),t.YNc(22,Jt,3,1,"tr",5),t.YNc(23,Et,3,0,"tr",5),t.YNc(24,$t,22,17,"tr",6),t.qZA()()(),t.TgZ(25,"app-log-detail",7),t.NdJ("close",function(){return r.closeLogDetail()}),t.qZA()),2&i&&(t.xp6(21),t.Q6J("ngIf",r.isLoading),t.xp6(1),t.Q6J("ngIf",!r.isLoading&&r.error),t.xp6(1),t.Q6J("ngIf",!r.isLoading&&!r.error&&0===r.logs.length),t.xp6(1),t.Q6J("ngForOf",r.logs),t.xp6(1),t.Q6J("log",r.selectedLog)("modalId","apiLogDetailModal")("title","Detail logu DIS API"))},dependencies:[p.sg,p.O5,Z,L,p.uU],styles:[".cursor-pointer[_ngcontent-%COMP%]{cursor:pointer}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{font-weight:500}.table-responsive[_ngcontent-%COMP%]{border-radius:6px!important;overflow:hidden!important;box-shadow:0 2px 4px #0000001a!important}.table[_ngcontent-%COMP%]{margin:0!important;border-collapse:collapse!important}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child, .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child{padding-left:1rem!important}.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child{padding-left:1rem!important;border-left:none!important}.badge[_ngcontent-%COMP%]{font-weight:500;padding:.35em .65em}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-left:none!important}.table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#007bff13!important}body.dark-theme[_ngcontent-%COMP%]   .table-hover[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#1a2530!important}.stack-trace[_ngcontent-%COMP%]{white-space:pre-wrap;font-size:.85rem;max-height:300px;overflow-y:auto}.bg-success[_ngcontent-%COMP%]{background-color:#28a745!important}.bg-primary[_ngcontent-%COMP%]{background-color:#007bff!important}.bg-info[_ngcontent-%COMP%]{background-color:#17a2b8!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important;color:#212529}.bg-danger[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-secondary[_ngcontent-%COMP%]{background-color:#6c757d!important}@media (prefers-color-scheme: dark){.table[_ngcontent-%COMP%]{color:var(--bs-light)}.bg-warning[_ngcontent-%COMP%]{color:#212529}pre[_ngcontent-%COMP%]{color:var(--bs-light)}}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:first-child{border-top-left-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:last-child{border-top-right-radius:6px!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr{background-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{background-color:var(--bs-table-bg, #111921)!important;color:#fff!important;border-color:var(--bs-table-bg, #111921)!important}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table{min-width:100%}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td>.badge{padding:.35em .65em;font-size:.875rem}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>tbody>tr>td:nth-child(5), [_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th:nth-child(5){max-width:250px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}[_nghost-%COMP%]     .card>.card-body>.table-responsive>.table>thead>tr>th{padding:.5rem;font-size:1rem}"]}),o})();function Vt(o,a){1&o&&t._UZ(0,"app-activity-logs")}function Gt(o,a){1&o&&t._UZ(0,"app-error-logs")}function Kt(o,a){1&o&&t._UZ(0,"app-api-logs")}let jt=(()=>{const a=class{constructor(e,i){this.router=e,this.route=i,this.activeTab="activity",this.tabs=[{id:"activity",label:"Auditn\xed logy",icon:"activity"},{id:"error",label:"Logy aplikace",icon:"exclamation-triangle"},{id:"api",label:"Logy DIS API",icon:"hdd-network"}],this.activityFilterState=null,this.errorFilterState=null,this.apiFilterState=null}ngOnInit(){this.route.queryParams.subscribe(e=>{if(e.tab)this.activeTab=e.tab,this.saveActiveTabToLocalStorage(e.tab);else{const i=this.loadActiveTabFromLocalStorage();i&&(this.activeTab=i,this.router.navigate([],{relativeTo:this.route,queryParams:{tab:i},queryParamsHandling:"merge"}))}})}changeTab(e){this.saveCurrentFilterState();const i="string"==typeof e?e:String(e);this.activeTab=i,this.saveActiveTabToLocalStorage(i),this.router.navigate([],{relativeTo:this.route,queryParams:{tab:i},queryParamsHandling:"merge"})}saveCurrentFilterState(){switch(this.activeTab){case"activity":this.activityFilterState=this.getFilterState("activity");break;case"error":this.errorFilterState=this.getFilterState("error");break;case"api":this.apiFilterState=this.getFilterState("api")}}getFilterState(e){try{const i=`current_filter_${e}`,r=localStorage.getItem(i);if(r)return JSON.parse(r)}catch(i){console.error(`Chyba p\u0159i na\u010d\xedt\xe1n\xed stavu filtru pro ${e}`,i)}return null}saveActiveTabToLocalStorage(e){try{localStorage.setItem("logs_active_tab",e),console.log(`Ulo\u017eena aktivn\xed z\xe1lo\u017eka do localStorage: ${e}`)}catch(i){console.error("Chyba p\u0159i ukl\xe1d\xe1n\xed aktivn\xed z\xe1lo\u017eky do localStorage:",i)}}loadActiveTabFromLocalStorage(){try{const e=localStorage.getItem("logs_active_tab");return console.log(`Na\u010dtena aktivn\xed z\xe1lo\u017eka z localStorage: ${e}`),e}catch(e){return console.error("Chyba p\u0159i na\u010d\xedt\xe1n\xed aktivn\xed z\xe1lo\u017eky z localStorage:",e),null}}};let o=a;return a.\u0275fac=function(i){return new(i||a)(t.Y36(y.F0),t.Y36(y.gz))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-logs"]],decls:9,vars:6,consts:[[1,"container"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],[3,"tabs","activeTabId","tabChange"],[3,"ngSwitch"],[4,"ngSwitchCase"]],template:function(i,r){1&i&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h2"),t._uU(3,"Logy syst\xe9mu"),t.qZA()(),t.TgZ(4,"app-tab-navigation",2),t.NdJ("tabChange",function(l){return r.changeTab(l)}),t.qZA(),t.TgZ(5,"div",3),t.YNc(6,Vt,1,0,"app-activity-logs",4),t.YNc(7,Gt,1,0,"app-error-logs",4),t.YNc(8,Kt,1,0,"app-api-logs",4),t.qZA()()),2&i&&(t.xp6(4),t.Q6J("tabs",r.tabs)("activeTabId",r.activeTab),t.xp6(1),t.Q6J("ngSwitch",r.activeTab),t.xp6(1),t.Q6J("ngSwitchCase","activity"),t.xp6(1),t.Q6J("ngSwitchCase","error"),t.xp6(1),t.Q6J("ngSwitchCase","api"))},dependencies:[p.RF,p.n9,F.j,Dt,kt,zt]}),o})();var Rt=_(4466);let Wt=(()=>{const a=class{};let o=a;return a.\u0275fac=function(i){return new(i||a)},a.\u0275mod=t.oAB({type:a}),a.\u0275inj=t.cJS({imports:[p.ez,u.u5,u.UX,Rt.m,y.Bz.forChild([{path:"",component:jt}])]}),o})()}}]);